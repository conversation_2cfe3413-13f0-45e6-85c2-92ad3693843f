# Database Configuration
# استبدل القيم التالية ببيانات قاعدة البيانات الخاصة بك
DATABASE_URL="postgresql://username:password@localhost:5432/khalihatanbaa?schema=public"

# NextAuth Configuration
# رابط الموقع (في التطوير: http://localhost:3000)
NEXTAUTH_URL="http://localhost:3000"

# مفتاح سري لـ NextAuth (يجب أن يكون عشوائي وآمن في الإنتاج)
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# Cloudinary Configuration (للمراحل القادمة)
# سجل حساب مجاني على cloudinary.com واحصل على هذه القيم
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# App Configuration
APP_NAME="خَلّيها تنْباع"
APP_DOMAIN="khalihatanbaa.com"

# Email Configuration (للمراحل القادمة)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# Payment Configuration (للمراحل القادمة)
# PAYEER_ACCOUNT="P1234567"
# PAYEER_API_ID="your-api-id"
# PAYEER_API_PASS="your-api-password"
