import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// مخطط التحقق من إضافة تقييم
const ratingSchema = z.object({
  sellerId: z.string().min(1, 'معرف البائع مطلوب'),
  rating: z.number().min(1, 'التقييم يجب أن يكون من 1 إلى 5').max(5, 'التقييم يجب أن يكون من 1 إلى 5'),
  comment: z.string().optional()
})

// جلب التقييمات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sellerId = searchParams.get('sellerId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    if (!sellerId) {
      return NextResponse.json(
        { success: false, error: 'معرف البائع مطلوب' },
        { status: 400 }
      )
    }

    // جلب التقييمات
    const ratings = await prisma.rating.findMany({
      where: {
        sellerId: sellerId
      },
      include: {
        giver: {
          select: { id: true, name: true, avatar: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    })

    // حساب العدد الإجمالي
    const total = await prisma.rating.count({
      where: {
        sellerId: sellerId
      }
    })

    // حساب متوسط التقييم
    const avgRating = await prisma.rating.aggregate({
      where: {
        sellerId: sellerId
      },
      _avg: {
        rating: true
      }
    })

    // حساب توزيع التقييمات
    const ratingDistribution = await prisma.rating.groupBy({
      by: ['rating'],
      where: {
        sellerId: sellerId
      },
      _count: {
        rating: true
      }
    })

    const distribution = [1, 2, 3, 4, 5].map(star => ({
      star,
      count: ratingDistribution.find(r => r.rating === star)?._count.rating || 0
    }))

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: {
        ratings,
        summary: {
          total,
          average: avgRating._avg.rating || 0,
          distribution
        },
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    })

  } catch (error) {
    console.error('Error fetching ratings:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب التقييمات' },
      { status: 500 }
    )
  }
}

// إضافة تقييم جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = ratingSchema.parse(body)

    // التحقق من عدم تقييم النفس
    if (validatedData.sellerId === session.user.id) {
      return NextResponse.json(
        { success: false, error: 'لا يمكنك تقييم نفسك' },
        { status: 400 }
      )
    }

    // التحقق من وجود البائع
    const seller = await prisma.user.findUnique({
      where: { id: validatedData.sellerId }
    })

    if (!seller) {
      return NextResponse.json(
        { success: false, error: 'البائع غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود تقييم سابق
    const existingRating = await prisma.rating.findUnique({
      where: {
        userId_sellerId: {
          userId: session.user.id,
          sellerId: validatedData.sellerId
        }
      }
    })

    if (existingRating) {
      return NextResponse.json(
        { success: false, error: 'لقد قمت بتقييم هذا البائع من قبل' },
        { status: 400 }
      )
    }

    // إنشاء التقييم
    const rating = await prisma.rating.create({
      data: {
        userId: session.user.id,
        sellerId: validatedData.sellerId,
        rating: validatedData.rating,
        comment: validatedData.comment
      },
      include: {
        giver: {
          select: { id: true, name: true, avatar: true }
        },
        receiver: {
          select: { id: true, name: true, avatar: true }
        }
      }
    })

    // تحديث متوسط التقييم للبائع
    const avgRating = await prisma.rating.aggregate({
      where: {
        sellerId: validatedData.sellerId
      },
      _avg: {
        rating: true
      },
      _count: {
        rating: true
      }
    })

    await prisma.user.update({
      where: { id: validatedData.sellerId },
      data: {
        ratingAverage: avgRating._avg.rating || 0,
        ratingCount: avgRating._count.rating || 0
      }
    })

    return NextResponse.json({
      success: true,
      data: rating,
      message: 'تم إضافة التقييم بنجاح'
    })

  } catch (error) {
    console.error('Error adding rating:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'حدث خطأ في إضافة التقييم' },
      { status: 500 }
    )
  }
}

// تحديث تقييم موجود
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { ratingId, rating: newRating, comment } = body

    if (!ratingId) {
      return NextResponse.json(
        { success: false, error: 'معرف التقييم مطلوب' },
        { status: 400 }
      )
    }

    // البحث عن التقييم
    const existingRating = await prisma.rating.findUnique({
      where: { id: ratingId }
    })

    if (!existingRating) {
      return NextResponse.json(
        { success: false, error: 'التقييم غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من الملكية
    if (existingRating.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'غير مسموح لك بتعديل هذا التقييم' },
        { status: 403 }
      )
    }

    // تحديث التقييم
    const updatedRating = await prisma.rating.update({
      where: { id: ratingId },
      data: {
        ...(newRating && { rating: newRating }),
        ...(comment !== undefined && { comment })
      },
      include: {
        giver: {
          select: { id: true, name: true, avatar: true }
        },
        receiver: {
          select: { id: true, name: true, avatar: true }
        }
      }
    })

    // إعادة حساب متوسط التقييم للبائع
    const avgRating = await prisma.rating.aggregate({
      where: {
        sellerId: existingRating.sellerId
      },
      _avg: {
        rating: true
      },
      _count: {
        rating: true
      }
    })

    await prisma.user.update({
      where: { id: existingRating.sellerId },
      data: {
        ratingAverage: avgRating._avg.rating || 0,
        ratingCount: avgRating._count.rating || 0
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedRating,
      message: 'تم تحديث التقييم بنجاح'
    })

  } catch (error) {
    console.error('Error updating rating:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تحديث التقييم' },
      { status: 500 }
    )
  }
}
