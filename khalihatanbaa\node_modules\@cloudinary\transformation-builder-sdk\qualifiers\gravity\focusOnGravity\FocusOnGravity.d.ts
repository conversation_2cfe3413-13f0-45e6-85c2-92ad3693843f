import { FocusOnValue } from "../../focusOn.js";
import { GravityQualifier } from "../GravityQualifier.js";
import { AutoGravity } from "../autoGravity/AutoGravity.js";
/**
 * @description The class for the FocusOn builder
 * @memberOf Qualifiers.Gravity
 * @extends {Qualifiers.Gravity.GravityQualifier}
 */
declare class FocusOnGravity extends GravityQualifier {
    constructor(FocusOnObjects: FocusOnValue[]);
    /**
     * @description Specifies the gravity to use if none of the other gravity objects are found.
     * @param {Qualifiers.Gravity.AutoGravity} val
     */
    fallbackGravity(val: AutoGravity): this;
}
export { FocusOnGravity };
