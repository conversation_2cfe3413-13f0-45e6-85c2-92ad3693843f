import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// مخطط التحقق من إرسال رسالة
const sendMessageSchema = z.object({
  toId: z.string().min(1, "معرف المستقبل مطلوب"),
  adId: z.string().optional(),
  content: z
    .string()
    .min(1, "محتوى الرسالة مطلوب")
    .max(1000, "الرسالة طويلة جداً"),
});

// جلب المحادثات
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const conversationWith = searchParams.get("with");
    const adId = searchParams.get("adId");

    if (conversationWith) {
      // جلب محادثة محددة
      const messages = await prisma.message.findMany({
        where: {
          OR: [
            { fromId: session.user.id, toId: conversationWith },
            { fromId: conversationWith, toId: session.user.id },
          ],
          ...(adId && { adId }),
        },
        include: {
          from: {
            select: { id: true, name: true, avatar: true },
          },
          to: {
            select: { id: true, name: true, avatar: true },
          },
          ad: {
            select: { id: true, title: true, price: true, imageUrls: true },
          },
        },
        orderBy: { createdAt: "asc" },
      });

      // تحديد الرسائل كمقروءة
      await prisma.message.updateMany({
        where: {
          fromId: conversationWith,
          toId: session.user.id,
          isRead: false,
        },
        data: { isRead: true },
      });

      return NextResponse.json({
        success: true,
        data: messages,
      });
    } else {
      // جلب قائمة المحادثات - مع حد للنتائج لتجنب تجاوز 5MB
      const conversations = await prisma.message.findMany({
        where: {
          OR: [{ fromId: session.user.id }, { toId: session.user.id }],
        },
        include: {
          from: {
            select: { id: true, name: true, avatar: true },
          },
          to: {
            select: { id: true, name: true, avatar: true },
          },
          ad: {
            select: { id: true, title: true, price: true, imageUrls: true },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 10, // حد أقصى 10 رسائل لتجنب تجاوز حد الحجم
      });

      // تجميع المحادثات حسب المستخدم والإعلان
      const groupedConversations = conversations.reduce((acc, message) => {
        const otherUserId =
          message.fromId === session.user.id ? message.toId : message.fromId;
        const key = `${otherUserId}-${message.adId || "general"}`;

        if (!acc[key] || acc[key].createdAt < message.createdAt) {
          acc[key] = {
            ...message,
            otherUser:
              message.fromId === session.user.id ? message.to : message.from,
            unreadCount: 0,
          };
        }

        return acc;
      }, {} as any);

      // حساب عدد الرسائل غير المقروءة لكل محادثة
      for (const conversation of Object.values(groupedConversations) as any[]) {
        const unreadCount = await prisma.message.count({
          where: {
            fromId: conversation.otherUser.id,
            toId: session.user.id,
            isRead: false,
            ...(conversation.adId && { adId: conversation.adId }),
          },
        });
        conversation.unreadCount = unreadCount;
      }

      return NextResponse.json({
        success: true,
        data: Object.values(groupedConversations),
      });
    }
  } catch (error) {
    console.error("Error fetching messages:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في جلب الرسائل" },
      { status: 500 }
    );
  }
}

// إرسال رسالة جديدة
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = sendMessageSchema.parse(body);

    // التحقق من وجود المستقبل
    const recipient = await prisma.user.findUnique({
      where: { id: validatedData.toId },
    });

    if (!recipient) {
      return NextResponse.json(
        { success: false, error: "المستخدم المستقبل غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود الإعلان إذا تم تحديده
    if (validatedData.adId) {
      const ad = await prisma.ad.findUnique({
        where: { id: validatedData.adId },
      });

      if (!ad) {
        return NextResponse.json(
          { success: false, error: "الإعلان غير موجود" },
          { status: 404 }
        );
      }
    }

    // إنشاء الرسالة
    const message = await prisma.message.create({
      data: {
        fromId: session.user.id,
        toId: validatedData.toId,
        adId: validatedData.adId,
        content: validatedData.content,
      },
      include: {
        from: {
          select: { id: true, name: true, avatar: true },
        },
        to: {
          select: { id: true, name: true, avatar: true },
        },
        ad: {
          select: { id: true, title: true, price: true, imageUrls: true },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: message,
      message: "تم إرسال الرسالة بنجاح",
    });
  } catch (error) {
    console.error("Error sending message:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "حدث خطأ في إرسال الرسالة" },
      { status: 500 }
    );
  }
}
