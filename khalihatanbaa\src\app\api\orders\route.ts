import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// إنشاء طلب جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { adId, notes } = await request.json()

    if (!adId) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان مطلوب' },
        { status: 400 }
      )
    }

    // جلب بيانات الإعلان
    const ad = await prisma.ad.findUnique({
      where: { id: adId },
      include: { user: true }
    })

    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      )
    }

    if (ad.userId === session.user.id) {
      return NextResponse.json(
        { success: false, error: 'لا يمكنك طلب إعلانك الخاص' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود طلب سابق
    const existingOrder = await prisma.order.findUnique({
      where: {
        buyerId_adId: {
          buyerId: session.user.id,
          adId: adId
        }
      }
    })

    if (existingOrder) {
      return NextResponse.json(
        { success: false, error: 'لديك طلب سابق لهذا الإعلان' },
        { status: 400 }
      )
    }

    // إنشاء الطلب
    const order = await prisma.order.create({
      data: {
        buyerId: session.user.id,
        sellerId: ad.userId,
        adId: adId,
        totalPrice: ad.price,
        notes: notes || null,
        status: 'pending'
      },
      include: {
        buyer: {
          select: { id: true, name: true, email: true }
        },
        seller: {
          select: { id: true, name: true, email: true }
        },
        ad: {
          select: { id: true, title: true, price: true }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: order
    })

  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في إنشاء الطلب' },
      { status: 500 }
    )
  }
}

// تحديث حالة الطلب
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { orderId, status } = await request.json()

    if (!orderId || !status) {
      return NextResponse.json(
        { success: false, error: 'معرف الطلب والحالة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من صحة الحالة
    const validStatuses = ['pending', 'confirmed', 'completed', 'cancelled']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: 'حالة غير صحيحة' },
        { status: 400 }
      )
    }

    // جلب الطلب والتحقق من الصلاحية
    const order = await prisma.order.findUnique({
      where: { id: orderId }
    })

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'الطلب غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من أن المستخدم هو البائع أو المشتري
    if (order.sellerId !== session.user.id && order.buyerId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح لك بتعديل هذا الطلب' },
        { status: 403 }
      )
    }

    // تحديث الطلب
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        status: status,
        ...(status === 'completed' && { completedAt: new Date() })
      },
      include: {
        buyer: {
          select: { id: true, name: true, email: true }
        },
        seller: {
          select: { id: true, name: true, email: true }
        },
        ad: {
          select: { id: true, title: true, price: true }
        }
      }
    })

    // إذا تم إكمال الطلب، تحديث حالة الإعلان
    if (status === 'completed') {
      await prisma.ad.update({
        where: { id: order.adId },
        data: { status: 'sold' }
      })
    }

    return NextResponse.json({
      success: true,
      data: updatedOrder
    })

  } catch (error) {
    console.error('Error updating order:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تحديث الطلب' },
      { status: 500 }
    )
  }
}
