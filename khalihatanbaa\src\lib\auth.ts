import { NextAuthOptions } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import { prisma } from "./prisma";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        emailOrPhone: {
          label: "البريد الإلكتروني أو رقم الهاتف",
          type: "text",
        },
        password: { label: "كلمة المرور", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.emailOrPhone || !credentials?.password) {
          return null;
        }

        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف
        const user = await prisma.user.findFirst({
          where: {
            OR: [
              { email: credentials.emailOrPhone },
              { phone: credentials.emailOrPhone },
            ],
            isActive: true,
          },
        });

        if (!user) {
          return null;
        }

        // التحقق من كلمة المرور
        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.passwordHash
        );

        if (!isPasswordValid) {
          return null;
        }

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          role: user.role,
          avatar: user.avatar || undefined,
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.phone = user.phone;
        token.avatar = user.avatar;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.phone = token.phone as string;
        session.user.avatar = token.avatar as string;
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
    // signUp: "/auth/register", // غير مدعوم في NextAuth
  },
};
