import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sellerId = searchParams.get('sellerId')
    const adId = searchParams.get('adId')

    if (!sellerId) {
      return NextResponse.json(
        { success: false, error: 'معرف البائع مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود طلب مكتمل بين المستخدم والبائع
    const completedOrder = await prisma.order.findFirst({
      where: {
        buyerId: session.user.id,
        sellerId: sellerId,
        status: 'completed',
        ...(adId && { adId: adId })
      },
      include: {
        ad: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      hasCompletedOrder: !!completedOrder,
      order: completedOrder ? {
        id: completedOrder.id,
        adTitle: completedOrder.ad.title,
        completedAt: completedOrder.completedAt
      } : null
    })

  } catch (error) {
    console.error('Error checking completed order:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في التحقق من الطلبات المكتملة' },
      { status: 500 }
    )
  }
}
