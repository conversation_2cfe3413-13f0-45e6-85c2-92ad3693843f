{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useSession, signOut } from 'next-auth/react'\nimport { \n  MagnifyingGlassIcon, \n  PlusIcon, \n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline'\n\nexport function Header() {\n  const { data: session } = useSession()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [searchQuery, setSearchQuery] = useState('')\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`\n    }\n  }\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">خَلّيها تنْباع</h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">{session.user.name}</span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAeO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,6LAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAoB,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAGtD,4BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,6LAAC;4DAAG,WAAU;;;;;;sEACd,6LAAC;4DACC,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,6LAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAtLgB;;QACY,iJAAA,CAAA,aAAU;;;KADtB", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/types/index.ts"], "sourcesContent": ["// أنواع البيانات الأساسية للتطبيق\n\nexport interface User {\n  id: string\n  name: string\n  email: string\n  phone: string\n  role: 'user' | 'admin'\n  avatar?: string\n  isActive: boolean\n  freeAdsCount: number\n  freeAdsExpiresAt?: Date\n  paidAdsCount: number\n  paidAdsExpiresAt?: Date\n  ratingAverage: number\n  ratingCount: number\n  createdAt: Date\n  updatedAt: Date\n}\n\nexport interface Ad {\n  id: string\n  title: string\n  description: string\n  price: number\n  category: string\n  subCategory?: string\n  condition: 'جديد' | 'مستعمل'\n  city: string\n  region?: string\n  addressDetail?: string\n  imageUrls: string[]\n  specifications?: Record<string, any>\n  views: number\n  isActive: boolean\n  isFreeAd: boolean\n  isPromoted: boolean\n  expiresAt?: Date\n  createdAt: Date\n  updatedAt: Date\n  userId: string\n  user?: User\n}\n\nexport interface Message {\n  id: string\n  content: string\n  isRead: boolean\n  createdAt: Date\n  fromId: string\n  toId: string\n  adId?: string\n  from?: User\n  to?: User\n  ad?: Ad\n}\n\nexport interface Favorite {\n  id: string\n  userId: string\n  adId: string\n  createdAt: Date\n  user?: User\n  ad?: Ad\n}\n\nexport interface Rating {\n  id: string\n  rating: number\n  comment?: string\n  createdAt: Date\n  userId: string\n  sellerId: string\n  giver?: User\n  receiver?: User\n}\n\nexport interface AdPackage {\n  id: string\n  name: string\n  adsCount: number\n  price: number\n  duration: number\n  isActive: boolean\n  createdAt: Date\n  userId: string\n  user?: User\n}\n\n// أنواع الفئات\nexport const CATEGORIES = {\n  'عقارات': {\n    name: 'عقارات',\n    icon: '🏠',\n    subCategories: ['شقة', 'فيلا', 'أرض', 'محل تجاري', 'مكتب']\n  },\n  'سيارات': {\n    name: 'سيارات',\n    icon: '🚗',\n    subCategories: ['تويوتا', 'نيسان', 'هيونداي', 'كيا', 'مرسيدس', 'BMW', 'أخرى']\n  },\n  'إلكترونيات': {\n    name: 'إلكترونيات',\n    icon: '📱',\n    subCategories: ['هاتف ذكي', 'لابتوب', 'تلفاز', 'كاميرا', 'أجهزة منزلية']\n  },\n  'أثاث': {\n    name: 'أثاث',\n    icon: '🪑',\n    subCategories: ['غرفة نوم', 'غرفة جلوس', 'مطبخ', 'مكتب', 'ديكور']\n  },\n  'ملابس': {\n    name: 'ملابس',\n    icon: '👕',\n    subCategories: ['رجالي', 'نسائي', 'أطفال', 'أحذية', 'إكسسوارات']\n  },\n  'رياضة': {\n    name: 'رياضة',\n    icon: '⚽',\n    subCategories: ['كرة قدم', 'كرة سلة', 'جيم', 'دراجات', 'أخرى']\n  }\n} as const\n\nexport type CategoryKey = keyof typeof CATEGORIES\n\n// أنواع المدن السورية\nexport const SYRIAN_CITIES = [\n  'دمشق',\n  'حلب',\n  'حمص',\n  'حماة',\n  'اللاذقية',\n  'طرطوس',\n  'درعا',\n  'السويداء',\n  'القنيطرة',\n  'دير الزور',\n  'الرقة',\n  'الحسكة',\n  'إدلب',\n  'ريف دمشق'\n] as const\n\nexport type SyrianCity = typeof SYRIAN_CITIES[number]\n\n// أنواع الاستعلامات\nexport interface SearchFilters {\n  keyword?: string\n  category?: CategoryKey\n  subCategory?: string\n  city?: SyrianCity\n  minPrice?: number\n  maxPrice?: number\n  condition?: 'جديد' | 'مستعمل'\n  sortBy?: 'newest' | 'oldest' | 'price_low' | 'price_high'\n}\n\nexport interface PaginationParams {\n  page: number\n  limit: number\n}\n\nexport interface ApiResponse<T> {\n  success: boolean\n  data?: T\n  message?: string\n  error?: string\n}\n\nexport interface PaginatedResponse<T> extends ApiResponse<T[]> {\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AA0F3B,MAAM,aAAa;IACxB,UAAU;QACR,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAO;YAAQ;YAAO;YAAa;SAAO;IAC5D;IACA,UAAU;QACR,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAU;YAAS;YAAW;YAAO;YAAU;YAAO;SAAO;IAC/E;IACA,cAAc;QACZ,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAU;YAAS;YAAU;SAAe;IAC1E;IACA,QAAQ;QACN,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAa;YAAQ;YAAQ;SAAQ;IACnE;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAS;YAAS;YAAS;YAAS;SAAY;IAClE;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAW;YAAW;YAAO;YAAU;SAAO;IAChE;AACF;AAKO,MAAM,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MagnifyingGlassIcon } from '@heroicons/react/24/outline'\nimport { CATEGORIES } from '@/types'\n\nexport function Hero() {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    const params = new URLSearchParams()\n    if (searchQuery.trim()) params.set('q', searchQuery)\n    if (selectedCategory) params.set('category', selectedCategory)\n    \n    window.location.href = `/search?${params.toString()}`\n  }\n\n  return (\n    <section className=\"bg-gradient-to-br from-primary-50 to-secondary-50 py-16 lg:py-24\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          {/* العنوان الرئيسي */}\n          <h1 className=\"text-4xl lg:text-6xl font-bold text-dark-800 mb-6\">\n            <span className=\"text-primary-500\">خَلّيها</span>{' '}\n            <span className=\"text-secondary-500\">تنْباع</span>\n          </h1>\n          \n          <p className=\"text-xl lg:text-2xl text-dark-600 mb-8 max-w-3xl mx-auto\">\n            منصة البيع والشراء الأولى في سوريا\n            <br />\n            <span className=\"text-lg text-dark-500\">\n              اعثر على أفضل العروض أو بع منتجاتك بسهولة\n            </span>\n          </p>\n\n          {/* شريط البحث المتقدم */}\n          <div className=\"max-w-4xl mx-auto\">\n            <form onSubmit={handleSearch} className=\"bg-white rounded-2xl shadow-lg p-6\">\n              <div className=\"flex flex-col lg:flex-row gap-4\">\n                {/* حقل البحث */}\n                <div className=\"flex-1 relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"ابحث عن أي شيء... (سيارة، شقة، هاتف، إلخ)\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  />\n                  <MagnifyingGlassIcon className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400\" />\n                </div>\n\n                {/* اختيار الفئة */}\n                <div className=\"lg:w-64\">\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"w-full px-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">جميع الفئات</option>\n                    {Object.entries(CATEGORIES).map(([key, category]) => (\n                      <option key={key} value={key}>\n                        {category.icon} {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* زر البحث */}\n                <button\n                  type=\"submit\"\n                  className=\"lg:w-32 bg-primary-500 text-white px-8 py-4 rounded-xl hover:bg-primary-600 transition-colors font-semibold text-lg\"\n                >\n                  بحث\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {/* إحصائيات سريعة */}\n          <div className=\"mt-16 grid grid-cols-2 lg:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-primary-500 mb-2\">\n                1000+\n              </div>\n              <div className=\"text-dark-600\">إعلان نشط</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-secondary-500 mb-2\">\n                500+\n              </div>\n              <div className=\"text-dark-600\">مستخدم مسجل</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-primary-500 mb-2\">\n                14\n              </div>\n              <div className=\"text-dark-600\">محافظة</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-secondary-500 mb-2\">\n                24/7\n              </div>\n              <div className=\"text-dark-600\">دعم متواصل</div>\n            </div>\n          </div>\n\n          {/* الكلمات المفتاحية الشائعة */}\n          <div className=\"mt-12\">\n            <p className=\"text-dark-600 mb-4\">البحث الشائع:</p>\n            <div className=\"flex flex-wrap justify-center gap-3\">\n              {[\n                'سيارات مستعملة',\n                'شقق للبيع',\n                'هواتف ذكية',\n                'أثاث منزلي',\n                'لابتوب',\n                'دراجات نارية'\n              ].map((keyword) => (\n                <button\n                  key={keyword}\n                  onClick={() => {\n                    setSearchQuery(keyword)\n                    handleSearch({ preventDefault: () => {} } as React.FormEvent)\n                  }}\n                  className=\"px-4 py-2 bg-white text-dark-600 rounded-full border border-gray-300 hover:border-primary-500 hover:text-primary-500 transition-colors\"\n                >\n                  {keyword}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,SAAS,IAAI;QACnB,IAAI,YAAY,IAAI,IAAI,OAAO,GAAG,CAAC,KAAK;QACxC,IAAI,kBAAkB,OAAO,GAAG,CAAC,YAAY;QAE7C,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,OAAO,QAAQ,IAAI;IACvD;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;4BAAe;0CAClD,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAE,WAAU;;4BAA2D;0CAEtE,6LAAC;;;;;0CACD,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;0DAEZ,6LAAC,wOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,OAAO,OAAO,CAAC,wHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBAC9C,6LAAC;wDAAiB,OAAO;;4DACtB,SAAS,IAAI;4DAAC;4DAAE,SAAS,IAAI;;uDADnB;;;;;;;;;;;;;;;;kDAQnB,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAuD;;;;;;kDAGtE,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAyD;;;;;;kDAGxE,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAuD;;;;;;kDAGtE,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAyD;;;;;;kDAGxE,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAKnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;oCACA;oCACA;oCACA;oCACA;oCACA;iCACD,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC;wCAEC,SAAS;4CACP,eAAe;4CACf,aAAa;gDAAE,gBAAgB,KAAO;4CAAE;wCAC1C;wCACA,WAAU;kDAET;uCAPI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBvB;GAnIgB;KAAA", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { HeartIcon, MapPinIcon, ClockIcon } from '@heroicons/react/24/outline'\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'\nimport { Ad } from '@/types'\n\n// بيانات تجريبية للإعلانات\nconst sampleAds: Ad[] = [\n  {\n    id: '1',\n    title: 'تويوتا كامري 2018 فل كامل',\n    description: 'سيارة بحالة ممتازة، صيانة دورية، لون أبيض',\n    price: 45000000,\n    category: 'سيارات',\n    subCategory: 'تويوتا',\n    condition: 'مستعمل',\n    city: 'دمشق',\n    region: 'المزة',\n    imageUrls: ['/placeholder-car.jpg'],\n    views: 125,\n    isActive: true,\n    isFreeAd: false,\n    isPromoted: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15'),\n    userId: 'user1',\n    specifications: {\n      year: 2018,\n      mileage: 85000,\n      fuel: 'بنزين',\n      color: 'أبيض'\n    }\n  },\n  {\n    id: '2',\n    title: 'شقة 3 غرف في المالكي',\n    description: 'شقة مفروشة بالكامل، إطلالة رائعة، قريبة من الخدمات',\n    price: 120000000,\n    category: 'عقارات',\n    subCategory: 'شقة',\n    condition: 'مستعمل',\n    city: 'دمشق',\n    region: 'المالكي',\n    imageUrls: ['/placeholder-apartment.jpg'],\n    views: 89,\n    isActive: true,\n    isFreeAd: true,\n    isPromoted: false,\n    createdAt: new Date('2024-01-14'),\n    updatedAt: new Date('2024-01-14'),\n    userId: 'user2',\n    specifications: {\n      rooms: 3,\n      bathrooms: 2,\n      area: 120,\n      floor: 3\n    }\n  },\n  {\n    id: '3',\n    title: 'آيفون 14 برو ماكس 256 جيجا',\n    description: 'جهاز جديد لم يستعمل، مع الكرتونة والشاحن الأصلي',\n    price: 8500000,\n    category: 'إلكترونيات',\n    subCategory: 'هاتف ذكي',\n    condition: 'جديد',\n    city: 'حلب',\n    region: 'الفرقان',\n    imageUrls: ['/placeholder-phone.jpg'],\n    views: 234,\n    isActive: true,\n    isFreeAd: false,\n    isPromoted: true,\n    createdAt: new Date('2024-01-13'),\n    updatedAt: new Date('2024-01-13'),\n    userId: 'user3',\n    specifications: {\n      brand: 'Apple',\n      model: 'iPhone 14 Pro Max',\n      storage: '256GB',\n      color: 'أسود'\n    }\n  },\n  {\n    id: '4',\n    title: 'طقم غرفة نوم خشب زان',\n    description: 'طقم كامل: سرير + دولاب + تسريحة، خشب زان طبيعي',\n    price: 15000000,\n    category: 'أثاث',\n    subCategory: 'غرفة نوم',\n    condition: 'مستعمل',\n    city: 'حمص',\n    region: 'الوعر',\n    imageUrls: ['/placeholder-furniture.jpg'],\n    views: 67,\n    isActive: true,\n    isFreeAd: true,\n    isPromoted: false,\n    createdAt: new Date('2024-01-12'),\n    updatedAt: new Date('2024-01-12'),\n    userId: 'user4',\n    specifications: {\n      material: 'خشب زان',\n      pieces: 3,\n      condition: 'ممتاز'\n    }\n  }\n]\n\nexport function FeaturedAds() {\n  const [ads, setAds] = useState<Ad[]>([])\n  const [favorites, setFavorites] = useState<Set<string>>(new Set())\n\n  useEffect(() => {\n    // في التطبيق الحقيقي، سنجلب البيانات من API\n    setAds(sampleAds)\n  }, [])\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('ar-SY').format(price)\n  }\n\n  const formatDate = (date: Date) => {\n    const now = new Date()\n    const diffTime = Math.abs(now.getTime() - date.getTime())\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n    \n    if (diffDays === 1) return 'منذ يوم واحد'\n    if (diffDays < 7) return `منذ ${diffDays} أيام`\n    if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`\n    return `منذ ${Math.ceil(diffDays / 30)} شهر`\n  }\n\n  const toggleFavorite = (adId: string) => {\n    setFavorites(prev => {\n      const newFavorites = new Set(prev)\n      if (newFavorites.has(adId)) {\n        newFavorites.delete(adId)\n      } else {\n        newFavorites.add(adId)\n      }\n      return newFavorites\n    })\n  }\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-dark-800 mb-4\">\n            أحدث الإعلانات\n          </h2>\n          <p className=\"text-lg text-dark-600 max-w-2xl mx-auto\">\n            اكتشف أحدث العروض والمنتجات المتاحة في جميع أنحاء سوريا\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {ads.map((ad) => (\n            <div\n              key={ad.id}\n              className=\"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300\"\n            >\n              {/* صورة الإعلان */}\n              <div className=\"relative h-48 bg-gray-200\">\n                <Image\n                  src={ad.imageUrls[0] || '/placeholder-image.jpg'}\n                  alt={ad.title}\n                  fill\n                  className=\"object-cover\"\n                />\n                \n                {/* شارة الإعلان المميز */}\n                {ad.isPromoted && (\n                  <div className=\"absolute top-3 right-3 bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-semibold\">\n                    مميز\n                  </div>\n                )}\n\n                {/* زر المفضلة */}\n                <button\n                  onClick={() => toggleFavorite(ad.id)}\n                  className=\"absolute top-3 left-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors\"\n                >\n                  {favorites.has(ad.id) ? (\n                    <HeartSolidIcon className=\"h-5 w-5 text-red-500\" />\n                  ) : (\n                    <HeartIcon className=\"h-5 w-5 text-gray-600\" />\n                  )}\n                </button>\n              </div>\n\n              {/* محتوى الإعلان */}\n              <div className=\"p-4\">\n                <Link href={`/ads/${ad.id}`}>\n                  <h3 className=\"font-semibold text-dark-800 mb-2 hover:text-primary-500 transition-colors line-clamp-2\">\n                    {ad.title}\n                  </h3>\n                </Link>\n\n                <div className=\"flex items-center text-sm text-dark-500 mb-2\">\n                  <MapPinIcon className=\"h-4 w-4 ml-1\" />\n                  {ad.city} - {ad.region}\n                </div>\n\n                <div className=\"flex items-center text-sm text-dark-500 mb-3\">\n                  <ClockIcon className=\"h-4 w-4 ml-1\" />\n                  {formatDate(ad.createdAt)}\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-lg font-bold text-primary-500\">\n                    {formatPrice(ad.price)} <span className=\"text-sm\">ل.س</span>\n                  </div>\n                  <div className=\"text-sm text-dark-500\">\n                    {ad.views} مشاهدة\n                  </div>\n                </div>\n\n                <div className=\"mt-3 flex items-center justify-between\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    ad.condition === 'جديد' \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-blue-100 text-blue-800'\n                  }`}>\n                    {ad.condition}\n                  </span>\n                  \n                  {ad.isFreeAd && (\n                    <span className=\"px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium\">\n                      إعلان مجاني\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* رابط عرض المزيد */}\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/ads\"\n            className=\"inline-flex items-center px-8 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors font-semibold\"\n          >\n            عرض جميع الإعلانات\n            <svg className=\"mr-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AASA,2BAA2B;AAC3B,MAAM,YAAkB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAAuB;QACnC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,MAAM;YACN,SAAS;YACT,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAA6B;QACzC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,OAAO;YACP,WAAW;YACX,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAAyB;QACrC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,OAAO;YACP,OAAO;YACP,SAAS;YACT,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAA6B;QACzC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,WAAW;QACb;IACF;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAE5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,4CAA4C;YAC5C,OAAO;QACT;gCAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO;QACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE,SAAS,KAAK,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QACjE,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;IAC9C;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA;YACX,MAAM,eAAe,IAAI,IAAI;YAC7B,IAAI,aAAa,GAAG,CAAC,OAAO;gBAC1B,aAAa,MAAM,CAAC;YACtB,OAAO;gBACL,aAAa,GAAG,CAAC;YACnB;YACA,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,IAAI,GAAG,CAAC,CAAC,mBACR,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,GAAG,SAAS,CAAC,EAAE,IAAI;4CACxB,KAAK,GAAG,KAAK;4CACb,IAAI;4CACJ,WAAU;;;;;;wCAIX,GAAG,UAAU,kBACZ,6LAAC;4CAAI,WAAU;sDAAgG;;;;;;sDAMjH,6LAAC;4CACC,SAAS,IAAM,eAAe,GAAG,EAAE;4CACnC,WAAU;sDAET,UAAU,GAAG,CAAC,GAAG,EAAE,kBAClB,6LAAC,kNAAA,CAAA,YAAc;gDAAC,WAAU;;;;;qEAE1B,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAM3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;sDACzB,cAAA,6LAAC;gDAAG,WAAU;0DACX,GAAG,KAAK;;;;;;;;;;;sDAIb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,sNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDACrB,GAAG,IAAI;gDAAC;gDAAI,GAAG,MAAM;;;;;;;sDAGxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDACpB,WAAW,GAAG,SAAS;;;;;;;sDAG1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,YAAY,GAAG,KAAK;wDAAE;sEAAC,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAEpD,6LAAC;oDAAI,WAAU;;wDACZ,GAAG,KAAK;wDAAC;;;;;;;;;;;;;sDAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,GAAG,SAAS,KAAK,SACb,gCACA,6BACJ;8DACC,GAAG,SAAS;;;;;;gDAGd,GAAG,QAAQ,kBACV,6LAAC;oDAAK,WAAU;8DAA2E;;;;;;;;;;;;;;;;;;;2BArE5F,GAAG,EAAE;;;;;;;;;;8BAgFhB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;4BACX;0CAEC,6LAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACtE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;GAjJgB;KAAA", "debugId": null}}]}