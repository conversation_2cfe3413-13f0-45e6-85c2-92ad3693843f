import { QualifierValue } from "../../internal/qualifier/QualifierValue.js";
/**
 * @description Acts as a marker for inputs passed into Rotate.mode()
 * @memberOf Qualifiers.RotationMode
 * @extends SDK.QualifierValue
 */
declare class RotationModeQualifierValue extends QualifierValue {
    private readonly val;
    constructor(val: string);
    toString(): string;
}
export { RotationModeQualifierValue };
