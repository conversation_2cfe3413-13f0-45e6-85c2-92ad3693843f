/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/messages/route";
exports.ids = ["app/api/messages/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmessages%2Froute&page=%2Fapi%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmessages%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmessages%2Froute&page=%2Fapi%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmessages%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_messages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/messages/route.ts */ \"(rsc)/./src/app/api/messages/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/messages/route\",\n        pathname: \"/api/messages\",\n        filename: \"route\",\n        bundlePath: \"app/api/messages/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\api\\\\messages\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_messages_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZtZXNzYWdlcyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGbWVzc2FnZXMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZtZXNzYWdlcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNXSU4xMCU1Q0Rlc2t0b3AlNUNrYWxpaGElNUNraGFsaWhhdGFuYmFhJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNXSU4xMCU1Q0Rlc2t0b3AlNUNrYWxpaGElNUNraGFsaWhhdGFuYmFhJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNtQztBQUNoSDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcV0lOMTBcXFxcRGVza3RvcFxcXFxrYWxpaGFcXFxca2hhbGloYXRhbmJhYVxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxtZXNzYWdlc1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbWVzc2FnZXMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9tZXNzYWdlc1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvbWVzc2FnZXMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxXSU4xMFxcXFxEZXNrdG9wXFxcXGthbGloYVxcXFxraGFsaWhhdGFuYmFhXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXG1lc3NhZ2VzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmessages%2Froute&page=%2Fapi%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmessages%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/messages/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/messages/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n\n// مخطط التحقق من إرسال رسالة\nconst sendMessageSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    toId: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, 'معرف المستقبل مطلوب'),\n    adId: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    content: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, 'محتوى الرسالة مطلوب').max(1000, 'الرسالة طويلة جداً')\n});\n// جلب المحادثات\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يجب تسجيل الدخول أولاً'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const conversationWith = searchParams.get('with');\n        const adId = searchParams.get('adId');\n        if (conversationWith) {\n            // جلب محادثة محددة\n            const messages = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.message.findMany({\n                where: {\n                    OR: [\n                        {\n                            fromId: session.user.id,\n                            toId: conversationWith\n                        },\n                        {\n                            fromId: conversationWith,\n                            toId: session.user.id\n                        }\n                    ],\n                    ...adId && {\n                        adId\n                    }\n                },\n                include: {\n                    from: {\n                        select: {\n                            id: true,\n                            name: true,\n                            avatar: true\n                        }\n                    },\n                    to: {\n                        select: {\n                            id: true,\n                            name: true,\n                            avatar: true\n                        }\n                    },\n                    ad: {\n                        select: {\n                            id: true,\n                            title: true,\n                            price: true,\n                            imageUrls: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: 'asc'\n                }\n            });\n            // تحديد الرسائل كمقروءة\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.message.updateMany({\n                where: {\n                    fromId: conversationWith,\n                    toId: session.user.id,\n                    isRead: false\n                },\n                data: {\n                    isRead: true\n                }\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: messages\n            });\n        } else {\n            // جلب قائمة المحادثات\n            const conversations = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.message.findMany({\n                where: {\n                    OR: [\n                        {\n                            fromId: session.user.id\n                        },\n                        {\n                            toId: session.user.id\n                        }\n                    ]\n                },\n                include: {\n                    from: {\n                        select: {\n                            id: true,\n                            name: true,\n                            avatar: true\n                        }\n                    },\n                    to: {\n                        select: {\n                            id: true,\n                            name: true,\n                            avatar: true\n                        }\n                    },\n                    ad: {\n                        select: {\n                            id: true,\n                            title: true,\n                            price: true,\n                            imageUrls: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: 'desc'\n                }\n            });\n            // تجميع المحادثات حسب المستخدم والإعلان\n            const groupedConversations = conversations.reduce((acc, message)=>{\n                const otherUserId = message.fromId === session.user.id ? message.toId : message.fromId;\n                const key = `${otherUserId}-${message.adId || 'general'}`;\n                if (!acc[key] || acc[key].createdAt < message.createdAt) {\n                    acc[key] = {\n                        ...message,\n                        otherUser: message.fromId === session.user.id ? message.to : message.from,\n                        unreadCount: 0\n                    };\n                }\n                return acc;\n            }, {});\n            // حساب عدد الرسائل غير المقروءة لكل محادثة\n            for (const conversation of Object.values(groupedConversations)){\n                const unreadCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.message.count({\n                    where: {\n                        fromId: conversation.otherUser.id,\n                        toId: session.user.id,\n                        isRead: false,\n                        ...conversation.adId && {\n                            adId: conversation.adId\n                        }\n                    }\n                });\n                conversation.unreadCount = unreadCount;\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: Object.values(groupedConversations)\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching messages:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في جلب الرسائل'\n        }, {\n            status: 500\n        });\n    }\n}\n// إرسال رسالة جديدة\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يجب تسجيل الدخول أولاً'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = sendMessageSchema.parse(body);\n        // التحقق من وجود المستقبل\n        const recipient = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: validatedData.toId\n            }\n        });\n        if (!recipient) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم المستقبل غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من وجود الإعلان إذا تم تحديده\n        if (validatedData.adId) {\n            const ad = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.ad.findUnique({\n                where: {\n                    id: validatedData.adId\n                }\n            });\n            if (!ad) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'الإعلان غير موجود'\n                }, {\n                    status: 404\n                });\n            }\n        }\n        // إنشاء الرسالة\n        const message = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.message.create({\n            data: {\n                fromId: session.user.id,\n                toId: validatedData.toId,\n                adId: validatedData.adId,\n                content: validatedData.content\n            },\n            include: {\n                from: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                },\n                to: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                },\n                ad: {\n                    select: {\n                        id: true,\n                        title: true,\n                        price: true,\n                        imageUrls: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: message,\n            message: 'تم إرسال الرسالة بنجاح'\n        });\n    } catch (error) {\n        console.error('Error sending message:', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_4__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: error.errors[0].message\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في إرسال الرسالة'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/messages/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                emailOrPhone: {\n                    label: \"البريد الإلكتروني أو رقم الهاتف\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"كلمة المرور\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.emailOrPhone || !credentials?.password) {\n                    return null;\n                }\n                // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findFirst({\n                    where: {\n                        OR: [\n                            {\n                                email: credentials.emailOrPhone\n                            },\n                            {\n                                phone: credentials.emailOrPhone\n                            }\n                        ],\n                        isActive: true\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                // التحقق من كلمة المرور\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(credentials.password, user.passwordHash);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    name: user.name,\n                    email: user.email,\n                    phone: user.phone,\n                    role: user.role,\n                    avatar: user.avatar || undefined\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.phone = user.phone;\n                token.avatar = user.avatar;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.phone = token.phone;\n                session.user.avatar = token.avatar;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO0tBQVE7QUFDaEIsR0FBRTtBQUVKLElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknXSxcbiAgfSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmessages%2Froute&page=%2Fapi%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmessages%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();