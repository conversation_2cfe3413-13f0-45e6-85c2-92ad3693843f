import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sellerId = searchParams.get('sellerId')

    if (!sellerId) {
      return NextResponse.json(
        { success: false, error: 'معرف البائع مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود رسائل سابقة بين المستخدم والبائع
    const messageExists = await prisma.message.findFirst({
      where: {
        OR: [
          {
            fromId: session.user.id,
            toId: sellerId
          },
          {
            fromId: sellerId,
            toId: session.user.id
          }
        ]
      }
    })

    return NextResponse.json({
      success: true,
      hasContacted: !!messageExists
    })

  } catch (error) {
    console.error('Error checking contact history:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في التحقق من تاريخ التواصل' },
      { status: 500 }
    )
  }
}
