import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'all' // 'active', 'expired', 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    const now = new Date()
    let where: any = {
      userId: session.user.id
    }

    // فلترة حسب الحالة
    switch (status) {
      case 'active':
        where.isActive = true
        where.OR = [
          { expiresAt: null },
          { expiresAt: { gte: now } }
        ]
        break
      case 'expired':
        where.OR = [
          { isActive: false },
          { expiresAt: { lt: now } }
        ]
        break
      // 'all' - لا نضيف فلترة إضافية
    }

    const [ads, total] = await Promise.all([
      prisma.ad.findMany({
        where,
        include: {
          _count: {
            select: {
              favorites: true,
              messages: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.ad.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: ads,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    })

  } catch (error) {
    console.error('Error fetching user ads:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب الإعلانات' },
      { status: 500 }
    )
  }
}
