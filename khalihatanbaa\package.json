{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.8.2", "@types/bcryptjs": "^2.4.6", "@types/react-slick": "^0.23.13", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "next": "15.3.3", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-slick": "^0.30.3", "zod": "^3.25.48"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}