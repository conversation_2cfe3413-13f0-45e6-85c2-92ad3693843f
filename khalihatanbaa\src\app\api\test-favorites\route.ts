import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    // جلب أول إعلان متاح
    const firstAd = await prisma.ad.findFirst()
    
    if (!firstAd) {
      return NextResponse.json(
        { success: false, error: 'لا توجد إعلانات متاحة' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود المفضلة مسبقاً
    const existingFavorite = await prisma.favorite.findUnique({
      where: {
        userId_adId: {
          userId: session.user.id,
          adId: firstAd.id
        }
      }
    })

    if (existingFavorite) {
      return NextResponse.json(
        { success: false, error: 'الإعلان موجود في المفضلة بالفعل' },
        { status: 400 }
      )
    }

    // إضافة إلى المفضلة
    const favorite = await prisma.favorite.create({
      data: {
        userId: session.user.id,
        adId: firstAd.id
      },
      include: {
        ad: {
          include: {
            user: {
              select: { id: true, name: true, avatar: true }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: favorite,
      message: 'تم إضافة الإعلان إلى المفضلة'
    })

  } catch (error) {
    console.error('Error adding test favorite:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في إضافة المفضلة' },
      { status: 500 }
    )
  }
}
