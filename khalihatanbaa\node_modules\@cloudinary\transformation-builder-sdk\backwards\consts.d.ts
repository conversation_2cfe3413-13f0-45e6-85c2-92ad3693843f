export declare const LEGACY_CONDITIONAL_OPERATORS: {
    "=": string;
    "!=": string;
    "<": string;
    ">": string;
    "<=": string;
    ">=": string;
    "&&": string;
    "||": string;
    "*": string;
    "/": string;
    "+": string;
    "-": string;
    "^": string;
};
export declare const CF_SHARED_CDN = "d3jpl91pxevbkh.cloudfront.net";
export declare const OLD_AKAMAI_SHARED_CDN = "cloudinary-a.akamaihd.net";
export declare const AKAMAI_SHARED_CDN = "res.cloudinary.com";
export declare const SHARED_CDN = "res.cloudinary.com";
export declare const LEGACY_PREDEFINED_VARS: {
    aspect_ratio: string;
    aspectRatio: string;
    current_page: string;
    currentPage: string;
    duration: string;
    face_count: string;
    faceCount: string;
    height: string;
    initial_aspect_ratio: string;
    initial_height: string;
    initial_width: string;
    initialAspectRatio: string;
    initialHeight: string;
    initialWidth: string;
    initial_duration: string;
    initialDuration: string;
    page_count: string;
    page_x: string;
    page_y: string;
    pageCount: string;
    pageX: string;
    pageY: string;
    tags: string;
    width: string;
};
export declare const NUMBER_PATTERN = "([0-9]*)\\.([0-9]+)|([0-9]+)";
export declare const OFFSET_ANY_PATTERN: string;
export declare const RANGE_VALUE_RE: RegExp;
export declare const OFFSET_ANY_PATTERN_RE: RegExp;
export declare const LAYER_KEYWORD_PARAMS: {
    font_weight: string;
    font_style: string;
    text_decoration: string;
    text_align: string;
    stroke: string;
};
