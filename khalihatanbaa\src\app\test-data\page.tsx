"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export default function TestDataPage() {
  const { data: session } = useSession();
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // جلب المفضلة
      const favResponse = await fetch("/api/favorites");
      const favData = await favResponse.json();

      // جلب الرسائل
      const msgResponse = await fetch("/api/messages");
      const msgData = await msgResponse.json();

      setData({
        session,
        favorites: favData,
        messages: msgData,
      });
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const addTestFavorite = async () => {
    try {
      const response = await fetch("/api/test-favorites", {
        method: "POST",
      });
      const result = await response.json();

      if (result.success) {
        alert("تم إضافة مفضلة تجريبية!");
        fetchData(); // إعادة جلب البيانات
      } else {
        alert(result.error);
      }
    } catch (error) {
      console.error("Error adding test favorite:", error);
      alert("حدث خطأ");
    }
  };

  if (loading) {
    return <div className="p-8">جاري التحميل...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">اختبار البيانات</h1>

      <div className="mb-6">
        <button
          onClick={addTestFavorite}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          إضافة مفضلة تجريبية
        </button>
      </div>

      <div className="space-y-6">
        {/* معلومات الجلسة */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-lg font-semibold mb-3">معلومات الجلسة</h2>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
            {JSON.stringify(data?.session, null, 2)}
          </pre>
        </div>

        {/* المفضلة */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-lg font-semibold mb-3">المفضلة</h2>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
            {JSON.stringify(data?.favorites, null, 2)}
          </pre>
        </div>

        {/* الرسائل */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-lg font-semibold mb-3">الرسائل</h2>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
            {JSON.stringify(data?.messages, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
