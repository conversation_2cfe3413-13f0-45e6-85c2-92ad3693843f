import { BackgroundQualifier } from "./base/BackgroundQualifier.js";
export declare const DEFAULT_INTENSITY = 100;
export declare const DEFAULT_BRIGHTNESS = 0;
/**
 * @description A class for blurred background transformations.
 * @memberOf Qualifiers.Background
 * @extends {Qualifiers.Background.BackgroundQualifier}
 */
declare class BlurredBackgroundAction extends BackgroundQualifier {
    private intensityLevel;
    private brightnessLevel;
    /**
     * @description Sets the intensity of the blur.
     * @param {number} value - The intensity of the blur.
     */
    intensity(value: number): this;
    /**
     * @description Sets the brightness of the background.
     * @param {number} value - The brightness of the background.
     */
    brightness(value: number): this;
    /**
     * @description
     * Stringify the qualifier
     * BackgroundQualifiers don't have a value, but instead override the toString() function
     */
    toString(): string;
}
export default BlurredBackgroundAction;
