/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/route";
exports.ids = ["app/api/ads/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ads_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ads/route.ts */ \"(rsc)/./src/app/api/ads/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/route\",\n        pathname: \"/api/ads\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\api\\\\ads\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ads_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ads/route.ts":
/*!**********************************!*\
  !*** ./src/app/api/ads/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '12');\n        const category = searchParams.get('category');\n        const city = searchParams.get('city');\n        const condition = searchParams.get('condition');\n        const minPrice = searchParams.get('minPrice');\n        const maxPrice = searchParams.get('maxPrice');\n        const search = searchParams.get('search');\n        const sortBy = searchParams.get('sortBy') || 'newest';\n        const skip = (page - 1) * limit;\n        // بناء شروط البحث\n        const where = {\n            isActive: true,\n            OR: [\n                {\n                    expiresAt: null\n                },\n                {\n                    expiresAt: {\n                        gte: new Date()\n                    }\n                }\n            ]\n        };\n        // فلترة حسب الفئة\n        if (category) {\n            where.category = category;\n        }\n        // فلترة حسب المدينة\n        if (city) {\n            where.city = city;\n        }\n        // فلترة حسب الحالة\n        if (condition) {\n            where.condition = condition;\n        }\n        // فلترة حسب السعر\n        if (minPrice || maxPrice) {\n            where.price = {};\n            if (minPrice) where.price.gte = parseFloat(minPrice);\n            if (maxPrice) where.price.lte = parseFloat(maxPrice);\n        }\n        // البحث في العنوان والوصف\n        if (search) {\n            where.AND = [\n                where.AND || {},\n                {\n                    OR: [\n                        {\n                            title: {\n                                contains: search,\n                                mode: 'insensitive'\n                            }\n                        },\n                        {\n                            description: {\n                                contains: search,\n                                mode: 'insensitive'\n                            }\n                        }\n                    ]\n                }\n            ];\n        }\n        // ترتيب النتائج\n        let orderBy = [];\n        switch(sortBy){\n            case 'newest':\n                orderBy = [\n                    {\n                        isPromoted: 'desc'\n                    },\n                    {\n                        createdAt: 'desc'\n                    }\n                ];\n                break;\n            case 'oldest':\n                orderBy = [\n                    {\n                        isPromoted: 'desc'\n                    },\n                    {\n                        createdAt: 'asc'\n                    }\n                ];\n                break;\n            case 'price_low':\n                orderBy = [\n                    {\n                        isPromoted: 'desc'\n                    },\n                    {\n                        price: 'asc'\n                    }\n                ];\n                break;\n            case 'price_high':\n                orderBy = [\n                    {\n                        isPromoted: 'desc'\n                    },\n                    {\n                        price: 'desc'\n                    }\n                ];\n                break;\n            case 'most_viewed':\n                orderBy = [\n                    {\n                        isPromoted: 'desc'\n                    },\n                    {\n                        views: 'desc'\n                    }\n                ];\n                break;\n            default:\n                orderBy = [\n                    {\n                        isPromoted: 'desc'\n                    },\n                    {\n                        createdAt: 'desc'\n                    }\n                ];\n        }\n        // جلب الإعلانات\n        const [ads, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.ad.findMany({\n                where,\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            avatar: true,\n                            ratingAverage: true,\n                            ratingCount: true\n                        }\n                    }\n                },\n                orderBy,\n                skip,\n                take: limit\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.ad.count({\n                where\n            })\n        ]);\n        const totalPages = Math.ceil(total / limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: ads,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching ads:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في جلب الإعلانات'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ads/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO0tBQVE7QUFDaEIsR0FBRTtBQUVKLElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknXSxcbiAgfSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();