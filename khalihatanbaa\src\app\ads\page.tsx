"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { PlaceholderImage } from "@/components/ui/PlaceholderImage";
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  HeartIcon,
  EyeIcon,
  MapPinIcon,
  ClockIcon,
  StarIcon,
} from "@heroicons/react/24/outline";

interface Ad {
  id: string;
  title: string;
  description: string;
  price: number;
  category: string;
  condition: string;
  city: string;
  region?: string;
  imageUrls: string[];
  views: number;
  isPromoted: boolean;
  createdAt: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
    ratingAverage: number;
    ratingCount: number;
  };
}

export default function AdsPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [condition, setCondition] = useState("");
  const [sortBy, setSortBy] = useState("newest");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const categories = [
    "عقارات",
    "سيارات",
    "إلكترونيات",
    "أثاث",
    "ملابس",
    "خدمات",
    "أخرى",
  ];

  const cities = [
    "دمشق",
    "حلب",
    "حمص",
    "حماة",
    "اللاذقية",
    "طرطوس",
    "درعا",
    "السويداء",
    "القنيطرة",
    "دير الزور",
    "الرقة",
    "الحسكة",
    "إدلب",
    "ريف دمشق",
  ];

  useEffect(() => {
    fetchAds();
  }, [page, selectedCategory, selectedCity, condition, sortBy]);

  const fetchAds = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "12",
        ...(selectedCategory && { category: selectedCategory }),
        ...(selectedCity && { city: selectedCity }),
        ...(condition && { condition }),
        ...(minPrice && { minPrice }),
        ...(maxPrice && { maxPrice }),
        ...(searchTerm && { search: searchTerm }),
        ...(sortBy && { sortBy }),
      });

      const response = await fetch(`/api/ads?${params}`);
      const data = await response.json();

      if (data.success) {
        setAds(data.data);
        setTotalPages(data.pagination?.totalPages || 1);
      }
    } catch (error) {
      console.error("Error fetching ads:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPage(1);
    fetchAds();
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("ar-SY").format(price);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "اليوم";
    if (diffDays === 2) return "أمس";
    if (diffDays < 7) return `${diffDays} أيام`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} أسابيع`;
    return `${Math.ceil(diffDays / 30)} شهور`;
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-dark-800 mb-2">
            جميع الإعلانات
          </h1>
          <p className="text-gray-600">اكتشف آلاف الإعلانات في جميع الفئات</p>
        </div>

        {/* شريط البحث والفلاتر */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
          {/* البحث */}
          <div className="flex space-x-4 space-x-reverse mb-6">
            <div className="flex-1">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="ابحث عن أي شيء..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              />
            </div>
            <button
              onClick={handleSearch}
              className="px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors flex items-center"
            >
              <MagnifyingGlassIcon className="h-5 w-5 ml-2" />
              بحث
            </button>
          </div>

          {/* الفلاتر */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">جميع الفئات</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <select
              value={selectedCity}
              onChange={(e) => setSelectedCity(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">جميع المدن</option>
              {cities.map((city) => (
                <option key={city} value={city}>
                  {city}
                </option>
              ))}
            </select>

            <select
              value={condition}
              onChange={(e) => setCondition(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">جميع الحالات</option>
              <option value="جديد">جديد</option>
              <option value="مستعمل">مستعمل</option>
            </select>

            <input
              type="number"
              value={minPrice}
              onChange={(e) => setMinPrice(e.target.value)}
              placeholder="أقل سعر"
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />

            <input
              type="number"
              value={maxPrice}
              onChange={(e) => setMaxPrice(e.target.value)}
              placeholder="أعلى سعر"
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="newest">الأحدث</option>
              <option value="oldest">الأقدم</option>
              <option value="price_low">السعر: من الأقل للأعلى</option>
              <option value="price_high">السعر: من الأعلى للأقل</option>
              <option value="most_viewed">الأكثر مشاهدة</option>
            </select>
          </div>
        </div>

        {/* النتائج */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(12)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : ads.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MagnifyingGlassIcon className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد نتائج
            </h3>
            <p className="text-gray-500 mb-6">
              جرب تغيير معايير البحث أو الفلاتر
            </p>
            <button
              onClick={() => {
                setSearchTerm("");
                setSelectedCategory("");
                setSelectedCity("");
                setCondition("");
                setMinPrice("");
                setMaxPrice("");
                setPage(1);
                fetchAds();
              }}
              className="px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
            >
              مسح الفلاتر
            </button>
          </div>
        ) : (
          <>
            {/* عدد النتائج */}
            <div className="flex items-center justify-between mb-6">
              <p className="text-gray-600">عرض {ads.length} إعلان</p>
              <div className="flex items-center space-x-2 space-x-reverse">
                <FunnelIcon className="h-5 w-5 text-gray-400" />
                <span className="text-sm text-gray-500">
                  مرتب حسب:{" "}
                  {sortBy === "newest"
                    ? "الأحدث"
                    : sortBy === "oldest"
                    ? "الأقدم"
                    : sortBy === "price_low"
                    ? "السعر الأقل"
                    : sortBy === "price_high"
                    ? "السعر الأعلى"
                    : "الأكثر مشاهدة"}
                </span>
              </div>
            </div>

            {/* شبكة الإعلانات */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {ads.map((ad) => (
                <Link
                  key={ad.id}
                  href={`/ads/${ad.id}`}
                  className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 group"
                >
                  {/* الصورة */}
                  <div className="relative h-48 bg-gray-200">
                    {ad.imageUrls && ad.imageUrls.length > 0 ? (
                      <img
                        src={ad.imageUrls[0]}
                        alt={ad.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <PlaceholderImage
                        width={300}
                        height={192}
                        text={ad.category}
                        className="w-full h-full"
                      />
                    )}

                    {/* شارة الإعلان المميز */}
                    {ad.isPromoted && (
                      <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                        <StarIcon className="h-3 w-3 ml-1" />
                        مميز
                      </div>
                    )}

                    {/* شارة الحالة */}
                    <div className="absolute top-2 left-2 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                      {ad.condition}
                    </div>
                  </div>

                  {/* المحتوى */}
                  <div className="p-4">
                    <h3 className="font-semibold text-dark-800 mb-2 line-clamp-2 group-hover:text-primary-500 transition-colors">
                      {ad.title}
                    </h3>

                    <div className="text-xl font-bold text-primary-500 mb-3">
                      {formatPrice(ad.price)}{" "}
                      <span className="text-sm">ل.س</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <MapPinIcon className="h-4 w-4 ml-1" />
                      {ad.city}
                      {ad.region && ` - ${ad.region}`}
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-400">
                      <div className="flex items-center">
                        <ClockIcon className="h-3 w-3 ml-1" />
                        {formatDate(ad.createdAt)}
                      </div>
                      <div className="flex items-center">
                        <EyeIcon className="h-3 w-3 ml-1" />
                        {ad.views}
                      </div>
                    </div>

                    {/* معلومات البائع */}
                    <div className="flex items-center mt-3 pt-3 border-t border-gray-100">
                      <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center text-xs font-medium text-primary-600">
                        {ad.user.name.charAt(0)}
                      </div>
                      <span className="text-sm text-gray-600 mr-2">
                        {ad.user.name}
                      </span>
                      {ad.user.ratingAverage > 0 && (
                        <div className="flex items-center mr-auto">
                          <StarIcon className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-500 mr-1">
                            {ad.user.ratingAverage.toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* التنقل بين الصفحات */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  السابق
                </button>

                <div className="flex space-x-1 space-x-reverse">
                  {[...Array(Math.min(5, totalPages))].map((_, i) => {
                    const pageNum = i + 1;
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setPage(pageNum)}
                        className={`px-3 py-2 rounded-lg ${
                          page === pageNum
                            ? "bg-primary-500 text-white"
                            : "border border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setPage(Math.min(totalPages, page + 1))}
                  disabled={page === totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  التالي
                </button>
              </div>
            )}
          </>
        )}
      </main>

      <Footer />
    </div>
  );
}
