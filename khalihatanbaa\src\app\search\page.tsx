'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { PlaceholderImage } from '@/components/ui/PlaceholderImage'
import { CATEGORIES, SYRIAN_CITIES, Ad } from '@/types'
import { 
  MagnifyingGlassIcon, 
  AdjustmentsHorizontalIcon,
  HeartIcon,
  MapPinIcon,
  ClockIcon 
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

// بيانات تجريبية للبحث
const sampleSearchResults: Ad[] = [
  {
    id: '1',
    title: 'تويوتا كامري 2018 فل كامل',
    description: 'سيارة بحالة ممتازة، صيانة دورية، لون أبيض',
    price: 45000000,
    category: 'سيارات',
    subCategory: 'تويوتا',
    condition: 'مستعمل',
    city: 'دمشق',
    region: 'المزة',
    imageUrls: ['/placeholder-car.jpg'],
    views: 125,
    isActive: true,
    isFreeAd: false,
    isPromoted: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    userId: 'user1'
  },
  {
    id: '2',
    title: 'شقة 3 غرف في المالكي',
    description: 'شقة مفروشة بالكامل، إطلالة رائعة، قريبة من الخدمات',
    price: 120000000,
    category: 'عقارات',
    subCategory: 'شقة',
    condition: 'مستعمل',
    city: 'دمشق',
    region: 'المالكي',
    imageUrls: ['/placeholder-apartment.jpg'],
    views: 89,
    isActive: true,
    isFreeAd: true,
    isPromoted: false,
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-14'),
    userId: 'user2'
  }
]

function SearchContent() {
  const searchParams = useSearchParams()
  const [ads, setAds] = useState<Ad[]>([])
  const [loading, setLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  
  const [filters, setFilters] = useState({
    keyword: searchParams.get('q') || '',
    category: searchParams.get('category') || '',
    subCategory: '',
    city: '',
    minPrice: '',
    maxPrice: '',
    condition: '',
    sortBy: 'newest'
  })

  useEffect(() => {
    // محاكاة جلب البيانات من API
    setLoading(true)
    setTimeout(() => {
      setAds(sampleSearchResults)
      setLoading(false)
    }, 1000)
  }, [filters])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SY').format(price)
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return 'منذ يوم واحد'
    if (diffDays < 7) return `منذ ${diffDays} أيام`
    if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`
    return `منذ ${Math.ceil(diffDays / 30)} شهر`
  }

  const toggleFavorite = (adId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev)
      if (newFavorites.has(adId)) {
        newFavorites.delete(adId)
      } else {
        newFavorites.add(adId)
      }
      return newFavorites
    })
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const clearFilters = () => {
    setFilters({
      keyword: '',
      category: '',
      subCategory: '',
      city: '',
      minPrice: '',
      maxPrice: '',
      condition: '',
      sortBy: 'newest'
    })
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* شريط البحث والفلاتر */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* شريط البحث */}
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="ابحث عن أي شيء..."
                value={filters.keyword}
                onChange={(e) => handleFilterChange('keyword', e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>

            {/* زر الفلاتر */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="lg:w-auto px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center justify-center gap-2"
            >
              <AdjustmentsHorizontalIcon className="h-5 w-5" />
              <span>فلاتر</span>
            </button>
          </div>

          {/* الفلاتر المتقدمة */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                  <select
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">جميع الفئات</option>
                    {Object.entries(CATEGORIES).map(([key, category]) => (
                      <option key={key} value={key}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                  <select
                    value={filters.city}
                    onChange={(e) => handleFilterChange('city', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {SYRIAN_CITIES.map((city) => (
                      <option key={city} value={city}>
                        {city}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                  <select
                    value={filters.condition}
                    onChange={(e) => handleFilterChange('condition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="جديد">جديد</option>
                    <option value="مستعمل">مستعمل</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ترتيب حسب</label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="price_low">السعر: من الأقل للأعلى</option>
                    <option value="price_high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر الأدنى (ل.س)</label>
                  <input
                    type="number"
                    placeholder="0"
                    value={filters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر الأعلى (ل.س)</label>
                  <input
                    type="number"
                    placeholder="∞"
                    value={filters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  مسح الفلاتر
                </button>
              </div>
            </div>
          )}
        </div>

        {/* النتائج */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-dark-800">
            {loading ? 'جاري البحث...' : `تم العثور على ${ads.length} إعلان`}
          </h2>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4 space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : ads.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">لم يتم العثور على نتائج</h3>
            <p className="text-gray-500">جرب تعديل كلمات البحث أو الفلاتر</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {ads.map((ad) => (
              <div
                key={ad.id}
                className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300"
              >
                {/* صورة الإعلان */}
                <div className="relative h-48 bg-gray-200">
                  <PlaceholderImage 
                    width={300} 
                    height={192} 
                    text={`صورة ${ad.category}`}
                    className="w-full h-full"
                  />
                  
                  {/* شارة الإعلان المميز */}
                  {ad.isPromoted && (
                    <div className="absolute top-3 right-3 bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      مميز
                    </div>
                  )}

                  {/* زر المفضلة */}
                  <button
                    onClick={() => toggleFavorite(ad.id)}
                    className="absolute top-3 left-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
                  >
                    {favorites.has(ad.id) ? (
                      <HeartSolidIcon className="h-5 w-5 text-red-500" />
                    ) : (
                      <HeartIcon className="h-5 w-5 text-gray-600" />
                    )}
                  </button>
                </div>

                {/* محتوى الإعلان */}
                <div className="p-4">
                  <Link href={`/ads/${ad.id}`}>
                    <h3 className="font-semibold text-dark-800 mb-2 hover:text-primary-500 transition-colors line-clamp-2">
                      {ad.title}
                    </h3>
                  </Link>

                  <div className="flex items-center text-sm text-dark-500 mb-2">
                    <MapPinIcon className="h-4 w-4 ml-1" />
                    {ad.city} - {ad.region}
                  </div>

                  <div className="flex items-center text-sm text-dark-500 mb-3">
                    <ClockIcon className="h-4 w-4 ml-1" />
                    {formatDate(ad.createdAt)}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-lg font-bold text-primary-500">
                      {formatPrice(ad.price)} <span className="text-sm">ل.س</span>
                    </div>
                    <div className="text-sm text-dark-500">
                      {ad.views} مشاهدة
                    </div>
                  </div>

                  <div className="mt-3 flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      ad.condition === 'جديد' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {ad.condition}
                    </span>
                    
                    {ad.isFreeAd && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        إعلان مجاني
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {!loading && ads.length > 0 && (
          <div className="mt-12 flex justify-center">
            <div className="flex items-center space-x-2 space-x-reverse">
              <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                السابق
              </button>
              <button className="px-3 py-2 bg-primary-500 text-white rounded-lg">
                1
              </button>
              <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                2
              </button>
              <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                3
              </button>
              <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                التالي
              </button>
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">جاري التحميل...</div>}>
      <SearchContent />
    </Suspense>
  )
}
