'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib_es6 = require('../../tslib.es6-7a681263.cjs');
var internal_Action = require('../../internal/Action.cjs');
require('../flag/FlagQualifier.cjs');
require('../../internal/qualifier/QualifierValue.cjs');
require('../../internal/qualifier/Qualifier.cjs');
require('../../internal/models/QualifierModel.cjs');
require('../../internal/models/qualifierToJson.cjs');
require('../../internal/utils/unsupportedError.cjs');
require('../../internal/utils/dataStructureUtils.cjs');
require('../../internal/models/ActionModel.cjs');
require('../../internal/models/actionToJson.cjs');

/**
 * @memberOf Qualifiers.Region
 */
var NamedRegion = /** @class */ (function (_super) {
    tslib_es6.__extends(NamedRegion, _super);
    function NamedRegion(type) {
        var _this = _super.call(this) || this;
        _this.regionType = type;
        _this._actionModel.regionType = type;
        return _this;
    }
    return NamedRegion;
}(internal_Action.Action));

exports.NamedRegion = NamedRegion;
