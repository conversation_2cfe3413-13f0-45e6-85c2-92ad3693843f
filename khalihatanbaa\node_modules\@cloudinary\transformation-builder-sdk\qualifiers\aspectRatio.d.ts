import { AspectRatioQualifierValue } from "./aspectRatio/AspectRatioQualifierValue.js";
import { FlagQualifier } from "./flag/FlagQualifier.js";
/**
 * @description 1 by 1 aspect ration.
 * @memberOf Qualifiers.AspectRatio
 * @return {string}
 */
declare function ar1X1(): AspectRatioQualifierValue;
/**
 * @summary qualifier
 * @description 5 by 4 aspect ration.
 * @memberOf Qualifiers.AspectRatio
 * @return {string}
 */
declare function ar5X4(): AspectRatioQualifierValue;
/**
 * @summary qualifier
 * @description 4 by 3 aspect ration.
 * @memberOf Qualifiers.AspectRatio
 * @return {string}
 */
declare function ar4X3(): AspectRatioQualifierValue;
/**
 * @summary qualifier
 * @description 3 by 2 aspect ration.
 * @memberOf Qualifiers.AspectRatio
 * @return {string}
 */
declare function ar3X2(): AspectRatioQualifierValue;
/**
 * @summary qualifier
 * @description 16 by 9 aspect ration.
 * @memberOf Qualifiers.AspectRatio
 * @return {string}
 */
declare function ar16X9(): AspectRatioQualifierValue;
/**
 * @summary qualifier
 * @description 3 by 1 aspect ration.
 * @memberOf Qualifiers.AspectRatio
 * @return {string}
 */
declare function ar3X1(): AspectRatioQualifierValue;
/**
 * @summary qualifier
 * @description ignores aspect ratio.
 * @memberOf Qualifiers.AspectRatio
 * @return {IgnoreAspectRatioQualifier}
 */
declare function ignoreInitialAspectRatio(): FlagQualifier;
/**
 * @summary qualifier
 * @description A list of all most commonly used aspect ratios. including an ‘ignore aspect ratio’ option which direct the BE to not stick to the original aspect ratio.
 * This is used in the context of resize actions
 * @namespace AspectRatio
 * @memberOf Qualifiers
 * @see Visit {@link Actions.Resize|Resize} for an example
 */
declare const AspectRatio: {
    ar1X1: typeof ar1X1;
    ar5X4: typeof ar5X4;
    ar3X1: typeof ar3X1;
    ar3X2: typeof ar3X2;
    ar4X3: typeof ar4X3;
    ar16X9: typeof ar16X9;
    ignoreInitialAspectRatio: typeof ignoreInitialAspectRatio;
};
export { ar1X1, ar5X4, ar3X1, ar3X2, ar4X3, ar16X9, ignoreInitialAspectRatio, AspectRatio };
