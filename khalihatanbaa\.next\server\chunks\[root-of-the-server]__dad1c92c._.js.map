{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/api/ads/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/prisma\";\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params;\n    const ad = await prisma.ad.findUnique({\n      where: {\n        id: id,\n        isActive: true,\n      },\n      include: {\n        user: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n            ratingAverage: true,\n            ratingCount: true,\n            createdAt: true,\n          },\n        },\n      },\n    });\n\n    if (!ad) {\n      return NextResponse.json(\n        { success: false, error: \"الإعلان غير موجود\" },\n        { status: 404 }\n      );\n    }\n\n    // زيادة عدد المشاهدات\n    await prisma.ad.update({\n      where: { id: id },\n      data: { views: { increment: 1 } },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: { ...ad, views: ad.views + 1 },\n    });\n  } catch (error) {\n    console.error(\"Error fetching ad:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في جلب الإعلان\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;YACpC,OAAO;gBACL,IAAI;gBACJ,UAAU;YACZ;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;wBACR,eAAe;wBACf,aAAa;wBACb,WAAW;oBACb;gBACF;YACF;QACF;QAEA,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,MAAM,CAAC;YACrB,OAAO;gBAAE,IAAI;YAAG;YAChB,MAAM;gBAAE,OAAO;oBAAE,WAAW;gBAAE;YAAE;QAClC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBAAE,GAAG,EAAE;gBAAE,OAAO,GAAG,KAAK,GAAG;YAAE;QACrC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAyB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}