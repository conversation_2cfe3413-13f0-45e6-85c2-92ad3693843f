/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/messages/page";
exports.ids = ["app/messages/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/messages/page.tsx */ \"(rsc)/./src/app/messages/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'messages',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/messages/page\",\n        pathname: \"/messages\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(rsc)/./src/components/providers/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dJTjEwJTVDJTVDRGVza3RvcCU1QyU1Q2thbGloYSU1QyU1Q2toYWxpaGF0YW5iYWElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNXSU4xMCU1QyU1Q0Rlc2t0b3AlNUMlNUNrYWxpaGElNUMlNUNraGFsaWhhdGFuYmFhJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q0F1dGhQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtNQUFxSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcV0lOMTBcXFxcRGVza3RvcFxcXFxrYWxpaGFcXFxca2hhbGloYXRhbmJhYVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnNcXFxcQXV0aFByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cmessages%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cmessages%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/messages/page.tsx */ \"(rsc)/./src/app/messages/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dJTjEwJTVDJTVDRGVza3RvcCU1QyU1Q2thbGloYSU1QyU1Q2toYWxpaGF0YW5iYWElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtZXNzYWdlcyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBbUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFdJTjEwXFxcXERlc2t0b3BcXFxca2FsaWhhXFxcXGtoYWxpaGF0YW5iYWFcXFxcc3JjXFxcXGFwcFxcXFxtZXNzYWdlc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cmessages%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"10728d75e266\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjEwNzI4ZDc1ZTI2NlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n\n\n\nconst metadata = {\n    title: \"خَلّيها تنْباع - منصة البيع والشراء في سوريا\",\n    description: \"منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا. اعثر على أفضل العروض للعقارات، السيارات، الإلكترونيات وأكثر.\",\n    keywords: \"بيع، شراء، سوريا، إعلانات، عقارات، سيارات، إلكترونيات\",\n    authors: [\n        {\n            name: \"خَلّيها تنْباع\"\n        }\n    ],\n    openGraph: {\n        title: \"خَلّيها تنْباع - منصة البيع والشراء في سوريا\",\n        description: \"منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا\",\n        url: \"https://khalihatanbaa.com\",\n        siteName: \"خَلّيها تنْباع\",\n        locale: \"ar_SY\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"خَلّيها تنْباع - منصة البيع والشراء في سوريا\",\n        description: \"منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-arabic antialiased\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/messages/page.tsx":
/*!***********************************!*\
  !*** ./src/app/messages/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\kaliha\\khalihatanbaa\\src\\app\\messages\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\kaliha\\khalihatanbaa\\src\\components\\providers\\AuthProvider.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dJTjEwJTVDJTVDRGVza3RvcCU1QyU1Q2thbGloYSU1QyU1Q2toYWxpaGF0YW5iYWElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNXSU4xMCU1QyU1Q0Rlc2t0b3AlNUMlNUNrYWxpaGElNUMlNUNraGFsaWhhdGFuYmFhJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q0F1dGhQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtNQUFxSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcV0lOMTBcXFxcRGVza3RvcFxcXFxrYWxpaGFcXFxca2hhbGloYXRhbmJhYVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnNcXFxcQXV0aFByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cmessages%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cmessages%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/messages/page.tsx */ \"(ssr)/./src/app/messages/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dJTjEwJTVDJTVDRGVza3RvcCU1QyU1Q2thbGloYSU1QyU1Q2toYWxpaGF0YW5iYWElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtZXNzYWdlcyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBbUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFdJTjEwXFxcXERlc2t0b3BcXFxca2FsaWhhXFxcXGtoYWxpaGF0YW5iYWFcXFxcc3JjXFxcXGFwcFxcXFxtZXNzYWdlc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWIN10%5C%5CDesktop%5C%5Ckaliha%5C%5Ckhalihatanbaa%5C%5Csrc%5C%5Capp%5C%5Cmessages%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/messages/page.tsx":
/*!***********************************!*\
  !*** ./src/app/messages/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessagesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,PaperAirplaneIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,PaperAirplaneIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,PaperAirplaneIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,PaperAirplaneIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction MessagesPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConversation, setSelectedConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // تعريف الدوال\n    const fetchConversations = async ()=>{\n        try {\n            const response = await fetch(\"/api/messages\");\n            const data = await response.json();\n            if (data.success) {\n                setConversations(data.data);\n                // إذا لم تكن هناك محادثة محددة وهناك محادثات متاحة، اختر الأولى\n                if (!selectedConversation && data.data.length > 0) {\n                    setSelectedConversation(data.data[0].otherUser.id);\n                    fetchMessages(data.data[0].otherUser.id, data.data[0].adId);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching conversations:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMessages = async (userId, adId)=>{\n        try {\n            const url = new URL(\"/api/messages\", window.location.origin);\n            url.searchParams.set(\"with\", userId);\n            if (adId) url.searchParams.set(\"adId\", adId);\n            const response = await fetch(url.toString());\n            const data = await response.json();\n            if (data.success) {\n                setMessages(data.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching messages:\", error);\n        }\n    };\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    // جميع useEffect يجب أن تكون في الأعلى قبل أي return مشروط\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessagesPage.useEffect\": ()=>{\n            if (status === \"authenticated\") {\n                fetchConversations();\n                // التحقق من وجود محادثة محددة في URL\n                const withUser = searchParams.get(\"with\") || searchParams.get(\"user\");\n                const adId = searchParams.get(\"ad\");\n                if (withUser) {\n                    setSelectedConversation(withUser);\n                    fetchMessages(withUser, adId);\n                }\n            }\n        }\n    }[\"MessagesPage.useEffect\"], [\n        searchParams,\n        status\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessagesPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"MessagesPage.useEffect\"], [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessagesPage.useEffect\": ()=>{\n            if (status === \"unauthenticated\") {\n                router.push(\"/auth/login\");\n            }\n        }\n    }[\"MessagesPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // إعادة توجيه إذا لم يكن المستخدم مسجل دخول\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: \"جاري التحميل...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        return null;\n    }\n    const sendMessage = async ()=>{\n        if (!newMessage.trim() || !selectedConversation || sending) return;\n        setSending(true);\n        try {\n            const adId = searchParams.get(\"ad\");\n            const response = await fetch(\"/api/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    toId: selectedConversation,\n                    content: newMessage.trim(),\n                    ...adId && {\n                        adId\n                    }\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessages((prev)=>[\n                        ...prev,\n                        data.data\n                    ]);\n                setNewMessage(\"\");\n                fetchConversations(); // تحديث قائمة المحادثات\n            } else {\n                alert(data.error || \"حدث خطأ في إرسال الرسالة\");\n            }\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            alert(\"حدث خطأ في إرسال الرسالة\");\n        } finally{\n            setSending(false);\n        }\n    };\n    const selectConversation = (conversation)=>{\n        setSelectedConversation(conversation.otherUser.id);\n        fetchMessages(conversation.otherUser.id, conversation.adId);\n        // تحديث URL\n        const url = new URL(window.location.href);\n        url.searchParams.set(\"with\", conversation.otherUser.id);\n        if (conversation.adId) {\n            url.searchParams.set(\"ad\", conversation.adId);\n        } else {\n            url.searchParams.delete(\"ad\");\n        }\n        window.history.pushState({}, \"\", url.toString());\n    };\n    const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffTime = Math.abs(now.getTime() - date.getTime());\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        if (diffDays === 1) {\n            return date.toLocaleTimeString(\"ar-SY\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffDays < 7) {\n            return `${diffDays} أيام`;\n        } else {\n            return date.toLocaleDateString(\"ar-SY\");\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SY\").format(price);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-200 rounded w-1/4 mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-96 bg-gray-200 rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 h-96 bg-gray-200 rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_5__.Footer, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n            lineNumber: 233,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-dark-800 mb-6\",\n                        children: \"الرسائل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-dark-800\",\n                                            children: \"المحادثات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-y-auto h-full\",\n                                        children: conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 text-center text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-3 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد محادثات حتى الآن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm mt-1\",\n                                                    children: \"ابدأ محادثة من خلال التواصل مع بائع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this) : conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>selectConversation(conversation),\n                                                className: `w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-right transition-colors ${selectedConversation === conversation.otherUser.id ? \"bg-primary-50\" : \"\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                            children: conversation.otherUser.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: conversation.otherUser.avatar,\n                                                                alt: conversation.otherUser.name,\n                                                                className: \"w-full h-full rounded-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-5 w-5 text-primary-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-dark-800 truncate\",\n                                                                            children: conversation.otherUser.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        conversation.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\",\n                                                                            children: conversation.unreadCount\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                conversation.ad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 truncate mb-1\",\n                                                                    children: conversation.ad.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500 truncate\",\n                                                                    children: conversation.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400 ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: formatTime(conversation.createdAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, `${conversation.otherUser.id}-${conversation.adId || \"general\"}`, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col\",\n                                children: selectedConversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 border-b border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-dark-800\",\n                                                                children: conversations.find((c)=>c.otherUser.id === selectedConversation)?.otherUser.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            searchParams.get(\"ad\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"حول:\",\n                                                                    \" \",\n                                                                    conversations.find((c)=>c.otherUser.id === selectedConversation)?.ad?.title\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                                            children: [\n                                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `flex ${message.fromId === session?.user?.id ? \"justify-end\" : \"justify-start\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${message.fromId === session?.user?.id ? \"bg-primary-500 text-white\" : \"bg-gray-100 text-dark-800\"}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: `text-xs mt-1 ${message.fromId === session?.user?.id ? \"text-primary-100\" : \"text-gray-500\"}`,\n                                                                    children: formatTime(message.createdAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, message.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newMessage,\n                                                        onChange: (e)=>setNewMessage(e.target.value),\n                                                        onKeyPress: (e)=>e.key === \"Enter\" && sendMessage(),\n                                                        placeholder: \"اكتب رسالتك...\",\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                                        disabled: sending\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: sendMessage,\n                                                        disabled: !newMessage.trim() || sending,\n                                                        className: \"px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_PaperAirplaneIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"اختر محادثة لبدء المراسلة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-1\",\n                                                children: \"اختر محادثة من القائمة الجانبية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_5__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\messages\\\\page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL21lc3NhZ2VzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVvRDtBQUNQO0FBQ2dCO0FBQ1Q7QUFDQTtBQU9mO0FBK0N0QixTQUFTWTtJQUN0QixNQUFNLEVBQUVDLE1BQU1DLE9BQU8sRUFBRUMsTUFBTSxFQUFFLEdBQUdaLDJEQUFVQTtJQUM1QyxNQUFNYSxTQUFTWiwwREFBU0E7SUFDeEIsTUFBTWEsZUFBZVosZ0VBQWVBO0lBRXBDLE1BQU0sQ0FBQ2EsZUFBZUMsaUJBQWlCLEdBQUduQiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUNvQixzQkFBc0JDLHdCQUF3QixHQUFHckIsK0NBQVFBLENBRTlEO0lBQ0YsTUFBTSxDQUFDc0IsVUFBVUMsWUFBWSxHQUFHdkIsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUN3QixZQUFZQyxjQUFjLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUMwQixTQUFTQyxXQUFXLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM0QixTQUFTQyxXQUFXLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNOEIsaUJBQWlCNUIsNkNBQU1BLENBQWlCO0lBRTlDLGVBQWU7SUFDZixNQUFNNkIscUJBQXFCO1FBQ3pCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTXBCLE9BQU8sTUFBTW1CLFNBQVNFLElBQUk7WUFFaEMsSUFBSXJCLEtBQUtzQixPQUFPLEVBQUU7Z0JBQ2hCaEIsaUJBQWlCTixLQUFLQSxJQUFJO2dCQUMxQixnRUFBZ0U7Z0JBQ2hFLElBQUksQ0FBQ08sd0JBQXdCUCxLQUFLQSxJQUFJLENBQUN1QixNQUFNLEdBQUcsR0FBRztvQkFDakRmLHdCQUF3QlIsS0FBS0EsSUFBSSxDQUFDLEVBQUUsQ0FBQ3dCLFNBQVMsQ0FBQ0MsRUFBRTtvQkFDakRDLGNBQWMxQixLQUFLQSxJQUFJLENBQUMsRUFBRSxDQUFDd0IsU0FBUyxDQUFDQyxFQUFFLEVBQUV6QixLQUFLQSxJQUFJLENBQUMsRUFBRSxDQUFDMkIsSUFBSTtnQkFDNUQ7WUFDRjtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtRQUNqRCxTQUFVO1lBQ1JkLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTVksZ0JBQWdCLE9BQU9JLFFBQWdCSDtRQUMzQyxJQUFJO1lBQ0YsTUFBTUksTUFBTSxJQUFJQyxJQUFJLGlCQUFpQkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO1lBQzNESixJQUFJM0IsWUFBWSxDQUFDZ0MsR0FBRyxDQUFDLFFBQVFOO1lBQzdCLElBQUlILE1BQU1JLElBQUkzQixZQUFZLENBQUNnQyxHQUFHLENBQUMsUUFBUVQ7WUFFdkMsTUFBTVIsV0FBVyxNQUFNQyxNQUFNVyxJQUFJTSxRQUFRO1lBQ3pDLE1BQU1yQyxPQUFPLE1BQU1tQixTQUFTRSxJQUFJO1lBRWhDLElBQUlyQixLQUFLc0IsT0FBTyxFQUFFO2dCQUNoQlosWUFBWVYsS0FBS0EsSUFBSTtZQUN2QjtRQUNGLEVBQUUsT0FBTzRCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUM7SUFDRjtJQUVBLE1BQU1VLGlCQUFpQjtRQUNyQnJCLGVBQWVzQixPQUFPLEVBQUVDLGVBQWU7WUFBRUMsVUFBVTtRQUFTO0lBQzlEO0lBRUEsMkRBQTJEO0lBQzNEckQsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSWMsV0FBVyxpQkFBaUI7Z0JBQzlCZ0I7Z0JBRUEscUNBQXFDO2dCQUNyQyxNQUFNd0IsV0FBV3RDLGFBQWF1QyxHQUFHLENBQUMsV0FBV3ZDLGFBQWF1QyxHQUFHLENBQUM7Z0JBQzlELE1BQU1oQixPQUFPdkIsYUFBYXVDLEdBQUcsQ0FBQztnQkFFOUIsSUFBSUQsVUFBVTtvQkFDWmxDLHdCQUF3QmtDO29CQUN4QmhCLGNBQWNnQixVQUFVZjtnQkFDMUI7WUFDRjtRQUNGO2lDQUFHO1FBQUN2QjtRQUFjRjtLQUFPO0lBRXpCZCxnREFBU0E7a0NBQUM7WUFDUmtEO1FBQ0Y7aUNBQUc7UUFBQzdCO0tBQVM7SUFFYnJCLGdEQUFTQTtrQ0FBQztZQUNSLElBQUljLFdBQVcsbUJBQW1CO2dCQUNoQ0MsT0FBT3lDLElBQUksQ0FBQztZQUNkO1FBQ0Y7aUNBQUc7UUFBQzFDO1FBQVFDO0tBQU87SUFFbkIsNENBQTRDO0lBQzVDLElBQUlELFdBQVcsV0FBVztRQUN4QixxQkFDRSw4REFBQzJDO1lBQUlDLFdBQVU7c0JBQWdEOzs7Ozs7SUFJbkU7SUFFQSxJQUFJNUMsV0FBVyxtQkFBbUI7UUFDaEMsT0FBTztJQUNUO0lBRUEsTUFBTTZDLGNBQWM7UUFDbEIsSUFBSSxDQUFDcEMsV0FBV3FDLElBQUksTUFBTSxDQUFDekMsd0JBQXdCUSxTQUFTO1FBRTVEQyxXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU1XLE9BQU92QixhQUFhdUMsR0FBRyxDQUFDO1lBRTlCLE1BQU14QixXQUFXLE1BQU1DLE1BQU0saUJBQWlCO2dCQUM1QzZCLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQkMsTUFBTS9DO29CQUNOZ0QsU0FBUzVDLFdBQVdxQyxJQUFJO29CQUN4QixHQUFJckIsUUFBUTt3QkFBRUE7b0JBQUssQ0FBQztnQkFDdEI7WUFDRjtZQUVBLE1BQU0zQixPQUFPLE1BQU1tQixTQUFTRSxJQUFJO1lBRWhDLElBQUlyQixLQUFLc0IsT0FBTyxFQUFFO2dCQUNoQlosWUFBWSxDQUFDOEMsT0FBUzsyQkFBSUE7d0JBQU14RCxLQUFLQSxJQUFJO3FCQUFDO2dCQUMxQ1ksY0FBYztnQkFDZE0sc0JBQXNCLHdCQUF3QjtZQUNoRCxPQUFPO2dCQUNMdUMsTUFBTXpELEtBQUs0QixLQUFLLElBQUk7WUFDdEI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEM2QixNQUFNO1FBQ1IsU0FBVTtZQUNSekMsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNMEMscUJBQXFCLENBQUNDO1FBQzFCbkQsd0JBQXdCbUQsYUFBYW5DLFNBQVMsQ0FBQ0MsRUFBRTtRQUNqREMsY0FBY2lDLGFBQWFuQyxTQUFTLENBQUNDLEVBQUUsRUFBRWtDLGFBQWFoQyxJQUFJO1FBRTFELFlBQVk7UUFDWixNQUFNSSxNQUFNLElBQUlDLElBQUlDLE9BQU9DLFFBQVEsQ0FBQzBCLElBQUk7UUFDeEM3QixJQUFJM0IsWUFBWSxDQUFDZ0MsR0FBRyxDQUFDLFFBQVF1QixhQUFhbkMsU0FBUyxDQUFDQyxFQUFFO1FBQ3RELElBQUlrQyxhQUFhaEMsSUFBSSxFQUFFO1lBQ3JCSSxJQUFJM0IsWUFBWSxDQUFDZ0MsR0FBRyxDQUFDLE1BQU11QixhQUFhaEMsSUFBSTtRQUM5QyxPQUFPO1lBQ0xJLElBQUkzQixZQUFZLENBQUN5RCxNQUFNLENBQUM7UUFDMUI7UUFDQTVCLE9BQU82QixPQUFPLENBQUNDLFNBQVMsQ0FBQyxDQUFDLEdBQUcsSUFBSWhDLElBQUlNLFFBQVE7SUFDL0M7SUFFQSxNQUFNMkIsYUFBYSxDQUFDQztRQUNsQixNQUFNQyxPQUFPLElBQUlDLEtBQUtGO1FBQ3RCLE1BQU1HLE1BQU0sSUFBSUQ7UUFDaEIsTUFBTUUsV0FBV0MsS0FBS0MsR0FBRyxDQUFDSCxJQUFJSSxPQUFPLEtBQUtOLEtBQUtNLE9BQU87UUFDdEQsTUFBTUMsV0FBV0gsS0FBS0ksSUFBSSxDQUFDTCxXQUFZLFFBQU8sS0FBSyxLQUFLLEVBQUM7UUFFekQsSUFBSUksYUFBYSxHQUFHO1lBQ2xCLE9BQU9QLEtBQUtTLGtCQUFrQixDQUFDLFNBQVM7Z0JBQ3RDQyxNQUFNO2dCQUNOQyxRQUFRO1lBQ1Y7UUFDRixPQUFPLElBQUlKLFdBQVcsR0FBRztZQUN2QixPQUFPLEdBQUdBLFNBQVMsS0FBSyxDQUFDO1FBQzNCLE9BQU87WUFDTCxPQUFPUCxLQUFLWSxrQkFBa0IsQ0FBQztRQUNqQztJQUNGO0lBRUEsTUFBTUMsY0FBYyxDQUFDQztRQUNuQixPQUFPLElBQUlDLEtBQUtDLFlBQVksQ0FBQyxTQUFTQyxNQUFNLENBQUNIO0lBQy9DO0lBRUEsSUFBSW5FLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2dDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDckQsNkRBQU1BOzs7Ozs4QkFDUCw4REFBQ29EO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzs7Ozs7MENBQ2YsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztrREFDZiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUlyQiw4REFBQ3BELDZEQUFNQTs7Ozs7Ozs7Ozs7SUFHYjtJQUVBLHFCQUNFLDhEQUFDbUQ7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNyRCw2REFBTUE7Ozs7OzBCQUVQLDhEQUFDMkY7Z0JBQUt0QyxXQUFVOztrQ0FDZCw4REFBQ3VDO3dCQUFHdkMsV0FBVTtrQ0FBd0M7Ozs7OztrQ0FFdEQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3dDOzRDQUFHeEMsV0FBVTtzREFBOEI7Ozs7Ozs7Ozs7O2tEQUc5Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1p6QyxjQUFja0IsTUFBTSxLQUFLLGtCQUN4Qiw4REFBQ3NCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2xELHNKQUF1QkE7b0RBQUNrRCxXQUFVOzs7Ozs7OERBQ25DLDhEQUFDeUM7OERBQUU7Ozs7Ozs4REFDSCw4REFBQ0E7b0RBQUV6QyxXQUFVOzhEQUFlOzs7Ozs7Ozs7OzttREFLOUJ6QyxjQUFjbUYsR0FBRyxDQUFDLENBQUM3Qiw2QkFDakIsOERBQUM4QjtnREFJQ0MsU0FBUyxJQUFNaEMsbUJBQW1CQztnREFDbENiLFdBQVcsQ0FBQyxrRkFBa0YsRUFDNUZ2Qyx5QkFBeUJvRCxhQUFhbkMsU0FBUyxDQUFDQyxFQUFFLEdBQzlDLGtCQUNBLElBQ0o7MERBRUYsNEVBQUNvQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNaYSxhQUFhbkMsU0FBUyxDQUFDbUUsTUFBTSxpQkFDNUIsOERBQUNDO2dFQUNDQyxLQUFLbEMsYUFBYW5DLFNBQVMsQ0FBQ21FLE1BQU07Z0VBQ2xDRyxLQUFLbkMsYUFBYW5DLFNBQVMsQ0FBQ3VFLElBQUk7Z0VBQ2hDakQsV0FBVTs7Ozs7cUZBR1osOERBQUNqRCxzSkFBUUE7Z0VBQUNpRCxXQUFVOzs7Ozs7Ozs7OztzRUFJeEIsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDeUM7NEVBQUV6QyxXQUFVO3NGQUNWYSxhQUFhbkMsU0FBUyxDQUFDdUUsSUFBSTs7Ozs7O3dFQUU3QnBDLGFBQWFxQyxXQUFXLEdBQUcsbUJBQzFCLDhEQUFDQzs0RUFBS25ELFdBQVU7c0ZBQ2JhLGFBQWFxQyxXQUFXOzs7Ozs7Ozs7Ozs7Z0VBSzlCckMsYUFBYXVDLEVBQUUsa0JBQ2QsOERBQUNYO29FQUFFekMsV0FBVTs4RUFDVmEsYUFBYXVDLEVBQUUsQ0FBQ0MsS0FBSzs7Ozs7OzhFQUkxQiw4REFBQ1o7b0VBQUV6QyxXQUFVOzhFQUNWYSxhQUFhSixPQUFPOzs7Ozs7OEVBR3ZCLDhEQUFDVjtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNoRCxzSkFBU0E7NEVBQUNnRCxXQUFVOzs7Ozs7c0ZBQ3JCLDhEQUFDbUQ7NEVBQUtuRCxXQUFVO3NGQUNia0IsV0FBV0wsYUFBYXlDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQ0FoRHJDLEdBQUd6QyxhQUFhbkMsU0FBUyxDQUFDQyxFQUFFLENBQUMsQ0FBQyxFQUNqQ2tDLGFBQWFoQyxJQUFJLElBQUksV0FDckI7Ozs7Ozs7Ozs7Ozs7Ozs7MENBMERaLDhEQUFDa0I7Z0NBQUlDLFdBQVU7MENBQ1p2QyxxQ0FDQzs7c0RBRUUsOERBQUNzQzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ2pELHNKQUFRQTs0REFBQ2lELFdBQVU7Ozs7Ozs7Ozs7O2tFQUV0Qiw4REFBQ0Q7OzBFQUNDLDhEQUFDMEM7Z0VBQUV6QyxXQUFVOzBFQUVUekMsY0FBY2dHLElBQUksQ0FDaEIsQ0FBQ0MsSUFBTUEsRUFBRTlFLFNBQVMsQ0FBQ0MsRUFBRSxLQUFLbEIsdUJBQ3pCaUIsVUFBVXVFOzs7Ozs7NERBR2hCM0YsYUFBYXVDLEdBQUcsQ0FBQyx1QkFDaEIsOERBQUM0QztnRUFBRXpDLFdBQVU7O29FQUF3QjtvRUFDOUI7b0VBRUh6QyxjQUFjZ0csSUFBSSxDQUNoQixDQUFDQyxJQUFNQSxFQUFFOUUsU0FBUyxDQUFDQyxFQUFFLEtBQUtsQix1QkFDekIyRixJQUFJQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQVNuQiw4REFBQ3REOzRDQUFJQyxXQUFVOztnREFDWnJDLFNBQVMrRSxHQUFHLENBQUMsQ0FBQ2Usd0JBQ2IsOERBQUMxRDt3REFFQ0MsV0FBVyxDQUFDLEtBQUssRUFDZnlELFFBQVFDLE1BQU0sS0FBS3ZHLFNBQVN3RyxNQUFNaEYsS0FDOUIsZ0JBQ0EsaUJBQ0o7a0VBRUYsNEVBQUNvQjs0REFDQ0MsV0FBVyxDQUFDLDBDQUEwQyxFQUNwRHlELFFBQVFDLE1BQU0sS0FBS3ZHLFNBQVN3RyxNQUFNaEYsS0FDOUIsOEJBQ0EsNkJBQ0o7OzhFQUVGLDhEQUFDOEQ7b0VBQUV6QyxXQUFVOzhFQUFXeUQsUUFBUWhELE9BQU87Ozs7Ozs4RUFDdkMsOERBQUNnQztvRUFDQ3pDLFdBQVcsQ0FBQyxhQUFhLEVBQ3ZCeUQsUUFBUUMsTUFBTSxLQUFLdkcsU0FBU3dHLE1BQU1oRixLQUM5QixxQkFDQSxpQkFDSjs4RUFFRHVDLFdBQVd1QyxRQUFRSCxTQUFTOzs7Ozs7Ozs7Ozs7dURBdEI1QkcsUUFBUTlFLEVBQUU7Ozs7OzhEQTJCbkIsOERBQUNvQjtvREFBSTZELEtBQUt6Rjs7Ozs7Ozs7Ozs7O3NEQUlaLDhEQUFDNEI7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzZEO3dEQUNDQyxNQUFLO3dEQUNMQyxPQUFPbEc7d0RBQ1BtRyxVQUFVLENBQUNDLElBQU1uRyxjQUFjbUcsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dEQUM3Q0ksWUFBWSxDQUFDRixJQUFNQSxFQUFFRyxHQUFHLEtBQUssV0FBV25FO3dEQUN4Q29FLGFBQVk7d0RBQ1pyRSxXQUFVO3dEQUNWc0UsVUFBVXJHOzs7Ozs7a0VBRVosOERBQUMwRTt3REFDQ0MsU0FBUzNDO3dEQUNUcUUsVUFBVSxDQUFDekcsV0FBV3FDLElBQUksTUFBTWpDO3dEQUNoQytCLFdBQVU7a0VBRVYsNEVBQUNuRCxzSkFBaUJBOzREQUFDbUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7aUVBTXJDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDbEQsc0pBQXVCQTtnREFBQ2tELFdBQVU7Ozs7OzswREFDbkMsOERBQUN5QztnREFBRXpDLFdBQVU7MERBQXNCOzs7Ozs7MERBR25DLDhEQUFDeUM7Z0RBQUV6QyxXQUFVOzBEQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVV4Qyw4REFBQ3BELDZEQUFNQTs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxzcmNcXGFwcFxcbWVzc2FnZXNcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCI7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCB7IEhlYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlclwiO1xuaW1wb3J0IHsgRm9vdGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyXCI7XG5pbXBvcnQgeyBQbGFjZWhvbGRlckltYWdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9QbGFjZWhvbGRlckltYWdlXCI7XG5pbXBvcnQge1xuICBQYXBlckFpcnBsYW5lSWNvbixcbiAgQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24sXG4gIFVzZXJJY29uLFxuICBDbG9ja0ljb24sXG59IGZyb20gXCJAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmVcIjtcblxuaW50ZXJmYWNlIE1lc3NhZ2Uge1xuICBpZDogc3RyaW5nO1xuICBjb250ZW50OiBzdHJpbmc7XG4gIGlzUmVhZDogYm9vbGVhbjtcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIGZyb21JZDogc3RyaW5nO1xuICB0b0lkOiBzdHJpbmc7XG4gIGFkSWQ/OiBzdHJpbmc7XG4gIGZyb206IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBhdmF0YXI/OiBzdHJpbmc7XG4gIH07XG4gIHRvOiB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgYXZhdGFyPzogc3RyaW5nO1xuICB9O1xuICBhZD86IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIHRpdGxlOiBzdHJpbmc7XG4gICAgcHJpY2U6IG51bWJlcjtcbiAgICBpbWFnZVVybHM6IHN0cmluZ1tdO1xuICB9O1xufVxuXG5pbnRlcmZhY2UgQ29udmVyc2F0aW9uIHtcbiAgaWQ6IHN0cmluZztcbiAgY29udGVudDogc3RyaW5nO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgYWRJZD86IHN0cmluZztcbiAgb3RoZXJVc2VyOiB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgYXZhdGFyPzogc3RyaW5nO1xuICB9O1xuICBhZD86IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIHRpdGxlOiBzdHJpbmc7XG4gICAgcHJpY2U6IG51bWJlcjtcbiAgICBpbWFnZVVybHM6IHN0cmluZ1tdO1xuICB9O1xuICB1bnJlYWRDb3VudDogbnVtYmVyO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNZXNzYWdlc1BhZ2UoKSB7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiwgc3RhdHVzIH0gPSB1c2VTZXNzaW9uKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcblxuICBjb25zdCBbY29udmVyc2F0aW9ucywgc2V0Q29udmVyc2F0aW9uc10gPSB1c2VTdGF0ZTxDb252ZXJzYXRpb25bXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWRDb252ZXJzYXRpb24sIHNldFNlbGVjdGVkQ29udmVyc2F0aW9uXSA9IHVzZVN0YXRlPFxuICAgIHN0cmluZyB8IG51bGxcbiAgPihudWxsKTtcbiAgY29uc3QgW21lc3NhZ2VzLCBzZXRNZXNzYWdlc10gPSB1c2VTdGF0ZTxNZXNzYWdlW10+KFtdKTtcbiAgY29uc3QgW25ld01lc3NhZ2UsIHNldE5ld01lc3NhZ2VdID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbc2VuZGluZywgc2V0U2VuZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgbWVzc2FnZXNFbmRSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuXG4gIC8vINiq2LnYsdmK2YEg2KfZhNiv2YjYp9mEXG4gIGNvbnN0IGZldGNoQ29udmVyc2F0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcIi9hcGkvbWVzc2FnZXNcIik7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldENvbnZlcnNhdGlvbnMoZGF0YS5kYXRhKTtcbiAgICAgICAgLy8g2KXYsNinINmE2YUg2KrZg9mGINmH2YbYp9mDINmF2K3Yp9iv2KvYqSDZhdit2K/Yr9ipINmI2YfZhtin2YMg2YXYrdin2K/Yq9in2Kog2YXYqtin2K3YqdiMINin2K7YqtixINin2YTYo9mI2YTZiVxuICAgICAgICBpZiAoIXNlbGVjdGVkQ29udmVyc2F0aW9uICYmIGRhdGEuZGF0YS5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0U2VsZWN0ZWRDb252ZXJzYXRpb24oZGF0YS5kYXRhWzBdLm90aGVyVXNlci5pZCk7XG4gICAgICAgICAgZmV0Y2hNZXNzYWdlcyhkYXRhLmRhdGFbMF0ub3RoZXJVc2VyLmlkLCBkYXRhLmRhdGFbMF0uYWRJZCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGNvbnZlcnNhdGlvbnM6XCIsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoTWVzc2FnZXMgPSBhc3luYyAodXNlcklkOiBzdHJpbmcsIGFkSWQ/OiBzdHJpbmcgfCBudWxsKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVybCA9IG5ldyBVUkwoXCIvYXBpL21lc3NhZ2VzXCIsIHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pO1xuICAgICAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoXCJ3aXRoXCIsIHVzZXJJZCk7XG4gICAgICBpZiAoYWRJZCkgdXJsLnNlYXJjaFBhcmFtcy5zZXQoXCJhZElkXCIsIGFkSWQpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybC50b1N0cmluZygpKTtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0TWVzc2FnZXMoZGF0YS5kYXRhKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIG1lc3NhZ2VzOlwiLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHNjcm9sbFRvQm90dG9tID0gKCkgPT4ge1xuICAgIG1lc3NhZ2VzRW5kUmVmLmN1cnJlbnQ/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6IFwic21vb3RoXCIgfSk7XG4gIH07XG5cbiAgLy8g2KzZhdmK2LkgdXNlRWZmZWN0INmK2KzYqCDYo9mGINiq2YPZiNmGINmB2Yog2KfZhNij2LnZhNmJINmC2KjZhCDYo9mKIHJldHVybiDZhdi02LHZiNi3XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHN0YXR1cyA9PT0gXCJhdXRoZW50aWNhdGVkXCIpIHtcbiAgICAgIGZldGNoQ29udmVyc2F0aW9ucygpO1xuXG4gICAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDZhdit2KfYr9ir2Kkg2YXYrdiv2K/YqSDZgdmKIFVSTFxuICAgICAgY29uc3Qgd2l0aFVzZXIgPSBzZWFyY2hQYXJhbXMuZ2V0KFwid2l0aFwiKSB8fCBzZWFyY2hQYXJhbXMuZ2V0KFwidXNlclwiKTtcbiAgICAgIGNvbnN0IGFkSWQgPSBzZWFyY2hQYXJhbXMuZ2V0KFwiYWRcIik7XG5cbiAgICAgIGlmICh3aXRoVXNlcikge1xuICAgICAgICBzZXRTZWxlY3RlZENvbnZlcnNhdGlvbih3aXRoVXNlcik7XG4gICAgICAgIGZldGNoTWVzc2FnZXMod2l0aFVzZXIsIGFkSWQpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3NlYXJjaFBhcmFtcywgc3RhdHVzXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzY3JvbGxUb0JvdHRvbSgpO1xuICB9LCBbbWVzc2FnZXNdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdGF0dXMgPT09IFwidW5hdXRoZW50aWNhdGVkXCIpIHtcbiAgICAgIHJvdXRlci5wdXNoKFwiL2F1dGgvbG9naW5cIik7XG4gICAgfVxuICB9LCBbc3RhdHVzLCByb3V0ZXJdKTtcblxuICAvLyDYpdi52KfYr9ipINiq2YjYrNmK2Ycg2KXYsNinINmE2YUg2YrZg9mGINin2YTZhdiz2KrYrtiv2YUg2YXYs9is2YQg2K/YrtmI2YRcbiAgaWYgKHN0YXR1cyA9PT0gXCJsb2FkaW5nXCIpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAg2KzYp9ix2Yog2KfZhNiq2K3ZhdmK2YQuLi5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoc3RhdHVzID09PSBcInVuYXV0aGVudGljYXRlZFwiKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICBjb25zdCBzZW5kTWVzc2FnZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIW5ld01lc3NhZ2UudHJpbSgpIHx8ICFzZWxlY3RlZENvbnZlcnNhdGlvbiB8fCBzZW5kaW5nKSByZXR1cm47XG5cbiAgICBzZXRTZW5kaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBhZElkID0gc2VhcmNoUGFyYW1zLmdldChcImFkXCIpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFwiL2FwaS9tZXNzYWdlc1wiLCB7XG4gICAgICAgIG1ldGhvZDogXCJQT1NUXCIsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHRvSWQ6IHNlbGVjdGVkQ29udmVyc2F0aW9uLFxuICAgICAgICAgIGNvbnRlbnQ6IG5ld01lc3NhZ2UudHJpbSgpLFxuICAgICAgICAgIC4uLihhZElkICYmIHsgYWRJZCB9KSxcbiAgICAgICAgfSksXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRNZXNzYWdlcygocHJldikgPT4gWy4uLnByZXYsIGRhdGEuZGF0YV0pO1xuICAgICAgICBzZXROZXdNZXNzYWdlKFwiXCIpO1xuICAgICAgICBmZXRjaENvbnZlcnNhdGlvbnMoKTsgLy8g2KrYrdiv2YrYqyDZgtin2KbZhdipINin2YTZhdit2KfYr9ir2KfYqlxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWxlcnQoZGF0YS5lcnJvciB8fCBcItit2K/YqyDYrti32KMg2YHZiiDYpdix2LPYp9mEINin2YTYsdiz2KfZhNipXCIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc2VuZGluZyBtZXNzYWdlOlwiLCBlcnJvcik7XG4gICAgICBhbGVydChcItit2K/YqyDYrti32KMg2YHZiiDYpdix2LPYp9mEINin2YTYsdiz2KfZhNipXCIpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTZW5kaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc2VsZWN0Q29udmVyc2F0aW9uID0gKGNvbnZlcnNhdGlvbjogQ29udmVyc2F0aW9uKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRDb252ZXJzYXRpb24oY29udmVyc2F0aW9uLm90aGVyVXNlci5pZCk7XG4gICAgZmV0Y2hNZXNzYWdlcyhjb252ZXJzYXRpb24ub3RoZXJVc2VyLmlkLCBjb252ZXJzYXRpb24uYWRJZCk7XG5cbiAgICAvLyDYqtit2K/ZitirIFVSTFxuICAgIGNvbnN0IHVybCA9IG5ldyBVUkwod2luZG93LmxvY2F0aW9uLmhyZWYpO1xuICAgIHVybC5zZWFyY2hQYXJhbXMuc2V0KFwid2l0aFwiLCBjb252ZXJzYXRpb24ub3RoZXJVc2VyLmlkKTtcbiAgICBpZiAoY29udmVyc2F0aW9uLmFkSWQpIHtcbiAgICAgIHVybC5zZWFyY2hQYXJhbXMuc2V0KFwiYWRcIiwgY29udmVyc2F0aW9uLmFkSWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICB1cmwuc2VhcmNoUGFyYW1zLmRlbGV0ZShcImFkXCIpO1xuICAgIH1cbiAgICB3aW5kb3cuaGlzdG9yeS5wdXNoU3RhdGUoe30sIFwiXCIsIHVybC50b1N0cmluZygpKTtcbiAgfTtcblxuICBjb25zdCBmb3JtYXRUaW1lID0gKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IGRpZmZUaW1lID0gTWF0aC5hYnMobm93LmdldFRpbWUoKSAtIGRhdGUuZ2V0VGltZSgpKTtcbiAgICBjb25zdCBkaWZmRGF5cyA9IE1hdGguY2VpbChkaWZmVGltZSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7XG5cbiAgICBpZiAoZGlmZkRheXMgPT09IDEpIHtcbiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZyhcImFyLVNZXCIsIHtcbiAgICAgICAgaG91cjogXCIyLWRpZ2l0XCIsXG4gICAgICAgIG1pbnV0ZTogXCIyLWRpZ2l0XCIsXG4gICAgICB9KTtcbiAgICB9IGVsc2UgaWYgKGRpZmZEYXlzIDwgNykge1xuICAgICAgcmV0dXJuIGAke2RpZmZEYXlzfSDYo9mK2KfZhWA7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZyhcImFyLVNZXCIpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXRQcmljZSA9IChwcmljZTogbnVtYmVyKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdChcImFyLVNZXCIpLmZvcm1hdChwcmljZSk7XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctYmFja2dyb3VuZFwiPlxuICAgICAgICA8SGVhZGVyIC8+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggYmctZ3JheS0yMDAgcm91bmRlZCB3LTEvNCBtYi02XCI+PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTk2IGJnLWdyYXktMjAwIHJvdW5kZWQtbGdcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yIGgtOTYgYmctZ3JheS0yMDAgcm91bmRlZC1sZ1wiPjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8Rm9vdGVyIC8+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICA8SGVhZGVyIC8+XG5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWRhcmstODAwIG1iLTZcIj7Yp9mE2LHYs9in2KbZhDwvaDE+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC02IGgtWzYwMHB4XVwiPlxuICAgICAgICAgIHsvKiDZgtin2KbZhdipINin2YTZhdit2KfYr9ir2KfYqiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1kYXJrLTgwMFwiPtin2YTZhdit2KfYr9ir2KfYqjwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy15LWF1dG8gaC1mdWxsXCI+XG4gICAgICAgICAgICAgIHtjb252ZXJzYXRpb25zLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiB0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICA8Q2hhdEJ1YmJsZUxlZnRSaWdodEljb24gY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItMyB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxwPtmE2Kcg2KrZiNis2K8g2YXYrdin2K/Yq9in2Kog2K3YqtmJINin2YTYotmGPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgINin2KjYr9ijINmF2K3Yp9iv2KvYqSDZhdmGINiu2YTYp9mEINin2YTYqtmI2KfYtdmEINmF2Lkg2KjYp9im2LlcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBjb252ZXJzYXRpb25zLm1hcCgoY29udmVyc2F0aW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGtleT17YCR7Y29udmVyc2F0aW9uLm90aGVyVXNlci5pZH0tJHtcbiAgICAgICAgICAgICAgICAgICAgICBjb252ZXJzYXRpb24uYWRJZCB8fCBcImdlbmVyYWxcIlxuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2VsZWN0Q29udmVyc2F0aW9uKGNvbnZlcnNhdGlvbil9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBwLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwIGhvdmVyOmJnLWdyYXktNTAgdGV4dC1yaWdodCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ29udmVyc2F0aW9uID09PSBjb252ZXJzYXRpb24ub3RoZXJVc2VyLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctcHJpbWFyeS01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwiXCJcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTMgc3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctcHJpbWFyeS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb252ZXJzYXRpb24ub3RoZXJVc2VyLmF2YXRhciA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17Y29udmVyc2F0aW9uLm90aGVyVXNlci5hdmF0YXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXtjb252ZXJzYXRpb24ub3RoZXJVc2VyLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCByb3VuZGVkLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxVc2VySWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHJpbWFyeS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1kYXJrLTgwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb252ZXJzYXRpb24ub3RoZXJVc2VyLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbnZlcnNhdGlvbi51bnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLXByaW1hcnktNTAwIHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkLWZ1bGwgcHgtMiBweS0xIG1pbi13LVsyMHB4XSB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbnZlcnNhdGlvbi51bnJlYWRDb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAge2NvbnZlcnNhdGlvbi5hZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCB0cnVuY2F0ZSBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbnZlcnNhdGlvbi5hZC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb252ZXJzYXRpb24uY29udGVudH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDbG9ja0ljb24gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWdyYXktNDAwIG1sLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0VGltZShjb252ZXJzYXRpb24uY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qINmF2YbYt9mC2Kkg2KfZhNmF2K3Yp9iv2KvYqSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTIgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICB7c2VsZWN0ZWRDb252ZXJzYXRpb24gPyAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgey8qINix2KPYsyDYp9mE2YXYrdin2K/Yq9ipICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgc3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLXByaW1hcnktMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxVc2VySWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHJpbWFyeS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWRhcmstODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnZlcnNhdGlvbnMuZmluZChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYykgPT4gYy5vdGhlclVzZXIuaWQgPT09IHNlbGVjdGVkQ29udmVyc2F0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICk/Lm90aGVyVXNlci5uYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIHtzZWFyY2hQYXJhbXMuZ2V0KFwiYWRcIikgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgINit2YjZhDp7XCIgXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb252ZXJzYXRpb25zLmZpbmQoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYykgPT4gYy5vdGhlclVzZXIuaWQgPT09IHNlbGVjdGVkQ29udmVyc2F0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKT8uYWQ/LnRpdGxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qINin2YTYsdiz2KfYptmEICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTQgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e21lc3NhZ2UuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZS5mcm9tSWQgPT09IHNlc3Npb24/LnVzZXI/LmlkXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJqdXN0aWZ5LWVuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJqdXN0aWZ5LXN0YXJ0XCJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YG1heC13LXhzIGxnOm1heC13LW1kIHB4LTQgcHktMiByb3VuZGVkLWxnICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2UuZnJvbUlkID09PSBzZXNzaW9uPy51c2VyPy5pZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1wcmltYXJ5LTUwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctZ3JheS0xMDAgdGV4dC1kYXJrLTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e21lc3NhZ2UuY29udGVudH08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cFxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXhzIG10LTEgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlLmZyb21JZCA9PT0gc2Vzc2lvbj8udXNlcj8uaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXByaW1hcnktMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRUaW1lKG1lc3NhZ2UuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDxkaXYgcmVmPXttZXNzYWdlc0VuZFJlZn0gLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiDYpdix2LPYp9mEINix2LPYp9mE2KkgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld01lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbktleVByZXNzPXsoZSkgPT4gZS5rZXkgPT09IFwiRW50ZXJcIiAmJiBzZW5kTWVzc2FnZSgpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KfZg9iq2Kgg2LHYs9in2YTYqtmDLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzZW5kaW5nfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17c2VuZE1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFuZXdNZXNzYWdlLnRyaW0oKSB8fCBzZW5kaW5nfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1wcmltYXJ5LTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctcHJpbWFyeS02MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFBhcGVyQWlycGxhbmVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8Q2hhdEJ1YmJsZUxlZnRSaWdodEljb24gY2xhc3NOYW1lPVwiaC0xNiB3LTE2IG14LWF1dG8gbWItNCB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAg2KfYrtiq2LEg2YXYrdin2K/Yq9ipINmE2KjYr9ihINin2YTZhdix2KfYs9mE2KlcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICDYp9iu2KrYsSDZhdit2KfYr9ir2Kkg2YXZhiDYp9mE2YLYp9im2YXYqSDYp9mE2KzYp9mG2KjZitipXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG5cbiAgICAgIDxGb290ZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVNlc3Npb24iLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJIZWFkZXIiLCJGb290ZXIiLCJQYXBlckFpcnBsYW5lSWNvbiIsIkNoYXRCdWJibGVMZWZ0UmlnaHRJY29uIiwiVXNlckljb24iLCJDbG9ja0ljb24iLCJNZXNzYWdlc1BhZ2UiLCJkYXRhIiwic2Vzc2lvbiIsInN0YXR1cyIsInJvdXRlciIsInNlYXJjaFBhcmFtcyIsImNvbnZlcnNhdGlvbnMiLCJzZXRDb252ZXJzYXRpb25zIiwic2VsZWN0ZWRDb252ZXJzYXRpb24iLCJzZXRTZWxlY3RlZENvbnZlcnNhdGlvbiIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJuZXdNZXNzYWdlIiwic2V0TmV3TWVzc2FnZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2VuZGluZyIsInNldFNlbmRpbmciLCJtZXNzYWdlc0VuZFJlZiIsImZldGNoQ29udmVyc2F0aW9ucyIsInJlc3BvbnNlIiwiZmV0Y2giLCJqc29uIiwic3VjY2VzcyIsImxlbmd0aCIsIm90aGVyVXNlciIsImlkIiwiZmV0Y2hNZXNzYWdlcyIsImFkSWQiLCJlcnJvciIsImNvbnNvbGUiLCJ1c2VySWQiLCJ1cmwiLCJVUkwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsIm9yaWdpbiIsInNldCIsInRvU3RyaW5nIiwic2Nyb2xsVG9Cb3R0b20iLCJjdXJyZW50Iiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsIndpdGhVc2VyIiwiZ2V0IiwicHVzaCIsImRpdiIsImNsYXNzTmFtZSIsInNlbmRNZXNzYWdlIiwidHJpbSIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInRvSWQiLCJjb250ZW50IiwicHJldiIsImFsZXJ0Iiwic2VsZWN0Q29udmVyc2F0aW9uIiwiY29udmVyc2F0aW9uIiwiaHJlZiIsImRlbGV0ZSIsImhpc3RvcnkiLCJwdXNoU3RhdGUiLCJmb3JtYXRUaW1lIiwiZGF0ZVN0cmluZyIsImRhdGUiLCJEYXRlIiwibm93IiwiZGlmZlRpbWUiLCJNYXRoIiwiYWJzIiwiZ2V0VGltZSIsImRpZmZEYXlzIiwiY2VpbCIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJmb3JtYXRQcmljZSIsInByaWNlIiwiSW50bCIsIk51bWJlckZvcm1hdCIsImZvcm1hdCIsIm1haW4iLCJoMSIsImgyIiwicCIsIm1hcCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJhdmF0YXIiLCJpbWciLCJzcmMiLCJhbHQiLCJuYW1lIiwidW5yZWFkQ291bnQiLCJzcGFuIiwiYWQiLCJ0aXRsZSIsImNyZWF0ZWRBdCIsImZpbmQiLCJjIiwibWVzc2FnZSIsImZyb21JZCIsInVzZXIiLCJyZWYiLCJpbnB1dCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9uS2V5UHJlc3MiLCJrZXkiLCJwbGFjZWhvbGRlciIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/messages/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-dark-800 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 space-x-reverse mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"خ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 12,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 11,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"خَلّيها تنْباع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 15,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"منصة البيع والشراء\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 16,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 14,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 max-w-md\",\n                                    children: \"منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"فيسبوك\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 27,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 26,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"تلغرام\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 33,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"واتساب\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 39,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"روابط سريعة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"من نحن\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/how-it-works\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"كيف يعمل الموقع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/safety-tips\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"نصائح الأمان\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/pricing\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"باقات الإعلانات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"اتصل بنا\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"الدعم والمساعدة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/help\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"مركز المساعدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/terms\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"شروط الاستخدام\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/privacy\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"سياسة الخصوصية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/report\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"الإبلاغ عن مشكلة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                                children: \"الأسئلة الشائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-700 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center md:text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"البريد الإلكتروني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"رقم الهاتف\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        dir: \"ltr\",\n                                        children: \"+963 11 123 4567\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center md:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"العنوان\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: \"دمشق، سوريا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-700 mt-8 pt-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"\\xa9 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-sm mt-2\",\n                            children: \"تم التطوير بـ ❤️ في سوريا\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,ChatBubbleLeftRightIcon,HeartIcon,MagnifyingGlassIcon,PlusIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-lg\",\n                                        children: \"خ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-dark-800\",\n                                            children: \"خَلّيها تنْباع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-dark-500\",\n                                            children: \"منصة البيع والشراء\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 max-w-lg mx-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"ابحث عن أي شيء...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 space-x-reverse\",\n                            children: [\n                                session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/ads/new\",\n                                            className: \"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"نشر إعلان\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/messages\",\n                                            className: \"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/favorites\",\n                                            className: \"p-2 text-dark-600 hover:text-primary-500 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"p-2 text-dark-600 hover:text-primary-500 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                                    className: \"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden sm:inline\",\n                                                            children: session.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/profile\",\n                                                            className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50\",\n                                                            children: \"الملف الشخصي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/profile/ads\",\n                                                            className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50\",\n                                                            children: \"إعلاناتي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/profile/settings\",\n                                                            className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50\",\n                                                            children: \"الإعدادات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                            className: \"my-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)(),\n                                                            className: \"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\",\n                                                            children: \"تسجيل الخروج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/login\",\n                                            className: \"text-dark-600 hover:text-primary-500 transition-colors\",\n                                            children: \"تسجيل الدخول\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/register\",\n                                            className: \"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\",\n                                            children: \"إنشاء حساب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    className: \"sm:hidden p-2 text-dark-600\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_ChatBubbleLeftRightIcon_HeartIcon_MagnifyingGlassIcon_PlusIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:hidden border-t border-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ads\",\n                                className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\",\n                                children: \"جميع الإعلانات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/categories\",\n                                className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\",\n                                children: \"جميع الفئات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this),\n                            session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/ads/new\",\n                                        className: \"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\",\n                                        children: \"نشر إعلان جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/messages\",\n                                        className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\",\n                                        children: \"الرسائل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/favorites\",\n                                        className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\",\n                                        children: \"المفضلة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\",\n                                        children: \"لوحة التحكم\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\components\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFaUQ7QUFNMUMsU0FBU0MsYUFBYSxFQUFFQyxRQUFRLEVBQXFCO0lBQzFELHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcc3JjXFxjb21wb25lbnRzXFxwcm92aWRlcnNcXEF1dGhQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogQXV0aFByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();