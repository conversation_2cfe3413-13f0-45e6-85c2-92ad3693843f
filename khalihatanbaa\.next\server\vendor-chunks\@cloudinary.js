"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@cloudinary";
exports.ids = ["vendor-chunks/@cloudinary"];
exports.modules = {

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/background/actions/BackgroundColor.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/actions/background/actions/BackgroundColor.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundColor: () => (/* binding */ BackgroundColor)\n/* harmony export */ });\n/* harmony import */ var _internal_Action_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../internal/Action.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js\");\n/* harmony import */ var _internal_qualifier_QualifierValue_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../internal/qualifier/QualifierValue.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js\");\n/* harmony import */ var _internal_qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../internal/qualifier/Qualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js\");\n/* harmony import */ var _internal_utils_prepareColor_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../internal/utils/prepareColor.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/prepareColor.js\");\n\n\n\n\n/**\n * @extends SDK.Action\n * @description A class for background transformations.\n */\nclass BackgroundColor extends _internal_Action_js__WEBPACK_IMPORTED_MODULE_0__.Action {\n    constructor(color) {\n        super();\n        this._actionModel = {};\n        this.addQualifier(new _internal_qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_1__.Qualifier('b', new _internal_qualifier_QualifierValue_js__WEBPACK_IMPORTED_MODULE_2__.QualifierValue((0,_internal_utils_prepareColor_js__WEBPACK_IMPORTED_MODULE_3__.prepareColor)(color)).setDelimiter('_')));\n        this._actionModel.color = color;\n        this._actionModel.actionType = 'backgroundColor';\n    }\n    static fromJson(actionModel) {\n        const { color } = actionModel;\n        // We are using this() to allow inheriting classes to use super.fromJson.apply(this, [actionModel])\n        // This allows the inheriting classes to determine the class to be created\n        const result = new this(color);\n        return result;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/background/actions/BackgroundColor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryAction.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryAction.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeliveryAction: () => (/* binding */ DeliveryAction)\n/* harmony export */ });\n/* harmony import */ var _internal_Action_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/Action.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js\");\n/* harmony import */ var _qualifiers_format_FormatQualifier_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../qualifiers/format/FormatQualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/format/FormatQualifier.js\");\n/* harmony import */ var _internal_qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/qualifier/Qualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js\");\n/* harmony import */ var _internal_internalConstants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/internalConstants.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/internalConstants.js\");\n\n\n\n\n/**\n * @description Qualifies the delivery of an asset.\n * @memberOf Actions.Delivery\n * @extends SDK.Action\n */\nclass DeliveryAction extends _internal_Action_js__WEBPACK_IMPORTED_MODULE_0__.Action {\n    /**\n     * @param {string} deliveryKey A generic Delivery Action Key (such as q, f, dn, etc.)\n     * @param {string} deliveryType A Format Qualifiers for the action, such as Quality.auto()\n     * @param {string} modelProperty internal model property of the action, for example quality uses `level` while dpr uses `density`\n     * @see Visit {@link Actions.Delivery|Delivery} for an example\n     */\n    constructor(deliveryKey, deliveryType, modelProperty) {\n        super();\n        this._actionModel = {};\n        let deliveryTypeValue;\n        if (deliveryType instanceof _qualifiers_format_FormatQualifier_js__WEBPACK_IMPORTED_MODULE_1__.FormatQualifier) {\n            deliveryTypeValue = deliveryType.getValue();\n        }\n        else {\n            deliveryTypeValue = deliveryType;\n        }\n        this._actionModel.actionType = _internal_internalConstants_js__WEBPACK_IMPORTED_MODULE_2__.DELIVERY_MODE_TO_ACTION_TYPE_MAP[deliveryKey];\n        this._actionModel[modelProperty] = deliveryTypeValue;\n        this.addQualifier(new _internal_qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_3__.Qualifier(deliveryKey, deliveryType));\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeliveryFormatAction: () => (/* binding */ DeliveryFormatAction)\n/* harmony export */ });\n/* harmony import */ var _qualifiers_flag_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../qualifiers/flag.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag.js\");\n/* harmony import */ var _DeliveryAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DeliveryAction.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryAction.js\");\n/* harmony import */ var _qualifiers_progressive_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../qualifiers/progressive.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/progressive.js\");\n\n\n\n/**\n * @memberOf Actions.Delivery\n * @extends {Actions.Delivery.DeliveryAction}\n * @see Visit {@link Actions.Delivery|Delivery} for an example\n */\nclass DeliveryFormatAction extends _DeliveryAction_js__WEBPACK_IMPORTED_MODULE_0__.DeliveryAction {\n    constructor(deliveryKey, deliveryType) {\n        super(deliveryKey, deliveryType, 'formatType');\n    }\n    /**\n     * @description Uses lossy compression when delivering animated GIF files.\n     * @return {this}\n     */\n    lossy() {\n        this._actionModel.lossy = true;\n        this.addFlag((0,_qualifiers_flag_js__WEBPACK_IMPORTED_MODULE_1__.lossy)());\n        return this;\n    }\n    /**\n     * @description Uses progressive compression when delivering JPG file format.\n     * @return {this}\n     */\n    progressive(mode) {\n        if (mode instanceof _qualifiers_progressive_js__WEBPACK_IMPORTED_MODULE_2__.ProgressiveQualifier) {\n            this._actionModel.progressive = { mode: mode.getFlagValue() };\n            this.addFlag(mode);\n        }\n        else {\n            this._actionModel.progressive = { mode: mode };\n            this.addFlag((0,_qualifiers_flag_js__WEBPACK_IMPORTED_MODULE_1__.progressive)(mode));\n        }\n        return this;\n    }\n    /**\n     * @description Ensures that images with a transparency channel are delivered in PNG format.\n     */\n    preserveTransparency() {\n        this._actionModel.preserveTransparency = true;\n        this.addFlag((0,_qualifiers_flag_js__WEBPACK_IMPORTED_MODULE_1__.preserveTransparency)());\n        return this;\n    }\n    static fromJson(actionModel) {\n        const { formatType, lossy, progressive, preserveTransparency } = actionModel;\n        let result;\n        if (formatType) {\n            result = new this('f', formatType);\n        }\n        else {\n            result = new this('f');\n        }\n        if (progressive) {\n            if (progressive.mode) {\n                result.progressive(progressive.mode);\n            }\n            else {\n                result.progressive();\n            }\n        }\n        lossy && result.lossy();\n        preserveTransparency && result.preserveTransparency();\n        return result;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action)\n/* harmony export */ });\n/* harmony import */ var _qualifiers_flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../qualifiers/flag/FlagQualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js\");\n/* harmony import */ var _qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./qualifier/Qualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js\");\n/* harmony import */ var _utils_dataStructureUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/dataStructureUtils.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/dataStructureUtils.js\");\n/* harmony import */ var _models_ActionModel_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./models/ActionModel.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/ActionModel.js\");\n\n\n\n\n/**\n * @summary SDK\n * @memberOf SDK\n * @description Defines the category of transformation to perform.\n */\nclass Action extends _models_ActionModel_js__WEBPACK_IMPORTED_MODULE_0__.ActionModel {\n    constructor() {\n        super(...arguments);\n        // We're using map, to overwrite existing keys. for example:\n        // addParam(w_100).addQualifier(w_200) should result in w_200. and not w_100,w_200\n        this.qualifiers = new Map();\n        // Unlike regular qualifiers, there can be multiple flags in each url component /fl_1,fl_2/\n        // If the falgs are added to the qualifiers map, only a single flag could exist in a component (it's a map)\n        // So flags are stored separately until the very end because of that reason\n        this.flags = [];\n        this.delimiter = ','; // {qualifier}{delimiter}{qualifier} for example: `${'w_100'}${','}${'c_fill'}`\n        this.actionTag = ''; // A custom name tag to identify this action in the future\n    }\n    prepareQualifiers() { }\n    /**\n     * @description Returns the custom name tag that was given to this action\n     * @return {string}\n     */\n    getActionTag() {\n        return this.actionTag;\n    }\n    /**\n     * @description Sets the custom name tag for this action\n     * @return {this}\n     */\n    setActionTag(tag) {\n        this.actionTag = tag;\n        return this;\n    }\n    /**\n     * @description Calls toString() on all child qualifiers (implicitly by using .join()).\n     * @return {string}\n     */\n    toString() {\n        this.prepareQualifiers();\n        return (0,_utils_dataStructureUtils_js__WEBPACK_IMPORTED_MODULE_1__.mapToSortedArray)(this.qualifiers, this.flags).join(this.delimiter);\n    }\n    /**\n     * @description Adds the parameter to the action.\n     * @param {SDK.Qualifier} qualifier\n     * @return {this}\n     */\n    addQualifier(qualifier) {\n        // if string, find the key and value\n        if (typeof qualifier === 'string') {\n            const [key, value] = qualifier.toLowerCase().split('_');\n            if (key === 'fl') {\n                // if string qualifier is a flag, store it in the flags arrays\n                this.flags.push(new _qualifiers_flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_2__.FlagQualifier(value));\n            }\n            else {\n                // if the string qualifier is not a flag, create a new qualifier from it\n                this.qualifiers.set(key, new _qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_3__.Qualifier(key, value));\n            }\n        }\n        else {\n            // if a qualifier object, insert to the qualifiers map\n            this.qualifiers.set(qualifier.key, qualifier);\n        }\n        return this;\n    }\n    /**\n     * @description Adds a flag to the current action.\n     * @param {Qualifiers.Flag} flag\n     * @return {this}\n     */\n    addFlag(flag) {\n        if (typeof flag === 'string') {\n            this.flags.push(new _qualifiers_flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_2__.FlagQualifier(flag));\n        }\n        else {\n            if (flag instanceof _qualifiers_flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_2__.FlagQualifier) {\n                this.flags.push(flag);\n            }\n        }\n        return this;\n    }\n    addValueToQualifier(qualifierKey, qualifierValue) {\n        this.qualifiers.get(qualifierKey).addValue(qualifierValue);\n        return this;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/RawAction.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/RawAction.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RawAction: () => (/* binding */ RawAction)\n/* harmony export */ });\n/* harmony import */ var _utils_unsupportedError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/unsupportedError.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js\");\n\n/**\n * @summary SDK\n * @memberOf SDK\n * @description Defines an action that's a string literal, no validations or manipulations are performed\n */\nclass RawAction {\n    constructor(raw) {\n        this.raw = raw;\n    }\n    toString() {\n        return this.raw;\n    }\n    toJson() {\n        return { error: (0,_utils_unsupportedError_js__WEBPACK_IMPORTED_MODULE_0__.createUnsupportedError)(`unsupported action ${this.constructor.name}`) };\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvUmF3QWN0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFFO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLE9BQU8sa0ZBQXNCLHVCQUF1QixzQkFBc0I7QUFDM0Y7QUFDQTtBQUNxQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxub2RlX21vZHVsZXNcXEBjbG91ZGluYXJ5XFx0cmFuc2Zvcm1hdGlvbi1idWlsZGVyLXNka1xcaW50ZXJuYWxcXFJhd0FjdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVVbnN1cHBvcnRlZEVycm9yIH0gZnJvbSBcIi4vdXRpbHMvdW5zdXBwb3J0ZWRFcnJvci5qc1wiO1xuLyoqXG4gKiBAc3VtbWFyeSBTREtcbiAqIEBtZW1iZXJPZiBTREtcbiAqIEBkZXNjcmlwdGlvbiBEZWZpbmVzIGFuIGFjdGlvbiB0aGF0J3MgYSBzdHJpbmcgbGl0ZXJhbCwgbm8gdmFsaWRhdGlvbnMgb3IgbWFuaXB1bGF0aW9ucyBhcmUgcGVyZm9ybWVkXG4gKi9cbmNsYXNzIFJhd0FjdGlvbiB7XG4gICAgY29uc3RydWN0b3IocmF3KSB7XG4gICAgICAgIHRoaXMucmF3ID0gcmF3O1xuICAgIH1cbiAgICB0b1N0cmluZygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucmF3O1xuICAgIH1cbiAgICB0b0pzb24oKSB7XG4gICAgICAgIHJldHVybiB7IGVycm9yOiBjcmVhdGVVbnN1cHBvcnRlZEVycm9yKGB1bnN1cHBvcnRlZCBhY3Rpb24gJHt0aGlzLmNvbnN0cnVjdG9yLm5hbWV9YCkgfTtcbiAgICB9XG59XG5leHBvcnQgeyBSYXdBY3Rpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/RawAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/internalConstants.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/internalConstants.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TYPE_TO_BLEND_MODE_MAP: () => (/* binding */ ACTION_TYPE_TO_BLEND_MODE_MAP),\n/* harmony export */   ACTION_TYPE_TO_CROP_MODE_MAP: () => (/* binding */ ACTION_TYPE_TO_CROP_MODE_MAP),\n/* harmony export */   ACTION_TYPE_TO_DELIVERY_MODE_MAP: () => (/* binding */ ACTION_TYPE_TO_DELIVERY_MODE_MAP),\n/* harmony export */   ACTION_TYPE_TO_EFFECT_MODE_MAP: () => (/* binding */ ACTION_TYPE_TO_EFFECT_MODE_MAP),\n/* harmony export */   ACTION_TYPE_TO_QUALITY_MODE_MAP: () => (/* binding */ ACTION_TYPE_TO_QUALITY_MODE_MAP),\n/* harmony export */   ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP: () => (/* binding */ ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP),\n/* harmony export */   CHROMA_MODEL_ENUM_TO_CHROMA_VALUE: () => (/* binding */ CHROMA_MODEL_ENUM_TO_CHROMA_VALUE),\n/* harmony export */   CHROMA_VALUE_TO_CHROMA_MODEL_ENUM: () => (/* binding */ CHROMA_VALUE_TO_CHROMA_MODEL_ENUM),\n/* harmony export */   COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP: () => (/* binding */ COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP),\n/* harmony export */   COLOR_SPACE_MODE_TO_COLOR_SPACE_MODEL_MODE_MAP: () => (/* binding */ COLOR_SPACE_MODE_TO_COLOR_SPACE_MODEL_MODE_MAP),\n/* harmony export */   CONDITIONAL_OPERATORS: () => (/* binding */ CONDITIONAL_OPERATORS),\n/* harmony export */   CROP_MODE_TO_ACTION_TYPE_MAP: () => (/* binding */ CROP_MODE_TO_ACTION_TYPE_MAP),\n/* harmony export */   DELIVERY_MODE_TO_ACTION_TYPE_MAP: () => (/* binding */ DELIVERY_MODE_TO_ACTION_TYPE_MAP),\n/* harmony export */   EFFECT_MODE_TO_ACTION_TYPE_MAP: () => (/* binding */ EFFECT_MODE_TO_ACTION_TYPE_MAP),\n/* harmony export */   QUALITY_MODE_TO_ACTION_TYPE_MAP: () => (/* binding */ QUALITY_MODE_TO_ACTION_TYPE_MAP),\n/* harmony export */   RESERVED_NAMES: () => (/* binding */ RESERVED_NAMES),\n/* harmony export */   STREAMING_PROFILE_TO_ACTION_TYPE_MAP: () => (/* binding */ STREAMING_PROFILE_TO_ACTION_TYPE_MAP)\n/* harmony export */ });\n/* harmony import */ var _utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/objectFlip.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/objectFlip.js\");\n/**\n * This file is for internal constants only.\n * It is not intended for public use and is not part of the public API\n */\n\nconst CONDITIONAL_OPERATORS = {\n    \"=\": \"eq\",\n    \"!=\": \"ne\",\n    \"<\": \"lt\",\n    \">\": \"gt\",\n    \"<=\": \"lte\",\n    \">=\": \"gte\",\n    \"&&\": \"and\",\n    \"||\": \"or\",\n    \"*\": \"mul\",\n    \"/\": \"div\",\n    \"+\": \"add\",\n    \"-\": \"sub\",\n    \"^\": \"pow\"\n};\nconst RESERVED_NAMES = {\n    \"aspect_ratio\": \"ar\",\n    \"aspectRatio\": \"ar\",\n    \"current_page\": \"cp\",\n    \"currentPage\": \"cp\",\n    \"duration\": \"du\",\n    \"face_count\": \"fc\",\n    \"faceCount\": \"fc\",\n    \"height\": \"h\",\n    \"initial_aspect_ratio\": \"iar\",\n    \"initial_height\": \"ih\",\n    \"initial_width\": \"iw\",\n    \"initialAspectRatio\": \"iar\",\n    \"initialHeight\": \"ih\",\n    \"initialWidth\": \"iw\",\n    \"initial_duration\": \"idu\",\n    \"initialDuration\": \"idu\",\n    \"page_count\": \"pc\",\n    \"page_x\": \"px\",\n    \"page_y\": \"py\",\n    \"pageCount\": \"pc\",\n    \"pageX\": \"px\",\n    \"pageY\": \"py\",\n    \"tags\": \"tags\",\n    \"width\": \"w\",\n    \"trimmed_aspect_ratio\": \"tar\",\n    \"current_public_id\": \"cpi\",\n    \"initial_density\": \"idn\",\n    \"page_names\": \"pgnames\"\n};\nconst ACTION_TYPE_TO_CROP_MODE_MAP = {\n    limitFit: 'limit',\n    limitFill: 'lfill',\n    minimumFit: 'mfit',\n    thumbnail: 'thumb',\n    limitPad: 'lpad',\n    minimumPad: 'mpad',\n    autoPad: 'auto_pad'\n};\nconst ACTION_TYPE_TO_DELIVERY_MODE_MAP = {\n    colorSpace: 'cs',\n    dpr: 'dpr',\n    density: 'dn',\n    defaultImage: 'd',\n    format: 'f',\n    quality: 'q'\n};\nconst ACTION_TYPE_TO_EFFECT_MODE_MAP = {\n    redEye: 'redeye',\n    advancedRedEye: 'adv_redeye',\n    oilPaint: 'oil_paint',\n    unsharpMask: 'unsharp_mask',\n    makeTransparent: 'make_transparent',\n    generativeRestore: 'gen_restore',\n    upscale: 'upscale'\n};\nconst ACTION_TYPE_TO_QUALITY_MODE_MAP = {\n    autoBest: 'auto:best',\n    autoEco: 'auto:eco',\n    autoGood: 'auto:good',\n    autoLow: 'auto:low',\n    jpegminiHigh: 'jpegmini:1',\n    jpegminiMedium: 'jpegmini:2',\n    jpegminiBest: 'jpegmini:0'\n};\nconst ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP = {\n    fullHd: 'full_hd',\n    fullHdWifi: 'full_hd_wifi',\n    fullHdLean: 'full_hd_lean',\n    hdLean: 'hd_lean'\n};\nconst CHROMA_VALUE_TO_CHROMA_MODEL_ENUM = {\n    444: \"CHROMA_444\",\n    420: \"CHROMA_420\"\n};\nconst COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP = {\n    'noCmyk': 'no_cmyk',\n    'keepCmyk': 'keep_cmyk',\n    'tinySrgb': 'tinysrgb',\n    'srgbTrueColor': 'srgb:truecolor'\n};\nconst ACTION_TYPE_TO_BLEND_MODE_MAP = {\n    'antiRemoval': 'anti_removal'\n};\nconst CHROMA_MODEL_ENUM_TO_CHROMA_VALUE = (0,_utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__.objectFlip)(CHROMA_VALUE_TO_CHROMA_MODEL_ENUM);\nconst COLOR_SPACE_MODE_TO_COLOR_SPACE_MODEL_MODE_MAP = (0,_utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__.objectFlip)(COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP);\nconst CROP_MODE_TO_ACTION_TYPE_MAP = (0,_utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__.objectFlip)(ACTION_TYPE_TO_CROP_MODE_MAP);\nconst DELIVERY_MODE_TO_ACTION_TYPE_MAP = (0,_utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__.objectFlip)(ACTION_TYPE_TO_DELIVERY_MODE_MAP);\nconst EFFECT_MODE_TO_ACTION_TYPE_MAP = (0,_utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__.objectFlip)(ACTION_TYPE_TO_EFFECT_MODE_MAP);\nconst QUALITY_MODE_TO_ACTION_TYPE_MAP = (0,_utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__.objectFlip)(ACTION_TYPE_TO_QUALITY_MODE_MAP);\nconst STREAMING_PROFILE_TO_ACTION_TYPE_MAP = (0,_utils_objectFlip_js__WEBPACK_IMPORTED_MODULE_0__.objectFlip)(ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/internalConstants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/ActionModel.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/models/ActionModel.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActionModel: () => (/* binding */ ActionModel)\n/* harmony export */ });\n/* harmony import */ var _actionToJson_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actionToJson.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/actionToJson.js\");\n\nclass ActionModel {\n    constructor() {\n        this._actionModel = {};\n    }\n    toJson() {\n        return _actionToJson_js__WEBPACK_IMPORTED_MODULE_0__.actionToJson.apply(this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvbW9kZWxzL0FjdGlvbk1vZGVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBQzFDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDBEQUFZO0FBQzNCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGtcXGludGVybmFsXFxtb2RlbHNcXEFjdGlvbk1vZGVsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFjdGlvblRvSnNvbiB9IGZyb20gXCIuL2FjdGlvblRvSnNvbi5qc1wiO1xuZXhwb3J0IGNsYXNzIEFjdGlvbk1vZGVsIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5fYWN0aW9uTW9kZWwgPSB7fTtcbiAgICB9XG4gICAgdG9Kc29uKCkge1xuICAgICAgICByZXR1cm4gYWN0aW9uVG9Kc29uLmFwcGx5KHRoaXMpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/ActionModel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/IErrorObject.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/models/IErrorObject.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isErrorObject: () => (/* binding */ isErrorObject)\n/* harmony export */ });\n/**\n * Validates obj is an instance of IErrorObject\n * @param obj\n */\nfunction isErrorObject(obj) {\n    const errorObj = obj;\n    return ('error' in errorObj) && !!errorObj.error;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvbW9kZWxzL0lFcnJvck9iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUN5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxub2RlX21vZHVsZXNcXEBjbG91ZGluYXJ5XFx0cmFuc2Zvcm1hdGlvbi1idWlsZGVyLXNka1xcaW50ZXJuYWxcXG1vZGVsc1xcSUVycm9yT2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVmFsaWRhdGVzIG9iaiBpcyBhbiBpbnN0YW5jZSBvZiBJRXJyb3JPYmplY3RcbiAqIEBwYXJhbSBvYmpcbiAqL1xuZnVuY3Rpb24gaXNFcnJvck9iamVjdChvYmopIHtcbiAgICBjb25zdCBlcnJvck9iaiA9IG9iajtcbiAgICByZXR1cm4gKCdlcnJvcicgaW4gZXJyb3JPYmopICYmICEhZXJyb3JPYmouZXJyb3I7XG59XG5leHBvcnQgeyBpc0Vycm9yT2JqZWN0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/IErrorObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/QualifierModel.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/models/QualifierModel.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualifierModel: () => (/* binding */ QualifierModel)\n/* harmony export */ });\n/* harmony import */ var _qualifierToJson_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./qualifierToJson.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/qualifierToJson.js\");\n\nclass QualifierModel {\n    constructor() {\n        this._qualifierModel = {};\n    }\n    toJson() {\n        return _qualifierToJson_js__WEBPACK_IMPORTED_MODULE_0__.qualifierToJson.apply(this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvbW9kZWxzL1F1YWxpZmllck1vZGVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVEO0FBQ2hEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGdFQUFlO0FBQzlCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGtcXGludGVybmFsXFxtb2RlbHNcXFF1YWxpZmllck1vZGVsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHF1YWxpZmllclRvSnNvbiB9IGZyb20gXCIuL3F1YWxpZmllclRvSnNvbi5qc1wiO1xuZXhwb3J0IGNsYXNzIFF1YWxpZmllck1vZGVsIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5fcXVhbGlmaWVyTW9kZWwgPSB7fTtcbiAgICB9XG4gICAgdG9Kc29uKCkge1xuICAgICAgICByZXR1cm4gcXVhbGlmaWVyVG9Kc29uLmFwcGx5KHRoaXMpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/QualifierModel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/actionToJson.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/models/actionToJson.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actionToJson: () => (/* binding */ actionToJson)\n/* harmony export */ });\n/* harmony import */ var _utils_unsupportedError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/unsupportedError.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js\");\n\n/**\n * Returns the action's model\n */\nfunction actionToJson() {\n    var _a, _b, _c;\n    const actionModelIsNotEmpty = this._actionModel && Object.keys(this._actionModel).length;\n    const sourceTransformationError = (_c = (_b = (_a = this._actionModel) === null || _a === void 0 ? void 0 : _a.source) === null || _b === void 0 ? void 0 : _b.transformation) === null || _c === void 0 ? void 0 : _c.error;\n    // Should return error when there is unsupported transformation inside\n    if (sourceTransformationError && sourceTransformationError instanceof Error) {\n        return { error: sourceTransformationError };\n    }\n    if (actionModelIsNotEmpty) {\n        return this._actionModel;\n    }\n    return { error: (0,_utils_unsupportedError_js__WEBPACK_IMPORTED_MODULE_0__.createUnsupportedError)(`unsupported action ${this.constructor.name}`) };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvbW9kZWxzL2FjdGlvblRvSnNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRTtBQUN0RTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLE9BQU8sa0ZBQXNCLHVCQUF1QixzQkFBc0I7QUFDdkYiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGtcXGludGVybmFsXFxtb2RlbHNcXGFjdGlvblRvSnNvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVVbnN1cHBvcnRlZEVycm9yIH0gZnJvbSBcIi4uL3V0aWxzL3Vuc3VwcG9ydGVkRXJyb3IuanNcIjtcbi8qKlxuICogUmV0dXJucyB0aGUgYWN0aW9uJ3MgbW9kZWxcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGFjdGlvblRvSnNvbigpIHtcbiAgICB2YXIgX2EsIF9iLCBfYztcbiAgICBjb25zdCBhY3Rpb25Nb2RlbElzTm90RW1wdHkgPSB0aGlzLl9hY3Rpb25Nb2RlbCAmJiBPYmplY3Qua2V5cyh0aGlzLl9hY3Rpb25Nb2RlbCkubGVuZ3RoO1xuICAgIGNvbnN0IHNvdXJjZVRyYW5zZm9ybWF0aW9uRXJyb3IgPSAoX2MgPSAoX2IgPSAoX2EgPSB0aGlzLl9hY3Rpb25Nb2RlbCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNvdXJjZSkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnRyYW5zZm9ybWF0aW9uKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MuZXJyb3I7XG4gICAgLy8gU2hvdWxkIHJldHVybiBlcnJvciB3aGVuIHRoZXJlIGlzIHVuc3VwcG9ydGVkIHRyYW5zZm9ybWF0aW9uIGluc2lkZVxuICAgIGlmIChzb3VyY2VUcmFuc2Zvcm1hdGlvbkVycm9yICYmIHNvdXJjZVRyYW5zZm9ybWF0aW9uRXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICByZXR1cm4geyBlcnJvcjogc291cmNlVHJhbnNmb3JtYXRpb25FcnJvciB9O1xuICAgIH1cbiAgICBpZiAoYWN0aW9uTW9kZWxJc05vdEVtcHR5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9hY3Rpb25Nb2RlbDtcbiAgICB9XG4gICAgcmV0dXJuIHsgZXJyb3I6IGNyZWF0ZVVuc3VwcG9ydGVkRXJyb3IoYHVuc3VwcG9ydGVkIGFjdGlvbiAke3RoaXMuY29uc3RydWN0b3IubmFtZX1gKSB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/actionToJson.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/qualifierToJson.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/models/qualifierToJson.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   qualifierToJson: () => (/* binding */ qualifierToJson)\n/* harmony export */ });\n/* harmony import */ var _utils_unsupportedError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/unsupportedError.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js\");\n\n/**\n * Returns the action's model\n */\nfunction qualifierToJson() {\n    return this._qualifierModel || { error: (0,_utils_unsupportedError_js__WEBPACK_IMPORTED_MODULE_0__.createUnsupportedError)(`unsupported qualifier ${this.constructor.name}`) };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvbW9kZWxzL3F1YWxpZmllclRvSnNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRTtBQUN0RTtBQUNBO0FBQ0E7QUFDTztBQUNQLHFDQUFxQyxPQUFPLGtGQUFzQiwwQkFBMEIsc0JBQXNCO0FBQ2xIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHRyYW5zZm9ybWF0aW9uLWJ1aWxkZXItc2RrXFxpbnRlcm5hbFxcbW9kZWxzXFxxdWFsaWZpZXJUb0pzb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlVW5zdXBwb3J0ZWRFcnJvciB9IGZyb20gXCIuLi91dGlscy91bnN1cHBvcnRlZEVycm9yLmpzXCI7XG4vKipcbiAqIFJldHVybnMgdGhlIGFjdGlvbidzIG1vZGVsXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBxdWFsaWZpZXJUb0pzb24oKSB7XG4gICAgcmV0dXJuIHRoaXMuX3F1YWxpZmllck1vZGVsIHx8IHsgZXJyb3I6IGNyZWF0ZVVuc3VwcG9ydGVkRXJyb3IoYHVuc3VwcG9ydGVkIHF1YWxpZmllciAke3RoaXMuY29uc3RydWN0b3IubmFtZX1gKSB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/qualifierToJson.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Qualifier: () => (/* binding */ Qualifier)\n/* harmony export */ });\n/* harmony import */ var _QualifierValue_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QualifierValue.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js\");\n/* harmony import */ var _models_QualifierModel_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../models/QualifierModel.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/QualifierModel.js\");\n\n\n/**\n * @summary SDK\n * @memberOf SDK\n */\nclass Qualifier extends _models_QualifierModel_js__WEBPACK_IMPORTED_MODULE_0__.QualifierModel {\n    constructor(key, qualifierValue) {\n        super();\n        this.delimiter = '_'; // {key}{delimiter}{qualifierValue}\n        this.key = key;\n        if (qualifierValue instanceof _QualifierValue_js__WEBPACK_IMPORTED_MODULE_1__.QualifierValue) {\n            this.qualifierValue = qualifierValue;\n        }\n        else {\n            this.qualifierValue = new _QualifierValue_js__WEBPACK_IMPORTED_MODULE_1__.QualifierValue();\n            this.qualifierValue.addValue(qualifierValue);\n        }\n    }\n    toString() {\n        const { key, delimiter, qualifierValue } = this;\n        return `${key}${delimiter}${qualifierValue.toString()}`;\n    }\n    addValue(value) {\n        this.qualifierValue.addValue(value);\n        return this;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualifierValue: () => (/* binding */ QualifierValue)\n/* harmony export */ });\n/**\n * @summary SDK\n * @memberOf SDK\n */\nclass QualifierValue {\n    /**\n     *\n     * @param {QualifierValue | QualifierValue[] | any[] | string | number}qualifierValue\n     */\n    constructor(qualifierValue) {\n        this.values = [];\n        this.delimiter = ':'; // {value}{delimiter}{value}...\n        if (this.hasValue(qualifierValue)) {\n            this.addValue(qualifierValue);\n        }\n    }\n    /**\n     * @description Joins the provided values with the provided delimiter\n     */\n    toString() {\n        return this.values.join(this.delimiter);\n    }\n    /**\n     * @description Checks if the provided argument has a value\n     * @param {any} v\n     * @private\n     * @return {boolean}\n     */\n    hasValue(v) {\n        return typeof v !== 'undefined' && v !== null && v !== '';\n    }\n    /**\n     * @desc Adds a value for the this qualifier instance\n     * @param {any} value\n     * @return {this}\n     */\n    addValue(value) {\n        // Append value or array of values\n        if (Array.isArray(value)) {\n            this.values = this.values.concat(value);\n        }\n        else {\n            this.values.push(value);\n        }\n        // Remove falsy values\n        this.values = this.values.filter((v) => this.hasValue(v));\n        return this;\n    }\n    /**\n     * @description Sets the delimiter for this instance\n     * @param delimiter\n     */\n    setDelimiter(delimiter) {\n        this.delimiter = delimiter;\n        return this;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/dataStructureUtils.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/dataStructureUtils.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   mapToSortedArray: () => (/* binding */ mapToSortedArray)\n/* harmony export */ });\n/**\n * Sort a map by key\n * @private\n * @param map <string, any>\n * @Return array of map's values sorted by key\n */\nfunction mapToSortedArray(map, flags) {\n    const array = Array.from(map.entries());\n    // objects from the Array.from() method above are stored in array of arrays:\n    // [[qualifierKey, QualifierObj], [qualifierKey, QualifierObj]]\n    // Flags is an array of FlagQualifierObj\n    // We need to convert it to the same form: [flagQualifier] =>  ['fl', flagQualifier]\n    flags.forEach((flag) => {\n        array.push(['fl', flag]); // push ['fl', flagQualifier]\n    });\n    return array.sort().map((v) => v[1]);\n}\n/**\n * Checks if `value` is a string.\n * @private\n * @param {*} value The value to check.\n * @return {boolean} `true` if `value` is a string, else `false`.\n */\nfunction isString(value) {\n    return (typeof value === 'string' || value instanceof String);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvdXRpbHMvZGF0YVN0cnVjdHVyZVV0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEMsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLEdBQUc7QUFDZCxZQUFZLFNBQVM7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDc0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGtcXGludGVybmFsXFx1dGlsc1xcZGF0YVN0cnVjdHVyZVV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU29ydCBhIG1hcCBieSBrZXlcbiAqIEBwcml2YXRlXG4gKiBAcGFyYW0gbWFwIDxzdHJpbmcsIGFueT5cbiAqIEBSZXR1cm4gYXJyYXkgb2YgbWFwJ3MgdmFsdWVzIHNvcnRlZCBieSBrZXlcbiAqL1xuZnVuY3Rpb24gbWFwVG9Tb3J0ZWRBcnJheShtYXAsIGZsYWdzKSB7XG4gICAgY29uc3QgYXJyYXkgPSBBcnJheS5mcm9tKG1hcC5lbnRyaWVzKCkpO1xuICAgIC8vIG9iamVjdHMgZnJvbSB0aGUgQXJyYXkuZnJvbSgpIG1ldGhvZCBhYm92ZSBhcmUgc3RvcmVkIGluIGFycmF5IG9mIGFycmF5czpcbiAgICAvLyBbW3F1YWxpZmllcktleSwgUXVhbGlmaWVyT2JqXSwgW3F1YWxpZmllcktleSwgUXVhbGlmaWVyT2JqXV1cbiAgICAvLyBGbGFncyBpcyBhbiBhcnJheSBvZiBGbGFnUXVhbGlmaWVyT2JqXG4gICAgLy8gV2UgbmVlZCB0byBjb252ZXJ0IGl0IHRvIHRoZSBzYW1lIGZvcm06IFtmbGFnUXVhbGlmaWVyXSA9PiAgWydmbCcsIGZsYWdRdWFsaWZpZXJdXG4gICAgZmxhZ3MuZm9yRWFjaCgoZmxhZykgPT4ge1xuICAgICAgICBhcnJheS5wdXNoKFsnZmwnLCBmbGFnXSk7IC8vIHB1c2ggWydmbCcsIGZsYWdRdWFsaWZpZXJdXG4gICAgfSk7XG4gICAgcmV0dXJuIGFycmF5LnNvcnQoKS5tYXAoKHYpID0+IHZbMV0pO1xufVxuLyoqXG4gKiBDaGVja3MgaWYgYHZhbHVlYCBpcyBhIHN0cmluZy5cbiAqIEBwcml2YXRlXG4gKiBAcGFyYW0geyp9IHZhbHVlIFRoZSB2YWx1ZSB0byBjaGVjay5cbiAqIEByZXR1cm4ge2Jvb2xlYW59IGB0cnVlYCBpZiBgdmFsdWVgIGlzIGEgc3RyaW5nLCBlbHNlIGBmYWxzZWAuXG4gKi9cbmZ1bmN0aW9uIGlzU3RyaW5nKHZhbHVlKSB7XG4gICAgcmV0dXJuICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnIHx8IHZhbHVlIGluc3RhbmNlb2YgU3RyaW5nKTtcbn1cbmV4cG9ydCB7IGlzU3RyaW5nLCBtYXBUb1NvcnRlZEFycmF5IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/dataStructureUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/objectFlip.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/objectFlip.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   objectFlip: () => (/* binding */ objectFlip)\n/* harmony export */ });\n/**\n * Flip keys and values for given object\n * @param obj\n */\nfunction objectFlip(obj) {\n    const result = {};\n    Object.keys(obj).forEach((key) => {\n        result[obj[key]] = key;\n    });\n    return result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvdXRpbHMvb2JqZWN0RmxpcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ3NCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHRyYW5zZm9ybWF0aW9uLWJ1aWxkZXItc2RrXFxpbnRlcm5hbFxcdXRpbHNcXG9iamVjdEZsaXAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBGbGlwIGtleXMgYW5kIHZhbHVlcyBmb3IgZ2l2ZW4gb2JqZWN0XG4gKiBAcGFyYW0gb2JqXG4gKi9cbmZ1bmN0aW9uIG9iamVjdEZsaXAob2JqKSB7XG4gICAgY29uc3QgcmVzdWx0ID0ge307XG4gICAgT2JqZWN0LmtleXMob2JqKS5mb3JFYWNoKChrZXkpID0+IHtcbiAgICAgICAgcmVzdWx0W29ialtrZXldXSA9IGtleTtcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzdWx0O1xufVxuZXhwb3J0IHsgb2JqZWN0RmxpcCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/objectFlip.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/prepareColor.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/prepareColor.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prepareColor: () => (/* binding */ prepareColor)\n/* harmony export */ });\n/**\n * Returns RGB or Color\n * @private\n * @param color\n */\nfunction prepareColor(color) {\n    if (color) {\n        return color.match(/^#/) ? `rgb:${color.substr(1)}` : color;\n    }\n    else {\n        return color;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvdXRpbHMvcHJlcGFyZUNvbG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLDBDQUEwQyxnQkFBZ0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxub2RlX21vZHVsZXNcXEBjbG91ZGluYXJ5XFx0cmFuc2Zvcm1hdGlvbi1idWlsZGVyLXNka1xcaW50ZXJuYWxcXHV0aWxzXFxwcmVwYXJlQ29sb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXR1cm5zIFJHQiBvciBDb2xvclxuICogQHByaXZhdGVcbiAqIEBwYXJhbSBjb2xvclxuICovXG5leHBvcnQgZnVuY3Rpb24gcHJlcGFyZUNvbG9yKGNvbG9yKSB7XG4gICAgaWYgKGNvbG9yKSB7XG4gICAgICAgIHJldHVybiBjb2xvci5tYXRjaCgvXiMvKSA/IGByZ2I6JHtjb2xvci5zdWJzdHIoMSl9YCA6IGNvbG9yO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGNvbG9yO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/prepareColor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnsupportedError: () => (/* binding */ UnsupportedError),\n/* harmony export */   createUnsupportedError: () => (/* binding */ createUnsupportedError)\n/* harmony export */ });\nclass UnsupportedError extends Error {\n    constructor(message = 'Unsupported') {\n        super(message);\n    }\n}\n/**\n * Creates a new UnsupportedError\n * @param message\n */\nfunction createUnsupportedError(message) {\n    return new UnsupportedError(message);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvaW50ZXJuYWwvdXRpbHMvdW5zdXBwb3J0ZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNvRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxub2RlX21vZHVsZXNcXEBjbG91ZGluYXJ5XFx0cmFuc2Zvcm1hdGlvbi1idWlsZGVyLXNka1xcaW50ZXJuYWxcXHV0aWxzXFx1bnN1cHBvcnRlZEVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNsYXNzIFVuc3VwcG9ydGVkRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSA9ICdVbnN1cHBvcnRlZCcpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgfVxufVxuLyoqXG4gKiBDcmVhdGVzIGEgbmV3IFVuc3VwcG9ydGVkRXJyb3JcbiAqIEBwYXJhbSBtZXNzYWdlXG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZVVuc3VwcG9ydGVkRXJyb3IobWVzc2FnZSkge1xuICAgIHJldHVybiBuZXcgVW5zdXBwb3J0ZWRFcnJvcihtZXNzYWdlKTtcbn1cbmV4cG9ydCB7IFVuc3VwcG9ydGVkRXJyb3IsIGNyZWF0ZVVuc3VwcG9ydGVkRXJyb3IgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Flag: () => (/* binding */ Flag),\n/* harmony export */   animated: () => (/* binding */ animated),\n/* harmony export */   animatedPng: () => (/* binding */ animatedPng),\n/* harmony export */   animatedWebP: () => (/* binding */ animatedWebP),\n/* harmony export */   anyFormat: () => (/* binding */ anyFormat),\n/* harmony export */   attachment: () => (/* binding */ attachment),\n/* harmony export */   clip: () => (/* binding */ clip),\n/* harmony export */   clipEvenOdd: () => (/* binding */ clipEvenOdd),\n/* harmony export */   custom: () => (/* binding */ custom),\n/* harmony export */   forceIcc: () => (/* binding */ forceIcc),\n/* harmony export */   forceStrip: () => (/* binding */ forceStrip),\n/* harmony export */   getInfo: () => (/* binding */ getInfo),\n/* harmony export */   hlsv3: () => (/* binding */ hlsv3),\n/* harmony export */   ignoreInitialAspectRatio: () => (/* binding */ ignoreInitialAspectRatio),\n/* harmony export */   ignoreMaskChannels: () => (/* binding */ ignoreMaskChannels),\n/* harmony export */   immutableCache: () => (/* binding */ immutableCache),\n/* harmony export */   keepAttribution: () => (/* binding */ keepAttribution),\n/* harmony export */   keepDar: () => (/* binding */ keepDar),\n/* harmony export */   keepIptc: () => (/* binding */ keepIptc),\n/* harmony export */   layerApply: () => (/* binding */ layerApply),\n/* harmony export */   lossy: () => (/* binding */ lossy),\n/* harmony export */   mono: () => (/* binding */ mono),\n/* harmony export */   noOverflow: () => (/* binding */ noOverflow),\n/* harmony export */   noStream: () => (/* binding */ noStream),\n/* harmony export */   png24: () => (/* binding */ png24),\n/* harmony export */   png32: () => (/* binding */ png32),\n/* harmony export */   png8: () => (/* binding */ png8),\n/* harmony export */   preserveTransparency: () => (/* binding */ preserveTransparency),\n/* harmony export */   progressive: () => (/* binding */ progressive),\n/* harmony export */   rasterize: () => (/* binding */ rasterize),\n/* harmony export */   regionRelative: () => (/* binding */ regionRelative),\n/* harmony export */   relative: () => (/* binding */ relative),\n/* harmony export */   sanitize: () => (/* binding */ sanitize),\n/* harmony export */   splice: () => (/* binding */ splice),\n/* harmony export */   streamingAttachment: () => (/* binding */ streamingAttachment),\n/* harmony export */   stripProfile: () => (/* binding */ stripProfile),\n/* harmony export */   tiff8Lzw: () => (/* binding */ tiff8Lzw),\n/* harmony export */   tiled: () => (/* binding */ tiled),\n/* harmony export */   truncateTS: () => (/* binding */ truncateTS),\n/* harmony export */   waveform: () => (/* binding */ waveform)\n/* harmony export */ });\n/* harmony import */ var _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./flag/FlagQualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js\");\n/**\n * @description Defines flags that you can use to alter the default transformation behavior.\n * @namespace Flag\n * @memberOf Qualifiers\n */\n\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Used when delivering a video file as an image format that supports animation, such as animated WebP.\n * Plays all frames rather than just delivering the first one as a static image.\n * Use this flag in addition to the flag or parameter controlling the delivery format,\n * for example f_auto or fl_awebp.\n\n * Note: When delivering a video in GIF format, it is delivered as an animated GIF by default and this flag is not\n * necessary. To deliver a single frame of a video in GIF format, use the page parameter.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction animated() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('animated');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description When converting animated images to WebP format, generate an animated WebP from all the frames in the\n * original\n * animated file instead of only from the first still frame.\n *\n * Note that animated WebPs are not supported in all browsers and versions.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction animatedWebP() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('awebp');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description When used together with automatic quality (q_auto):\n * allow switching to PNG8 encoding if the quality algorithm decides that it's more efficient.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction anyFormat() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('any_format');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description When converting animated images to PNG format, generates an animated PNG from all the frames in the\n * original\n * animated file instead of only from the first still frame.\n *\n * Note that animated PNGs are not supported in all browsers and versions.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction animatedPng() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('apng');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Trims pixels according to a clipping path included in the original image\n * (e.g., manually created using PhotoShop).\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction clip() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('clip');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Trims pixels according to a clipping path included in the original image (e.g., manually created\n * using PhotoShop)\n * using an evenodd clipping rule.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction clipEvenOdd() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('clip_evenodd');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Instructs Cloudinary to clear all image meta-data (IPTC, Exif and XMP) while applying an incoming\n * transformation.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction forceStrip() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('force_strip');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Allows custom flag\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction custom(value) {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier(value);\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Adds ICC color space metadata to the image, even when the original image doesn't contain any ICC data.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction forceIcc() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('force_icc');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Delivers the image as an attachment.\n * @param {string} filename The attachment's filename\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction attachment(filename) {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('attachment', filename);\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Returns metadata of the input asset and of the transformed output asset in JSON instead of the\n * transformed image.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction getInfo() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('getinfo');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Deliver an HLS adaptive bitrate streaming file as HLS v3 instead of the default version (HLS v4).\n * Delivering in this format requires a private CDN configuration.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction hlsv3() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('hlsv3');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Sets the cache-control to immutable for the asset.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction immutableCache() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('immutable_cache');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description * Allows specifying only either width or height so the value of the second axis remains as is, and is not\n * recalculated to maintain the aspect ratio of the original image.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction ignoreInitialAspectRatio() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('ignore_aspect_ratio');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Keeps the copyright related fields when stripping meta-data.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction keepAttribution() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('keep_attribution');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * Keep the Display Aspect Ratio metadata of the uploaded video (if it’s different from the current video\n * dimensions).\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction keepDar() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('keep_dar');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Keeps all meta-data.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction keepIptc() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('keep_iptc');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Applies all chained transformations, until a transformation component that includes this flag, on the last added\n * overlay or underlay instead of applying on the containing image.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction layerApply() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('layer_apply');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Automatically use lossy compression when delivering animated GIF files.\n *\n * This flag can also be used as a conditional flag for delivering PNG files: it tells Cloudinary to deliver the\n * image in PNG format (as requested) unless there is no transparency channel - in which case deliver in JPEG\n * format.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction lossy() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('lossy');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Convert the audio channel to mono\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction mono() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('mono');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Used internally by Position within an Overlay, this flag will tile the overlay across your image.\n *\n * <b>Learn more:</b> {@link https://cloudinary.com/documentation/transformation_reference#fl_no_overflow|Overflow in overlays}\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction noOverflow() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('no_overflow');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Don't stream a video that is currently being generated on the fly. Wait until the video is fully generated.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction noStream() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('no_stream');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Generate PNG images in the png24 format.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction png24() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('png24');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Generate PNG images in the png32 format.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction png32() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('png32');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Generate PNG images in the PNG8 format.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction png8() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('png8');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description When used with automatic fetch_format (f_auto): ensures that images with a transparency channel will be\n * delivered in PNG format.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction preserveTransparency() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('preserve_transparency');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Generates a JPG image using the progressive (interlaced) JPG format.\n *\n * This format allows the browser to quickly show a low-quality rendering of the image until the full-quality\n * image is loaded.\n *\n * @param {string} mode? The mode to determine a specific progressive outcome as follows:\n * * semi - A smart optimization of the decoding time, compression level and progressive rendering\n *          (less iterations). This is the default mode when using q_auto.\n * * steep - Delivers a preview very quickly, and in a single later phase improves the image to\n *           the required resolution.\n * * none  - Use this to deliver a non-progressive image. This is the default mode when setting\n *           a specific value for quality.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction progressive(mode) {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('progressive', mode);\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Modifies percentage-based width & height parameters of overlays and underlays (e.g., 1.0) to be relative to the overlaid region\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction regionRelative() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('region_relative');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Reduces the image to one flat pixelated layer (as opposed to the default vector based graphic) in\n * order to enable\n * PDF resizing and overlay manipulations.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction rasterize() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('rasterize');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Modifies percentage-based width & height parameters of overlays and underlays (e.g., 1.0) to be relative to the containing image instead of the added layer.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction relative() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('relative');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Instructs Cloudinary to run a sanitizer on the image (relevant only for the SVG format).\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction sanitize() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('sanitize');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Splices the video stipulated as an overlay on to the end of the container video instead of adding it as an\n * overlay.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction splice() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('splice');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Instructs Cloudinary to clear all ICC color profile data included with the image.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction stripProfile() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('strip_profile');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description If the requested video transformation has already been generated, this flag works identically to\n * Flag::attachment.\n *\n *  However, if the video transformation is being requested for the first time, this flag causes the video download\n * to begin immediately, streaming it as a fragmented video file.\n *\n * In contrast, if the regular fl_attachment flag is used when a user requests a new video transformation,\n * the download will begin only after the complete transformed video has been generated.\n *\n * Most standard video players successfully play fragmented video files without issue.\n *\n * @param {string} filename The attachment's filename\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction streamingAttachment(filename) {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('streaming_attachment', filename);\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Generates TIFF images using LZW compression and in the TIFF8 format.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction tiff8Lzw() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('tiff8_lzw');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Used internally by Position within an Overlay, this flag will tile the overlay across your image.\n *\n * <b>Learn more:</b> {@link https://cloudinary.com/documentation/layers#automatic_tiling|Tiling overlay}\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction tiled() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('tiled');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Truncate (trim) a video file based on the start time defined in the metadata (relevant only where the metadata\n * includes a directive to play only a section of the video).\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction truncateTS() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('truncate_ts');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description Create a waveform image (in the format specified by the file extension) from the audio or video file.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction waveform() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('waveform');\n}\n/**\n * @summary qualifier\n * @memberOf Qualifiers.Flag\n * @description A qualifier that ensures that an alpha channel is not applied to a TIFF image if it is a mask channel.\n * @return {Qualifiers.Flag.FlagQualifier}\n */\nfunction ignoreMaskChannels() {\n    return new _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier('ignore_mask_channels');\n}\nconst Flag = {\n    animated, anyFormat, animatedPng, animatedWebP,\n    clipEvenOdd, lossy, preserveTransparency, png8, png24, png32, progressive, rasterize,\n    sanitize, stripProfile, tiff8Lzw, attachment, forceIcc, forceStrip, getInfo, immutableCache,\n    keepAttribution, keepIptc, custom, streamingAttachment, hlsv3, keepDar, noStream, mono,\n    layerApply, relative, regionRelative, splice, truncateTS, waveform, ignoreInitialAspectRatio, clip,\n    tiled, noOverflow, ignoreMaskChannels\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlagQualifier: () => (/* binding */ FlagQualifier)\n/* harmony export */ });\n/* harmony import */ var _internal_qualifier_QualifierValue_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internal/qualifier/QualifierValue.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js\");\n/* harmony import */ var _internal_qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/qualifier/Qualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js\");\n\n\n/**\n * @memberOf Qualifiers.Flag\n * @extends {SDK.Qualifier}\n * @description the FlagQualifier class\n */\nclass FlagQualifier extends _internal_qualifier_Qualifier_js__WEBPACK_IMPORTED_MODULE_0__.Qualifier {\n    constructor(flagType, flagValue) {\n        let qualifierValue;\n        if (flagValue) {\n            qualifierValue = new _internal_qualifier_QualifierValue_js__WEBPACK_IMPORTED_MODULE_1__.QualifierValue([flagType, `${flagValue}`]).setDelimiter(':');\n        }\n        else {\n            qualifierValue = flagType;\n        }\n        super('fl', qualifierValue);\n        this.flagValue = flagValue;\n    }\n    toString() {\n        return super.toString().replace(/\\./g, '%2E');\n    }\n    getFlagValue() {\n        return this.flagValue;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvcXVhbGlmaWVycy9mbGFnL0ZsYWdRdWFsaWZpZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRFO0FBQ1Y7QUFDbEU7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsNEJBQTRCLHVFQUFTO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxpRkFBYyxlQUFlLFVBQVU7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUN5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxub2RlX21vZHVsZXNcXEBjbG91ZGluYXJ5XFx0cmFuc2Zvcm1hdGlvbi1idWlsZGVyLXNka1xccXVhbGlmaWVyc1xcZmxhZ1xcRmxhZ1F1YWxpZmllci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBRdWFsaWZpZXJWYWx1ZSB9IGZyb20gXCIuLi8uLi9pbnRlcm5hbC9xdWFsaWZpZXIvUXVhbGlmaWVyVmFsdWUuanNcIjtcbmltcG9ydCB7IFF1YWxpZmllciB9IGZyb20gXCIuLi8uLi9pbnRlcm5hbC9xdWFsaWZpZXIvUXVhbGlmaWVyLmpzXCI7XG4vKipcbiAqIEBtZW1iZXJPZiBRdWFsaWZpZXJzLkZsYWdcbiAqIEBleHRlbmRzIHtTREsuUXVhbGlmaWVyfVxuICogQGRlc2NyaXB0aW9uIHRoZSBGbGFnUXVhbGlmaWVyIGNsYXNzXG4gKi9cbmNsYXNzIEZsYWdRdWFsaWZpZXIgZXh0ZW5kcyBRdWFsaWZpZXIge1xuICAgIGNvbnN0cnVjdG9yKGZsYWdUeXBlLCBmbGFnVmFsdWUpIHtcbiAgICAgICAgbGV0IHF1YWxpZmllclZhbHVlO1xuICAgICAgICBpZiAoZmxhZ1ZhbHVlKSB7XG4gICAgICAgICAgICBxdWFsaWZpZXJWYWx1ZSA9IG5ldyBRdWFsaWZpZXJWYWx1ZShbZmxhZ1R5cGUsIGAke2ZsYWdWYWx1ZX1gXSkuc2V0RGVsaW1pdGVyKCc6Jyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBxdWFsaWZpZXJWYWx1ZSA9IGZsYWdUeXBlO1xuICAgICAgICB9XG4gICAgICAgIHN1cGVyKCdmbCcsIHF1YWxpZmllclZhbHVlKTtcbiAgICAgICAgdGhpcy5mbGFnVmFsdWUgPSBmbGFnVmFsdWU7XG4gICAgfVxuICAgIHRvU3RyaW5nKCkge1xuICAgICAgICByZXR1cm4gc3VwZXIudG9TdHJpbmcoKS5yZXBsYWNlKC9cXC4vZywgJyUyRScpO1xuICAgIH1cbiAgICBnZXRGbGFnVmFsdWUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmZsYWdWYWx1ZTtcbiAgICB9XG59XG5leHBvcnQgeyBGbGFnUXVhbGlmaWVyIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/format/FormatQualifier.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/format/FormatQualifier.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormatQualifier: () => (/* binding */ FormatQualifier)\n/* harmony export */ });\n/* harmony import */ var _internal_qualifier_QualifierValue_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/qualifier/QualifierValue.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js\");\n\n/**\n * @memberOf Qualifiers.Format\n * @extends {SDK.QualifierValue}\n */\nclass FormatQualifier extends _internal_qualifier_QualifierValue_js__WEBPACK_IMPORTED_MODULE_0__.QualifierValue {\n    constructor(val) {\n        super(val);\n        this.val = val;\n    }\n    getValue() {\n        return this.val;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvcXVhbGlmaWVycy9mb3JtYXQvRm9ybWF0UXVhbGlmaWVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRFO0FBQzVFO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSw4QkFBOEIsaUZBQWM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUMyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxub2RlX21vZHVsZXNcXEBjbG91ZGluYXJ5XFx0cmFuc2Zvcm1hdGlvbi1idWlsZGVyLXNka1xccXVhbGlmaWVyc1xcZm9ybWF0XFxGb3JtYXRRdWFsaWZpZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUXVhbGlmaWVyVmFsdWUgfSBmcm9tIFwiLi4vLi4vaW50ZXJuYWwvcXVhbGlmaWVyL1F1YWxpZmllclZhbHVlLmpzXCI7XG4vKipcbiAqIEBtZW1iZXJPZiBRdWFsaWZpZXJzLkZvcm1hdFxuICogQGV4dGVuZHMge1NESy5RdWFsaWZpZXJWYWx1ZX1cbiAqL1xuY2xhc3MgRm9ybWF0UXVhbGlmaWVyIGV4dGVuZHMgUXVhbGlmaWVyVmFsdWUge1xuICAgIGNvbnN0cnVjdG9yKHZhbCkge1xuICAgICAgICBzdXBlcih2YWwpO1xuICAgICAgICB0aGlzLnZhbCA9IHZhbDtcbiAgICB9XG4gICAgZ2V0VmFsdWUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnZhbDtcbiAgICB9XG59XG5leHBvcnQgeyBGb3JtYXRRdWFsaWZpZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/format/FormatQualifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/progressive.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/progressive.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progressive: () => (/* binding */ Progressive),\n/* harmony export */   ProgressiveQualifier: () => (/* binding */ ProgressiveQualifier),\n/* harmony export */   none: () => (/* binding */ none),\n/* harmony export */   progressive: () => (/* binding */ progressive),\n/* harmony export */   semi: () => (/* binding */ semi),\n/* harmony export */   steep: () => (/* binding */ steep)\n/* harmony export */ });\n/* harmony import */ var _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./flag/FlagQualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js\");\n/**\n * @description Contains functions to select the mode when using a progressive format.\n * <b>Learn more</b>: {@link https://cloudinary.com/documentation/transformation_reference#fl_progressive|Progressive modes}\n * @memberOf Qualifiers\n * @namespace Progressive\n * @example\n * import {Cloudinary} from \"@cloudinary/url-gen\";\n * import {format} from \"@cloudinary/url-gen/actions/delivery\";\n * import {jpg} from \"@cloudinary/url-gen/qualifiers/format\";\n * import {steep} from \"@cloudinary/url-gen/qualifiers/progressive\";\n *\n * const yourCldInstance = new Cloudinary({cloud: {cloudName: 'demo'}});\n * const image = yourCldInstance.image('woman');\n * image.delivery(format(jpg()).progressive(steep()))\n */\n\nclass ProgressiveQualifier extends _flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_0__.FlagQualifier {\n    constructor(mode) {\n        super('progressive', mode);\n    }\n}\n/**\n * @memberOf Qualifiers.Progressive\n */\nfunction none() {\n    return new ProgressiveQualifier('none');\n}\n/**\n * @memberOf Qualifiers.Progressive\n */\nfunction semi() {\n    return new ProgressiveQualifier('semi');\n}\n/**\n * @memberOf Qualifiers.Progressive\n */\nfunction steep() {\n    return new ProgressiveQualifier('steep');\n}\n/**\n * @memberOf Qualifiers.Progressive\n */\nfunction progressive() {\n    return new ProgressiveQualifier();\n}\nconst Progressive = {\n    semi,\n    none,\n    steep,\n    progressive,\n    ProgressiveQualifier\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvcXVhbGlmaWVycy9wcm9ncmVzc2l2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQSxXQUFXLFlBQVk7QUFDdkIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsS0FBSztBQUNoQixXQUFXLE9BQU87QUFDbEI7QUFDQSwyQ0FBMkMsUUFBUSxtQkFBbUI7QUFDdEU7QUFDQTtBQUNBO0FBQ3dEO0FBQ3hELG1DQUFtQyxpRUFBYTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzZFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHRyYW5zZm9ybWF0aW9uLWJ1aWxkZXItc2RrXFxxdWFsaWZpZXJzXFxwcm9ncmVzc2l2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBkZXNjcmlwdGlvbiBDb250YWlucyBmdW5jdGlvbnMgdG8gc2VsZWN0IHRoZSBtb2RlIHdoZW4gdXNpbmcgYSBwcm9ncmVzc2l2ZSBmb3JtYXQuXG4gKiA8Yj5MZWFybiBtb3JlPC9iPjoge0BsaW5rIGh0dHBzOi8vY2xvdWRpbmFyeS5jb20vZG9jdW1lbnRhdGlvbi90cmFuc2Zvcm1hdGlvbl9yZWZlcmVuY2UjZmxfcHJvZ3Jlc3NpdmV8UHJvZ3Jlc3NpdmUgbW9kZXN9XG4gKiBAbWVtYmVyT2YgUXVhbGlmaWVyc1xuICogQG5hbWVzcGFjZSBQcm9ncmVzc2l2ZVxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7Q2xvdWRpbmFyeX0gZnJvbSBcIkBjbG91ZGluYXJ5L3VybC1nZW5cIjtcbiAqIGltcG9ydCB7Zm9ybWF0fSBmcm9tIFwiQGNsb3VkaW5hcnkvdXJsLWdlbi9hY3Rpb25zL2RlbGl2ZXJ5XCI7XG4gKiBpbXBvcnQge2pwZ30gZnJvbSBcIkBjbG91ZGluYXJ5L3VybC1nZW4vcXVhbGlmaWVycy9mb3JtYXRcIjtcbiAqIGltcG9ydCB7c3RlZXB9IGZyb20gXCJAY2xvdWRpbmFyeS91cmwtZ2VuL3F1YWxpZmllcnMvcHJvZ3Jlc3NpdmVcIjtcbiAqXG4gKiBjb25zdCB5b3VyQ2xkSW5zdGFuY2UgPSBuZXcgQ2xvdWRpbmFyeSh7Y2xvdWQ6IHtjbG91ZE5hbWU6ICdkZW1vJ319KTtcbiAqIGNvbnN0IGltYWdlID0geW91ckNsZEluc3RhbmNlLmltYWdlKCd3b21hbicpO1xuICogaW1hZ2UuZGVsaXZlcnkoZm9ybWF0KGpwZygpKS5wcm9ncmVzc2l2ZShzdGVlcCgpKSlcbiAqL1xuaW1wb3J0IHsgRmxhZ1F1YWxpZmllciB9IGZyb20gXCIuL2ZsYWcvRmxhZ1F1YWxpZmllci5qc1wiO1xuY2xhc3MgUHJvZ3Jlc3NpdmVRdWFsaWZpZXIgZXh0ZW5kcyBGbGFnUXVhbGlmaWVyIHtcbiAgICBjb25zdHJ1Y3Rvcihtb2RlKSB7XG4gICAgICAgIHN1cGVyKCdwcm9ncmVzc2l2ZScsIG1vZGUpO1xuICAgIH1cbn1cbi8qKlxuICogQG1lbWJlck9mIFF1YWxpZmllcnMuUHJvZ3Jlc3NpdmVcbiAqL1xuZnVuY3Rpb24gbm9uZSgpIHtcbiAgICByZXR1cm4gbmV3IFByb2dyZXNzaXZlUXVhbGlmaWVyKCdub25lJyk7XG59XG4vKipcbiAqIEBtZW1iZXJPZiBRdWFsaWZpZXJzLlByb2dyZXNzaXZlXG4gKi9cbmZ1bmN0aW9uIHNlbWkoKSB7XG4gICAgcmV0dXJuIG5ldyBQcm9ncmVzc2l2ZVF1YWxpZmllcignc2VtaScpO1xufVxuLyoqXG4gKiBAbWVtYmVyT2YgUXVhbGlmaWVycy5Qcm9ncmVzc2l2ZVxuICovXG5mdW5jdGlvbiBzdGVlcCgpIHtcbiAgICByZXR1cm4gbmV3IFByb2dyZXNzaXZlUXVhbGlmaWVyKCdzdGVlcCcpO1xufVxuLyoqXG4gKiBAbWVtYmVyT2YgUXVhbGlmaWVycy5Qcm9ncmVzc2l2ZVxuICovXG5mdW5jdGlvbiBwcm9ncmVzc2l2ZSgpIHtcbiAgICByZXR1cm4gbmV3IFByb2dyZXNzaXZlUXVhbGlmaWVyKCk7XG59XG5jb25zdCBQcm9ncmVzc2l2ZSA9IHtcbiAgICBzZW1pLFxuICAgIG5vbmUsXG4gICAgc3RlZXAsXG4gICAgcHJvZ3Jlc3NpdmUsXG4gICAgUHJvZ3Jlc3NpdmVRdWFsaWZpZXJcbn07XG5leHBvcnQgeyBQcm9ncmVzc2l2ZSwgc2VtaSwgbm9uZSwgc3RlZXAsIHByb2dyZXNzaXZlLCBQcm9ncmVzc2l2ZVF1YWxpZmllciB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/progressive.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/ImageTransformation.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/transformation/ImageTransformation.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageTransformation: () => (/* binding */ ImageTransformation)\n/* harmony export */ });\n/* harmony import */ var _Transformation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Transformation.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js\");\n\n/**\n * @summary SDK\n * @extends {SDK.Transformation}\n * @memberOf SDK\n */\nclass ImageTransformation extends _Transformation_js__WEBPACK_IMPORTED_MODULE_0__.Transformation {\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvdHJhbnNmb3JtYXRpb24vSW1hZ2VUcmFuc2Zvcm1hdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUNyRDtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxrQ0FBa0MsOERBQWM7QUFDaEQ7QUFDK0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGtcXHRyYW5zZm9ybWF0aW9uXFxJbWFnZVRyYW5zZm9ybWF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRyYW5zZm9ybWF0aW9uIH0gZnJvbSBcIi4vVHJhbnNmb3JtYXRpb24uanNcIjtcbi8qKlxuICogQHN1bW1hcnkgU0RLXG4gKiBAZXh0ZW5kcyB7U0RLLlRyYW5zZm9ybWF0aW9ufVxuICogQG1lbWJlck9mIFNES1xuICovXG5jbGFzcyBJbWFnZVRyYW5zZm9ybWF0aW9uIGV4dGVuZHMgVHJhbnNmb3JtYXRpb24ge1xufVxuZXhwb3J0IHsgSW1hZ2VUcmFuc2Zvcm1hdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/ImageTransformation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Transformation: () => (/* binding */ Transformation)\n/* harmony export */ });\n/* harmony import */ var _internal_Action_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internal/Action.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js\");\n/* harmony import */ var _actions_background_actions_BackgroundColor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../actions/background/actions/BackgroundColor.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/background/actions/BackgroundColor.js\");\n/* harmony import */ var _qualifiers_flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../qualifiers/flag/FlagQualifier.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js\");\n/* harmony import */ var _internal_RawAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/RawAction.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/RawAction.js\");\n/* harmony import */ var _internal_models_IErrorObject_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../internal/models/IErrorObject.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/internal/models/IErrorObject.js\");\n/* harmony import */ var _actions_delivery_DeliveryFormatAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/delivery/DeliveryFormatAction.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js\");\n\n\n\n\n\n\n/**\n * @summary SDK\n * @description - Defines how to transform an asset\n * @memberOf SDK\n */\nclass Transformation {\n    constructor() {\n        this.actions = [];\n    }\n    /**\n     * @param {SDK.Action | string} action\n     * @return {this}\n     */\n    addAction(action) {\n        let actionToAdd;\n        if (typeof action === 'string') {\n            if (action.indexOf('/') >= 0) {\n                throw 'addAction cannot accept a string with a forward slash in it - /, use .addTransformation() instead';\n            }\n            else {\n                actionToAdd = new _internal_RawAction_js__WEBPACK_IMPORTED_MODULE_0__.RawAction(action);\n            }\n        }\n        else {\n            actionToAdd = action;\n        }\n        this.actions.push(actionToAdd);\n        return this;\n    }\n    /**\n     * @description Allows the injection of a raw transformation as a string into the transformation, or a Transformation instance that was previously created\n     * @param {string | SDK.Transformation} tx\n     * @example\n     * import {Transformation} from \"@cloudinary/url-gen\";\n     *\n     * const transformation = new Transformation();\n     * transformation.addTransformation('w_100/w_200/w_300');\n     * @return {this}\n     */\n    addTransformation(tx) {\n        if (tx instanceof Transformation) {\n            // Concat the new actions into the existing actions\n            this.actions = this.actions.concat(tx.actions);\n        }\n        else {\n            this.actions.push(new _internal_RawAction_js__WEBPACK_IMPORTED_MODULE_0__.RawAction(tx));\n        }\n        return this;\n    }\n    /**\n     * @return {string}\n     */\n    toString() {\n        return this.actions\n            .map((action) => {\n            return action.toString();\n        })\n            .filter((a) => a)\n            .join('/');\n    }\n    /**\n     * @description Delivers an animated GIF.\n     * @param {AnimatedAction} animatedAction\n     * @return {this}\n     */\n    animated(animatedAction) {\n        return this.addAction(animatedAction);\n    }\n    /**\n     * @description Adds a border around the image.\n     * @param {Border} borderAction\n     * @return {this}\n     */\n    border(borderAction) {\n        return this.addAction(borderAction);\n    }\n    /**\n     * @description Adjusts the shape of the delivered image. </br>\n     * <b>Learn more:</b> {@link https://cloudinary.com/documentation/effects_and_artistic_enhancements#distort|Shape changes and distortion effects}\n     * @param {IReshape} reshapeAction\n     * @return {this}\n     */\n    reshape(reshapeAction) {\n        return this.addAction(reshapeAction);\n    }\n    /**\n     * @description Resize the asset using provided resize action\n     * @param {ResizeSimpleAction} resizeAction\n     * @return {this}\n     */\n    resize(resizeAction) {\n        return this.addAction(resizeAction);\n    }\n    /**\n     * @desc An alias to Action Delivery.quality\n     * @param {string|number} quality\n     * @return {this}\n     */\n    quality(quality) {\n        this.addAction(new _actions_delivery_DeliveryFormatAction_js__WEBPACK_IMPORTED_MODULE_1__.DeliveryFormatAction('q', quality));\n        return this;\n    }\n    /**\n     * @desc An alias to Action Delivery.format\n     * @param {string} format\n     * @return {this}\n     */\n    format(format) {\n        this.addAction(new _actions_delivery_DeliveryFormatAction_js__WEBPACK_IMPORTED_MODULE_1__.DeliveryFormatAction('f', format));\n        return this;\n    }\n    /**\n     * @description Rounds the specified corners of an image.\n     * @param roundCornersAction\n     * @return {this}\n     */\n    roundCorners(roundCornersAction) {\n        return this.addAction(roundCornersAction);\n    }\n    /**\n     * @description Adds an overlay over the base image.\n     * @param {LayerAction} overlayAction\n     * @return {this}\n     */\n    overlay(overlayAction) {\n        return this.addAction(overlayAction);\n    }\n    /**\n     * @description Adds an underlay under the base image.\n     * @param {LayerAction} underlayAction\n     * @return {this}\n     */\n    underlay(underlayAction) {\n        underlayAction.setLayerType('u');\n        return this.addAction(underlayAction);\n    }\n    /**\n     * @description Defines an new user variable.\n     * @param {VariableAction} variableAction\n     * @return {this}\n     */\n    addVariable(variableAction) {\n        return this.addAction(variableAction);\n    }\n    /**\n     * @description Specifies a condition to be met before applying a transformation.\n     * @param {ConditionalAction} conditionAction\n     * @return {this}\n     */\n    conditional(conditionAction) {\n        return this.addAction(conditionAction);\n    }\n    /**\n     * @description Applies a filter or an effect on an asset.\n     * @param {SimpleEffectAction} effectAction\n     * @return {this}\n     */\n    effect(effectAction) {\n        return this.addAction(effectAction);\n    }\n    /**\n     * @description Applies adjustment effect on an asset.\n     * @param action\n     * @return {this}\n     */\n    adjust(action) {\n        return this.addAction(action);\n    }\n    /**\n     * @description Rotates the asset by the given angle.\n     * @param {RotateAction} rotateAction\n     * @return {this}\n     */\n    rotate(rotateAction) {\n        return this.addAction(rotateAction);\n    }\n    /**\n     * @description Applies a pre-defined named transformation of the given name.\n     * @param {NamedTransformation} namedTransformation\n     * @return {this}\n     */\n    namedTransformation(namedTransformation) {\n        return this.addAction(namedTransformation);\n    }\n    /**\n     * @description Applies delivery action.\n     * @param deliveryAction\n     * @return {this}\n     */\n    delivery(deliveryAction) {\n        return this.addAction(deliveryAction);\n    }\n    /**\n     * @description Sets the color of the background.\n     * @param {Qualifiers.Color} color\n     * @return {this}\n     */\n    backgroundColor(color) {\n        return this.addAction(new _actions_background_actions_BackgroundColor_js__WEBPACK_IMPORTED_MODULE_2__.BackgroundColor(color));\n    }\n    /**\n     * @description Adds a layer in a Photoshop document.\n     * @param action\n     * @return {this}\n     */\n    psdTools(action) {\n        return this.addAction(action);\n    }\n    /**\n     * @description Extracts an image or a page using an index, a range, or a name from a layered media asset.\n     * @param action\n     * @return {this}\n     */\n    extract(action) {\n        return this.addAction(action);\n    }\n    /**\n     * @description Adds a flag as a separate action.\n     * @param {Qualifiers.Flag | string} flagQualifier\n     * @return {this}\n     */\n    addFlag(flagQualifier) {\n        const action = new _internal_Action_js__WEBPACK_IMPORTED_MODULE_3__.Action();\n        let flagToAdd = flagQualifier;\n        if (typeof flagQualifier === 'string') {\n            flagToAdd = new _qualifiers_flag_FlagQualifier_js__WEBPACK_IMPORTED_MODULE_4__.FlagQualifier(flagQualifier);\n        }\n        action.addQualifier(flagToAdd);\n        return this.addAction(action);\n    }\n    /**\n     * @description Inject a custom function into the image transformation pipeline.\n     * @return {this}\n     */\n    customFunction(customFunction) {\n        return this.addAction(customFunction);\n    }\n    /**\n     * Transcodes the video (or audio) to another format.\n     * @param {Action} action\n     * @return {this}\n     */\n    transcode(action) {\n        return this.addAction(action);\n    }\n    /**\n     * Applies the specified video edit action.\n     *\n     * @param {videoEditType} action\n     * @return {this}\n     */\n    videoEdit(action) {\n        return this.addAction(action);\n    }\n    toJson() {\n        const actions = [];\n        for (const action of this.actions) {\n            const json = action.toJson();\n            if ((0,_internal_models_IErrorObject_js__WEBPACK_IMPORTED_MODULE_5__.isErrorObject)(json)) {\n                // Fail early and return an IErrorObject\n                return json;\n            }\n            actions.push(json);\n        }\n        return { actions };\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/VideoTransformation.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@cloudinary/transformation-builder-sdk/transformation/VideoTransformation.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VideoTransformation: () => (/* binding */ VideoTransformation)\n/* harmony export */ });\n/* harmony import */ var _Transformation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Transformation.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js\");\n\n/**\n * @summary SDK\n * @extends {SDK.Transformation}\n * @memberOf SDK\n */\nclass VideoTransformation extends _Transformation_js__WEBPACK_IMPORTED_MODULE_0__.Transformation {\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvdHJhbnNmb3JtYXRpb24vVmlkZW9UcmFuc2Zvcm1hdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUNyRDtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxrQ0FBa0MsOERBQWM7QUFDaEQ7QUFDK0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGtcXHRyYW5zZm9ybWF0aW9uXFxWaWRlb1RyYW5zZm9ybWF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRyYW5zZm9ybWF0aW9uIH0gZnJvbSBcIi4vVHJhbnNmb3JtYXRpb24uanNcIjtcbi8qKlxuICogQHN1bW1hcnkgU0RLXG4gKiBAZXh0ZW5kcyB7U0RLLlRyYW5zZm9ybWF0aW9ufVxuICogQG1lbWJlck9mIFNES1xuICovXG5jbGFzcyBWaWRlb1RyYW5zZm9ybWF0aW9uIGV4dGVuZHMgVHJhbnNmb3JtYXRpb24ge1xufVxuZXhwb3J0IHsgVmlkZW9UcmFuc2Zvcm1hdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/VideoTransformation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryFile.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/assets/CloudinaryFile.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudinaryFile: () => (/* binding */ CloudinaryFile),\n/* harmony export */   SEO_TYPES: () => (/* binding */ SEO_TYPES)\n/* harmony export */ });\n/* harmony import */ var _internal_url_cloudinaryURL_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internal/url/cloudinaryURL.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/internal/url/cloudinaryURL.js\");\n/* harmony import */ var _config_URLConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/URLConfig.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/config/URLConfig.js\");\n/* harmony import */ var _sdkAnalytics_getSDKAnalyticsSignature_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../sdkAnalytics/getSDKAnalyticsSignature.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/getSDKAnalyticsSignature.js\");\n\n\n\n/**\n * This const contains all the valid combination of asset/delivery for URL shortening purposes\n * It's exported because it's used in a test, but it's not really shared enough to belong in a separate file\n */\nconst SEO_TYPES = {\n    \"image/upload\": \"images\",\n    \"image/private\": \"private_images\",\n    \"image/authenticated\": \"authenticated_images\",\n    \"raw/upload\": \"files\",\n    \"video/upload\": \"videos\"\n};\n/**\n * @description Cloudinary file without a transformation\n * @summary SDK\n * @memberOf SDK\n */\nclass CloudinaryFile {\n    constructor(publicID, cloudConfig = {}, urlConfig) {\n        this.setPublicID(publicID);\n        this.setCloudConfig(cloudConfig);\n        this.setURLConfig(urlConfig);\n    }\n    /**\n     * @description Sets the URL Config for this asset\n     * @param urlConfig\n     * @return {this}\n     */\n    setURLConfig(urlConfig) {\n        this.urlConfig = new _config_URLConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](urlConfig);\n        return this;\n    }\n    /**\n     * @description Sets the Cloud Config for this asset\n     * @param urlConfig\n     * @return {this}\n     */\n    setCloudConfig(cloudConfig) {\n        this.cloudName = cloudConfig.cloudName;\n        this.apiKey = cloudConfig.apiKey;\n        this.apiSecret = cloudConfig.apiSecret;\n        this.authToken = cloudConfig.authToken;\n        return this;\n    }\n    /**\n     * @description Sets the public ID of the asset.\n     * @param {string} publicID The public ID of the asset.\n     * @return {this}\n     */\n    setPublicID(publicID) {\n        // PublicID must be a string!\n        this.publicID = publicID ? publicID.toString() : '';\n        return this;\n    }\n    /**\n     * @description Sets the delivery type of the asset.\n     * @param {DELIVERY_TYPE | string} newType The type of the asset.\n     * @return {this}\n     */\n    setDeliveryType(newType) {\n        this.deliveryType = newType;\n        return this;\n    }\n    /**\n     * @description Sets the URL SEO suffix of the asset.\n     * @param {string} newSuffix The SEO suffix.\n     * @return {this}\n     */\n    setSuffix(newSuffix) {\n        this.suffix = newSuffix;\n        return this;\n    }\n    /**\n     * @description Sets the signature of the asset.\n     * @param {string} signature The signature.\n     * @return {this}\n     */\n    setSignature(signature) {\n        this.signature = signature;\n        return this;\n    }\n    /**\n     * @description Sets the version of the asset.\n     * @param {string} newVersion The version of the asset.\n     * @return {this}\n     */\n    setVersion(newVersion) {\n        if (newVersion) {\n            this.version = newVersion;\n        }\n        return this;\n    }\n    /**\n     * @description Sets the asset type.\n     * @param {string} newType The type of the asset.\n     * @return {this}\n     */\n    setAssetType(newType) {\n        if (newType) {\n            this.assetType = newType;\n        }\n        return this;\n    }\n    sign() {\n        return this;\n    }\n    /**\n     * @description Serializes to URL string\n     * @param overwriteOptions\n     */\n    toURL(overwriteOptions = {}) {\n        return this.createCloudinaryURL(null, overwriteOptions.trackedAnalytics);\n    }\n    /**\n     * @description Validate various options before attempting to create a URL\n     * The function will throw in case a violation\n     * @throws Validation errors\n     */\n    validateAssetForURLCreation() {\n        if (typeof this.cloudName === 'undefined') {\n            throw 'You must supply a cloudName when initializing the asset';\n        }\n        const suffixContainsDot = this.suffix && this.suffix.indexOf('.') >= 0;\n        const suffixContainsSlash = this.suffix && this.suffix.indexOf('/') >= 0;\n        if (suffixContainsDot || suffixContainsSlash) {\n            throw '`suffix`` should not include . or /';\n        }\n    }\n    /**\n     * @description return an SEO friendly name for a combination of asset/delivery, some examples:\n     * * image/upload -> images\n     * * video/upload -> videos\n     * If no match is found, return `{asset}/{delivery}`\n     */\n    getResourceType() {\n        const assetType = (0,_internal_url_cloudinaryURL_js__WEBPACK_IMPORTED_MODULE_1__.handleAssetType)(this.assetType);\n        const deliveryType = (0,_internal_url_cloudinaryURL_js__WEBPACK_IMPORTED_MODULE_1__.handleDeliveryType)(this.deliveryType);\n        const hasSuffix = !!this.suffix;\n        const regularSEOType = `${assetType}/${deliveryType}`;\n        const shortSEOType = SEO_TYPES[`${assetType}/${deliveryType}`];\n        const useRootPath = this.urlConfig.useRootPath;\n        const shorten = this.urlConfig.shorten;\n        // Quick exit incase of useRootPath\n        if (useRootPath) {\n            if (regularSEOType === 'image/upload') {\n                return ''; // For image/upload we're done, just return nothing\n            }\n            else {\n                throw new Error(`useRootPath can only be used with assetType: 'image' and deliveryType: 'upload'. Provided: ${regularSEOType} instead`);\n            }\n        }\n        if (shorten && regularSEOType === 'image/upload') {\n            return 'iu';\n        }\n        if (hasSuffix) {\n            if (shortSEOType) {\n                return shortSEOType;\n            }\n            else {\n                throw new Error(`URL Suffix only supported for ${Object.keys(SEO_TYPES).join(', ')}, Provided: ${regularSEOType} instead`);\n            }\n        }\n        // If all else fails, return the regular image/upload combination (asset/delivery)\n        return regularSEOType;\n    }\n    getSignature() {\n        if (this.signature) {\n            return `s--${this.signature}--`;\n        }\n        else {\n            return '';\n        }\n    }\n    /**\n     *\n     * @description Creates a fully qualified CloudinaryURL\n     * @return {string} CloudinaryURL\n     * @throws Validation Errors\n     */\n    createCloudinaryURL(transformation, trackedAnalytics) {\n        // In accordance with the existing implementation, if no publicID exists we should return nothing.\n        if (!this.publicID) {\n            return '';\n        }\n        // Throws if some options are mis-configured\n        // See the function for more information on when it throws\n        this.validateAssetForURLCreation();\n        const prefix = (0,_internal_url_cloudinaryURL_js__WEBPACK_IMPORTED_MODULE_1__.getUrlPrefix)(this.cloudName, this.urlConfig);\n        const transformationString = transformation ? transformation.toString() : '';\n        const version = (0,_internal_url_cloudinaryURL_js__WEBPACK_IMPORTED_MODULE_1__.getUrlVersion)(this.publicID, this.version, this.urlConfig.forceVersion);\n        const publicID = this.publicID;\n        if (typeof transformation === 'string') {\n            const url = [prefix, this.getResourceType(), this.getSignature(), transformationString, version, publicID.replace(/,/g, '%2C'), this.suffix]\n                .filter((a) => a)\n                .join('/');\n            return url;\n        }\n        else {\n            // Avoid applying encodeURI on entire string in case where we have transformations with comma (i.e. f_auto,q_auto)\n            // Since encodeURI does not encode commas we replace commas *only* on the publicID\n            const safeURL = [\n                encodeURI(prefix),\n                this.getResourceType(),\n                this.getSignature(),\n                encodeURI(transformationString),\n                version,\n                encodeURI(publicID).replace(/,/g, '%2C'),\n                this.suffix && encodeURI(this.suffix)\n            ]\n                .filter((a) => a)\n                .join('/')\n                .replace(/\\?/g, '%3F')\n                .replace(/=/g, '%3D');\n            const shouldAddAnalytics = this.urlConfig.analytics !== false && !(publicID.includes('?'));\n            let queryParamsString = '';\n            if (typeof (this.urlConfig.queryParams) === 'object') {\n                try {\n                    const queryParams = new URLSearchParams(this.urlConfig.queryParams);\n                    if (shouldAddAnalytics) {\n                        queryParams.set(\"_a\", (0,_sdkAnalytics_getSDKAnalyticsSignature_js__WEBPACK_IMPORTED_MODULE_2__.getSDKAnalyticsSignature)(trackedAnalytics));\n                    }\n                    queryParamsString = queryParams.toString();\n                }\n                catch (err) {\n                    console.error('Error: URLSearchParams is not available so the queryParams object cannot be parsed, please try passing as an already parsed string');\n                }\n            }\n            else {\n                queryParamsString = this.urlConfig.queryParams || '';\n                if (shouldAddAnalytics) {\n                    queryParamsString += `${(queryParamsString.length > 0 ? '&' : '')}_a=${(0,_sdkAnalytics_getSDKAnalyticsSignature_js__WEBPACK_IMPORTED_MODULE_2__.getSDKAnalyticsSignature)(trackedAnalytics)}`;\n                }\n            }\n            if (queryParamsString) {\n                return `${safeURL}?${queryParamsString}`;\n            }\n            else {\n                return safeURL;\n            }\n        }\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryFile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryImage.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/assets/CloudinaryImage.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudinaryImage: () => (/* binding */ CloudinaryImage)\n/* harmony export */ });\n/* harmony import */ var _cloudinary_transformation_builder_sdk_transformation_ImageTransformation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @cloudinary/transformation-builder-sdk/transformation/ImageTransformation */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/ImageTransformation.js\");\n/* harmony import */ var _CloudinaryTransformable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CloudinaryTransformable.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js\");\n\n\n/**\n * @desc Cloudinary image asset, with image-related transformations\n * @summary SDK\n * @memberOf SDK\n */\nclass CloudinaryImage extends _CloudinaryTransformable_js__WEBPACK_IMPORTED_MODULE_0__.CloudinaryTransformable {\n    constructor(publicID, cloudConfig, urlConfig) {\n        /* istanbul ignore next */\n        super(publicID, cloudConfig, urlConfig, new _cloudinary_transformation_builder_sdk_transformation_ImageTransformation__WEBPACK_IMPORTED_MODULE_1__.ImageTransformation());\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9hc3NldHMvQ2xvdWRpbmFyeUltYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnSDtBQUN6QztBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLGdGQUF1QjtBQUNyRDtBQUNBO0FBQ0Esb0RBQW9ELDBIQUFtQjtBQUN2RTtBQUNBO0FBQzJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHVybC1nZW5cXGFzc2V0c1xcQ2xvdWRpbmFyeUltYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEltYWdlVHJhbnNmb3JtYXRpb24gfSBmcm9tIFwiQGNsb3VkaW5hcnkvdHJhbnNmb3JtYXRpb24tYnVpbGRlci1zZGsvdHJhbnNmb3JtYXRpb24vSW1hZ2VUcmFuc2Zvcm1hdGlvblwiO1xuaW1wb3J0IHsgQ2xvdWRpbmFyeVRyYW5zZm9ybWFibGUgfSBmcm9tIFwiLi9DbG91ZGluYXJ5VHJhbnNmb3JtYWJsZS5qc1wiO1xuLyoqXG4gKiBAZGVzYyBDbG91ZGluYXJ5IGltYWdlIGFzc2V0LCB3aXRoIGltYWdlLXJlbGF0ZWQgdHJhbnNmb3JtYXRpb25zXG4gKiBAc3VtbWFyeSBTREtcbiAqIEBtZW1iZXJPZiBTREtcbiAqL1xuY2xhc3MgQ2xvdWRpbmFyeUltYWdlIGV4dGVuZHMgQ2xvdWRpbmFyeVRyYW5zZm9ybWFibGUge1xuICAgIGNvbnN0cnVjdG9yKHB1YmxpY0lELCBjbG91ZENvbmZpZywgdXJsQ29uZmlnKSB7XG4gICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovXG4gICAgICAgIHN1cGVyKHB1YmxpY0lELCBjbG91ZENvbmZpZywgdXJsQ29uZmlnLCBuZXcgSW1hZ2VUcmFuc2Zvcm1hdGlvbigpKTtcbiAgICB9XG59XG5leHBvcnQgeyBDbG91ZGluYXJ5SW1hZ2UgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryImage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudinaryTransformable: () => (/* binding */ CloudinaryTransformable)\n/* harmony export */ });\n/* harmony import */ var _CloudinaryFile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CloudinaryFile.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryFile.js\");\n/* harmony import */ var _cloudinary_transformation_builder_sdk_actions_delivery_DeliveryFormatAction__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js\");\n\n\n/**\n * @desc Cloudinary Transformable interface, extended by any class that needs a Transformation Interface\n * @summary SDK\n * @memberOf SDK\n */\nclass CloudinaryTransformable extends _CloudinaryFile_js__WEBPACK_IMPORTED_MODULE_0__.CloudinaryFile {\n    constructor(publicID, cloudConfig, urlConfig, transformation) {\n        /* istanbul ignore next */\n        super(publicID, cloudConfig, urlConfig);\n        this.transformation = transformation;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Animated} animated\n     * @return {this}\n     */\n    animated(animated) {\n        this.transformation.animated(animated);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Border} border\n     * @return {this}\n     */\n    border(border) {\n        this.transformation.border(border);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Reshape} reshape\n     * @return {this}\n     */\n    reshape(reshape) {\n        this.transformation.reshape(reshape);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Resize} resize\n     * @return {this}\n     */\n    resize(resize) {\n        this.transformation.resize(resize);\n        return this;\n    }\n    /**\n     * @desc An alias to Action Delivery.quality\n     * @param {string|number} quality\n     * @return {this}\n     */\n    quality(quality) {\n        this.addAction(new _cloudinary_transformation_builder_sdk_actions_delivery_DeliveryFormatAction__WEBPACK_IMPORTED_MODULE_1__.DeliveryFormatAction('q', quality));\n        return this;\n    }\n    /**\n     * @desc An alias to Action Delivery.format\n     * @param {string} format\n     * @return {this}\n     */\n    format(format) {\n        this.addAction(new _cloudinary_transformation_builder_sdk_actions_delivery_DeliveryFormatAction__WEBPACK_IMPORTED_MODULE_1__.DeliveryFormatAction('f', format));\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.RoundCorners} roundCorners\n     * @return {this}\n     */\n    roundCorners(roundCorners) {\n        this.transformation.roundCorners(roundCorners);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @return {this}\n     */\n    overlay(overlayAction) {\n        this.transformation.overlay(overlayAction);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Variable} variableAction\n     * @return {this}\n     */\n    addVariable(variableAction) {\n        this.transformation.addVariable(variableAction);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Condition} conditionalAction\n     * @return {this}\n     */\n    conditional(conditionalAction) {\n        this.transformation.conditional(conditionalAction);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Effect} effect\n     * @return {this}\n     */\n    effect(effect) {\n        this.transformation.effect(effect);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Adjust} action\n     * @return {this}\n     */\n    adjust(action) {\n        this.transformation.adjust(action);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Rotate} rotate\n     * @return {this}\n     */\n    rotate(rotate) {\n        this.transformation.rotate(rotate);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.NamedTransformation} namedTransformation\n     * @return {this}\n     */\n    namedTransformation(namedTransformation) {\n        this.transformation.namedTransformation(namedTransformation);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Delivery} deliveryAction\n     * @return {this}\n     */\n    delivery(deliveryAction) {\n        this.transformation.delivery(deliveryAction);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Qualifiers.color} color\n     * @return {this}\n     */\n    backgroundColor(color) {\n        this.transformation.backgroundColor(color);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.PSDTools} action\n     * @return {this}\n     */\n    psdTools(action) {\n        this.transformation.psdTools(action);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Extract} action\n     * @return {this}\n     */\n    extract(action) {\n        this.transformation.extract(action);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Qualifiers.Flag | string} flagQualifier\n     * @return {this}\n     */\n    addFlag(flagQualifier) {\n        this.transformation.addFlag(flagQualifier);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.CustomFunction} customFunction\n     * @return {this}\n     */\n    customFunction(customFunction) {\n        this.transformation.customFunction(customFunction);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {SDK.Action | string} action\n     * @return {this}\n     */\n    addAction(action) {\n        this.transformation.addAction(action);\n        return this;\n    }\n    /**\n     * @description Extend your transformation with another transformation\n     * @param { string | SDK.Transformation } tx\n     */\n    addTransformation(tx) {\n        this.transformation.addTransformation(tx);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @return {string}\n     */\n    toString() {\n        return this.transformation.toString();\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @return {this}\n     */\n    underlay(underlayAction) {\n        this.transformation.underlay(underlayAction);\n        return this;\n    }\n    toURL(overwriteOptions = {}) {\n        return this.createCloudinaryURL(this.transformation, overwriteOptions === null || overwriteOptions === void 0 ? void 0 : overwriteOptions.trackedAnalytics);\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryVideo.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/assets/CloudinaryVideo.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudinaryVideo: () => (/* binding */ CloudinaryVideo)\n/* harmony export */ });\n/* harmony import */ var _CloudinaryTransformable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CloudinaryTransformable.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js\");\n/* harmony import */ var _transformation_VideoTransformation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../transformation/VideoTransformation.js */ \"(ssr)/./node_modules/@cloudinary/transformation-builder-sdk/transformation/VideoTransformation.js\");\n\n\n/**\n * @desc Cloudinary video asset, with video-related transformations\n * @summary SDK\n * @memberOf SDK\n */\nclass CloudinaryVideo extends _CloudinaryTransformable_js__WEBPACK_IMPORTED_MODULE_0__.CloudinaryTransformable {\n    constructor(publicID, cloudConfig, urlConfig) {\n        /* istanbul ignore next */\n        super(publicID, cloudConfig, urlConfig, new _transformation_VideoTransformation_js__WEBPACK_IMPORTED_MODULE_1__.VideoTransformation());\n        this.assetType = 'video';\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.Transcode} action\n     * @return {this}\n     */\n    transcode(action) {\n        this.transformation.transcode(action);\n        return this;\n    }\n    /**\n     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation\n     * @param {Actions.VideoEdit} action\n     * @return {this}\n     */\n    videoEdit(action) {\n        this.transformation.videoEdit(action);\n        return this;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryVideo.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/config/BaseConfig.js":
/*!***************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/config/BaseConfig.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n *\n * @private\n * @param {any} a\n */\nfunction isObject(a) {\n    if (typeof a !== 'object' || a instanceof Array) {\n        return false;\n    }\n    else {\n        return true;\n    }\n}\nclass Config {\n    filterOutNonSupportedKeys(userProvidedConfig, validKeys) {\n        const obj = Object.create({});\n        if (isObject(userProvidedConfig)) {\n            Object.keys(userProvidedConfig).forEach((key) => {\n                if (validKeys.indexOf(key) >= 0) {\n                    obj[key] = userProvidedConfig[key];\n                }\n                else {\n                    console.warn('Warning - unsupported key provided to configuration: ', key);\n                }\n            });\n            return obj;\n        }\n        else {\n            return Object.create({});\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9jb25maWcvQmFzZUNvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsTUFBTSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHVybC1nZW5cXGNvbmZpZ1xcQmFzZUNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqXG4gKiBAcHJpdmF0ZVxuICogQHBhcmFtIHthbnl9IGFcbiAqL1xuZnVuY3Rpb24gaXNPYmplY3QoYSkge1xuICAgIGlmICh0eXBlb2YgYSAhPT0gJ29iamVjdCcgfHwgYSBpbnN0YW5jZW9mIEFycmF5KSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbn1cbmNsYXNzIENvbmZpZyB7XG4gICAgZmlsdGVyT3V0Tm9uU3VwcG9ydGVkS2V5cyh1c2VyUHJvdmlkZWRDb25maWcsIHZhbGlkS2V5cykge1xuICAgICAgICBjb25zdCBvYmogPSBPYmplY3QuY3JlYXRlKHt9KTtcbiAgICAgICAgaWYgKGlzT2JqZWN0KHVzZXJQcm92aWRlZENvbmZpZykpIHtcbiAgICAgICAgICAgIE9iamVjdC5rZXlzKHVzZXJQcm92aWRlZENvbmZpZykuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHZhbGlkS2V5cy5pbmRleE9mKGtleSkgPj0gMCkge1xuICAgICAgICAgICAgICAgICAgICBvYmpba2V5XSA9IHVzZXJQcm92aWRlZENvbmZpZ1trZXldO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdXYXJuaW5nIC0gdW5zdXBwb3J0ZWQga2V5IHByb3ZpZGVkIHRvIGNvbmZpZ3VyYXRpb246ICcsIGtleSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gb2JqO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5jcmVhdGUoe30pO1xuICAgICAgICB9XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgQ29uZmlnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/config/BaseConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/config/URLConfig.js":
/*!**************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/config/URLConfig.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _BaseConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseConfig.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/config/BaseConfig.js\");\n/* harmony import */ var _internal_internalConstants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internal/internalConstants.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/internal/internalConstants.js\");\n\n\nclass URLConfig extends _BaseConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n    /**\n     * @param {IURLConfig} userURLConfig\n     */\n    constructor(userURLConfig) {\n        super();\n        const urlConfig = this.filterOutNonSupportedKeys(userURLConfig, _internal_internalConstants_js__WEBPACK_IMPORTED_MODULE_1__.ALLOWED_URL_CONFIG);\n        Object.assign(this, {\n            secure: true\n        }, urlConfig);\n    }\n    extend(userURLConfig) {\n        const urlConfig = this.filterOutNonSupportedKeys(userURLConfig, _internal_internalConstants_js__WEBPACK_IMPORTED_MODULE_1__.ALLOWED_URL_CONFIG);\n        return new URLConfig(Object.assign({}, this, urlConfig));\n    }\n    /**\n     * @param {string} value Sets the cname\n     */\n    setCname(value) {\n        this.cname = value;\n        return this;\n    }\n    /**\n     * @param {string} value Sets the secureDistribution\n     */\n    setSecureDistribution(value) {\n        this.secureDistribution = value;\n        return this;\n    }\n    /**\n     * @param {boolean} value Sets whether to use a private CDN (Removes cloudName from URL)\n     */\n    setPrivateCdn(value) {\n        this.privateCdn = value;\n        return this;\n    }\n    /**\n     * @param value Sets whether or not to sign the URL\n     */\n    setSignUrl(value) {\n        this.signUrl = value;\n        return this;\n    }\n    /**\n     * @param value Sets whether or not to use a long signature\n     */\n    setLongUrlSignature(value) {\n        this.longUrlSignature = value;\n        return this;\n    }\n    /**\n     * @param value Sets whether or not to shorten the URL\n     */\n    setShorten(value) {\n        this.shorten = value;\n        return this;\n    }\n    /**\n     * @param value Sets whether or not to use a root path\n     */\n    setUseRootPath(value) {\n        this.useRootPath = value;\n        return this;\n    }\n    /**\n     * @param value Sets whether or not to deliver the asset through https\n     */\n    setSecure(value) {\n        this.secure = value;\n        return this;\n    }\n    /**\n     * @param value Sets whether to force a version in the URL\n     */\n    setForceVersion(value) {\n        this.forceVersion = value;\n        return this;\n    }\n    /**\n     * @param params Sets additional params\n     */\n    setQueryParams(params) {\n        this.queryParams = params;\n        return this;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (URLConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/config/URLConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/instance/Cloudinary.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/instance/Cloudinary.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cloudinary: () => (/* binding */ Cloudinary)\n/* harmony export */ });\n/* harmony import */ var _assets_CloudinaryImage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../assets/CloudinaryImage.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryImage.js\");\n/* harmony import */ var _assets_CloudinaryVideo_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../assets/CloudinaryVideo.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/assets/CloudinaryVideo.js\");\n\n\nclass Cloudinary {\n    constructor(cloudinaryConfig) {\n        if (cloudinaryConfig) {\n            this.cloudinaryConfig = cloudinaryConfig;\n        }\n    }\n    image(publicID) {\n        return new _assets_CloudinaryImage_js__WEBPACK_IMPORTED_MODULE_0__.CloudinaryImage(publicID, this.cloudinaryConfig.cloud, this.cloudinaryConfig.url);\n    }\n    video(publicID) {\n        return new _assets_CloudinaryVideo_js__WEBPACK_IMPORTED_MODULE_1__.CloudinaryVideo(publicID, this.cloudinaryConfig.cloud, this.cloudinaryConfig.url);\n    }\n    setConfig(cloudinaryConfig) {\n        this.cloudinaryConfig = cloudinaryConfig;\n        return this;\n    }\n    getConfig() {\n        return this.cloudinaryConfig;\n    }\n    extendConfig() {\n        // Future implementation\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9pbnN0YW5jZS9DbG91ZGluYXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErRDtBQUNBO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHVFQUFlO0FBQ2xDO0FBQ0E7QUFDQSxtQkFBbUIsdUVBQWU7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ3NCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHVybC1nZW5cXGluc3RhbmNlXFxDbG91ZGluYXJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENsb3VkaW5hcnlJbWFnZSB9IGZyb20gXCIuLi9hc3NldHMvQ2xvdWRpbmFyeUltYWdlLmpzXCI7XG5pbXBvcnQgeyBDbG91ZGluYXJ5VmlkZW8gfSBmcm9tIFwiLi4vYXNzZXRzL0Nsb3VkaW5hcnlWaWRlby5qc1wiO1xuY2xhc3MgQ2xvdWRpbmFyeSB7XG4gICAgY29uc3RydWN0b3IoY2xvdWRpbmFyeUNvbmZpZykge1xuICAgICAgICBpZiAoY2xvdWRpbmFyeUNvbmZpZykge1xuICAgICAgICAgICAgdGhpcy5jbG91ZGluYXJ5Q29uZmlnID0gY2xvdWRpbmFyeUNvbmZpZztcbiAgICAgICAgfVxuICAgIH1cbiAgICBpbWFnZShwdWJsaWNJRCkge1xuICAgICAgICByZXR1cm4gbmV3IENsb3VkaW5hcnlJbWFnZShwdWJsaWNJRCwgdGhpcy5jbG91ZGluYXJ5Q29uZmlnLmNsb3VkLCB0aGlzLmNsb3VkaW5hcnlDb25maWcudXJsKTtcbiAgICB9XG4gICAgdmlkZW8ocHVibGljSUQpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBDbG91ZGluYXJ5VmlkZW8ocHVibGljSUQsIHRoaXMuY2xvdWRpbmFyeUNvbmZpZy5jbG91ZCwgdGhpcy5jbG91ZGluYXJ5Q29uZmlnLnVybCk7XG4gICAgfVxuICAgIHNldENvbmZpZyhjbG91ZGluYXJ5Q29uZmlnKSB7XG4gICAgICAgIHRoaXMuY2xvdWRpbmFyeUNvbmZpZyA9IGNsb3VkaW5hcnlDb25maWc7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBnZXRDb25maWcoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNsb3VkaW5hcnlDb25maWc7XG4gICAgfVxuICAgIGV4dGVuZENvbmZpZygpIHtcbiAgICAgICAgLy8gRnV0dXJlIGltcGxlbWVudGF0aW9uXG4gICAgfVxufVxuZXhwb3J0IHsgQ2xvdWRpbmFyeSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/instance/Cloudinary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/internal/internalConstants.js":
/*!************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/internal/internalConstants.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_CLOUD_CONFIG: () => (/* binding */ ALLOWED_CLOUD_CONFIG),\n/* harmony export */   ALLOWED_URL_CONFIG: () => (/* binding */ ALLOWED_URL_CONFIG)\n/* harmony export */ });\n/**\n * This file is for internal constants only.\n * It is not intended for public use and is not part of the public API\n */\n/**\n * @private\n */\nconst ALLOWED_URL_CONFIG = [\n    'cname',\n    'secureDistribution',\n    'privateCdn',\n    'signUrl',\n    'longUrlSignature',\n    'shorten',\n    'useRootPath',\n    'secure',\n    'forceVersion',\n    'analytics',\n    'queryParams'\n];\n/**\n * @private\n */\nconst ALLOWED_CLOUD_CONFIG = [\n    'cloudName',\n    'apiKey',\n    'apiSecret',\n    'authToken'\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9pbnRlcm5hbC9pbnRlcm5hbENvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdXJsLWdlblxcaW50ZXJuYWxcXGludGVybmFsQ29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhpcyBmaWxlIGlzIGZvciBpbnRlcm5hbCBjb25zdGFudHMgb25seS5cbiAqIEl0IGlzIG5vdCBpbnRlbmRlZCBmb3IgcHVibGljIHVzZSBhbmQgaXMgbm90IHBhcnQgb2YgdGhlIHB1YmxpYyBBUElcbiAqL1xuLyoqXG4gKiBAcHJpdmF0ZVxuICovXG5leHBvcnQgY29uc3QgQUxMT1dFRF9VUkxfQ09ORklHID0gW1xuICAgICdjbmFtZScsXG4gICAgJ3NlY3VyZURpc3RyaWJ1dGlvbicsXG4gICAgJ3ByaXZhdGVDZG4nLFxuICAgICdzaWduVXJsJyxcbiAgICAnbG9uZ1VybFNpZ25hdHVyZScsXG4gICAgJ3Nob3J0ZW4nLFxuICAgICd1c2VSb290UGF0aCcsXG4gICAgJ3NlY3VyZScsXG4gICAgJ2ZvcmNlVmVyc2lvbicsXG4gICAgJ2FuYWx5dGljcycsXG4gICAgJ3F1ZXJ5UGFyYW1zJ1xuXTtcbi8qKlxuICogQHByaXZhdGVcbiAqL1xuZXhwb3J0IGNvbnN0IEFMTE9XRURfQ0xPVURfQ09ORklHID0gW1xuICAgICdjbG91ZE5hbWUnLFxuICAgICdhcGlLZXknLFxuICAgICdhcGlTZWNyZXQnLFxuICAgICdhdXRoVG9rZW4nXG5dO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/internal/internalConstants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/internal/url/cloudinaryURL.js":
/*!************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/internal/url/cloudinaryURL.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUrlPrefix: () => (/* binding */ getUrlPrefix),\n/* harmony export */   getUrlVersion: () => (/* binding */ getUrlVersion),\n/* harmony export */   handleAssetType: () => (/* binding */ handleAssetType),\n/* harmony export */   handleDeliveryType: () => (/* binding */ handleDeliveryType)\n/* harmony export */ });\n/* harmony import */ var _urlUtils_isUrl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./urlUtils/isUrl.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isUrl.js\");\n/* harmony import */ var _urlUtils_isFileName_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./urlUtils/isFileName.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isFileName.js\");\n/* harmony import */ var _urlUtils_publicIDContainsVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./urlUtils/publicIDContainsVersion.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/publicIDContainsVersion.js\");\n\n\n\n/**\n * Create the URL prefix for Cloudinary resources.\n * Available use cases\n * http://res.cloudinary.com/{cloudName}\n * https://res.cloudinary.com/{cloudName}\n * https://{cloudName}-res.cloudinary.com/\n * http://{domain}/${cloudName}\n * https://{domain}/${cloudName}\n * https://{domain}\n * @private\n *\n * @param {string} cloudName\n * @param {IURLConfig} urlConfig\n */\nfunction getUrlPrefix(cloudName, urlConfig) {\n    const secure = urlConfig.secure;\n    const privateCDN = urlConfig.privateCdn;\n    const cname = urlConfig.cname;\n    const secureDistribution = urlConfig.secureDistribution;\n    if (!secure && !cname) {\n        return `http://res.cloudinary.com/${cloudName}`;\n    }\n    if (secure && !secureDistribution && privateCDN) {\n        return `https://${cloudName}-res.cloudinary.com`;\n    }\n    if (secure && !secureDistribution) {\n        return `https://res.cloudinary.com/${cloudName}`;\n    }\n    if (secure && secureDistribution && privateCDN) {\n        return `https://${secureDistribution}`;\n    }\n    if (secure && secureDistribution) {\n        return `https://${secureDistribution}/${cloudName}`;\n    }\n    if (!secure && cname) {\n        return `http://${cname}/${cloudName}`;\n    }\n    else {\n        return 'ERROR';\n    }\n}\n/**\n * @private\n * @param assetType\n */\nfunction handleAssetType(assetType) {\n    //default to image\n    if (!assetType) {\n        return 'image';\n    }\n    return assetType;\n}\n/**\n * @private\n * @param deliveryType\n */\nfunction handleDeliveryType(deliveryType) {\n    //default to upload\n    if (!deliveryType) {\n        return 'upload';\n    }\n    return deliveryType;\n}\n/**\n *\n * @param {string} publicID\n * @param {number} version\n * @param {boolean} forceVersion\n */\nfunction getUrlVersion(publicID, version, forceVersion) {\n    const shouldForceVersion = forceVersion !== false;\n    if (version) {\n        return `v${version}`;\n    }\n    // In all these conditions we never force a version\n    if ((0,_urlUtils_publicIDContainsVersion_js__WEBPACK_IMPORTED_MODULE_0__.publicIDContainsVersion)(publicID) || (0,_urlUtils_isUrl_js__WEBPACK_IMPORTED_MODULE_1__.isUrl)(publicID) || (0,_urlUtils_isFileName_js__WEBPACK_IMPORTED_MODULE_2__.isFileName)(publicID)) {\n        return '';\n    }\n    return shouldForceVersion ? 'v1' : '';\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/internal/url/cloudinaryURL.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isFileName.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isFileName.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFileName: () => (/* binding */ isFileName)\n/* harmony export */ });\n/**\n *\n * @param publicID\n */\nfunction isFileName(publicID) {\n    return publicID.indexOf('/') < 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9pbnRlcm5hbC91cmwvdXJsVXRpbHMvaXNGaWxlTmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdXJsLWdlblxcaW50ZXJuYWxcXHVybFxcdXJsVXRpbHNcXGlzRmlsZU5hbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKlxuICogQHBhcmFtIHB1YmxpY0lEXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0ZpbGVOYW1lKHB1YmxpY0lEKSB7XG4gICAgcmV0dXJuIHB1YmxpY0lELmluZGV4T2YoJy8nKSA8IDA7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isFileName.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isUrl.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isUrl.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: () => (/* binding */ isUrl)\n/* harmony export */ });\n/**\n *\n * @param publicID\n */\nfunction isUrl(publicID) {\n    return publicID.match(/^https?:\\//);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9pbnRlcm5hbC91cmwvdXJsVXRpbHMvaXNVcmwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHVybC1nZW5cXGludGVybmFsXFx1cmxcXHVybFV0aWxzXFxpc1VybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqXG4gKiBAcGFyYW0gcHVibGljSURcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzVXJsKHB1YmxpY0lEKSB7XG4gICAgcmV0dXJuIHB1YmxpY0lELm1hdGNoKC9eaHR0cHM/OlxcLy8pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/isUrl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/publicIDContainsVersion.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/internal/url/urlUtils/publicIDContainsVersion.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   publicIDContainsVersion: () => (/* binding */ publicIDContainsVersion)\n/* harmony export */ });\n/**\n *\n * @param publicID\n */\nfunction publicIDContainsVersion(publicID) {\n    return publicID.match(/^v[0-9]+/);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9pbnRlcm5hbC91cmwvdXJsVXRpbHMvcHVibGljSURDb250YWluc1ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHVybC1nZW5cXGludGVybmFsXFx1cmxcXHVybFV0aWxzXFxwdWJsaWNJRENvbnRhaW5zVmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqXG4gKiBAcGFyYW0gcHVibGljSURcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHB1YmxpY0lEQ29udGFpbnNWZXJzaW9uKHB1YmxpY0lEKSB7XG4gICAgcmV0dXJuIHB1YmxpY0lELm1hdGNoKC9edlswLTldKy8pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/internal/url/urlUtils/publicIDContainsVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/internal/utils/packageVersion.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/internal/utils/packageVersion.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   packageVersion: () => (/* binding */ packageVersion)\n/* harmony export */ });\nconst packageVersion = '1.15.0';\n/**\n * Export package version (injected during our build).\n * Reason for this is that If we import values from from package.json,\n * it will cause an error for users who do not have an 'import from json' plugin\n * as part of their build process / bundler.\n */\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9pbnRlcm5hbC91dGlscy9wYWNrYWdlVmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdXJsLWdlblxcaW50ZXJuYWxcXHV0aWxzXFxwYWNrYWdlVmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBwYWNrYWdlVmVyc2lvbiA9ICcxLjE1LjAnO1xuLyoqXG4gKiBFeHBvcnQgcGFja2FnZSB2ZXJzaW9uIChpbmplY3RlZCBkdXJpbmcgb3VyIGJ1aWxkKS5cbiAqIFJlYXNvbiBmb3IgdGhpcyBpcyB0aGF0IElmIHdlIGltcG9ydCB2YWx1ZXMgZnJvbSBmcm9tIHBhY2thZ2UuanNvbixcbiAqIGl0IHdpbGwgY2F1c2UgYW4gZXJyb3IgZm9yIHVzZXJzIHdobyBkbyBub3QgaGF2ZSBhbiAnaW1wb3J0IGZyb20ganNvbicgcGx1Z2luXG4gKiBhcyBwYXJ0IG9mIHRoZWlyIGJ1aWxkIHByb2Nlc3MgLyBidW5kbGVyLlxuICovXG5leHBvcnQgeyBwYWNrYWdlVmVyc2lvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/internal/utils/packageVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/base64Map.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/sdkAnalytics/base64Map.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64Map: () => (/* binding */ base64Map)\n/* harmony export */ });\n/* harmony import */ var _stringPad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringPad.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js\");\n\n/**\n * This file maps sequences of 6 bit binary digits to a character in base64.\n * 000000 -> A\n * 110011 -> Z\n * 111111 -> /\n */\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst base64Map = {};\nlet num = 0;\nchars.split('').forEach((char) => {\n    let key = num.toString(2);\n    key = (0,_stringPad_js__WEBPACK_IMPORTED_MODULE_0__.stringPad)(key, 6, '0');\n    base64Map[key] = char;\n    num++;\n});\n/**\n * Map of [six-bit binary codes] -> [Base64 characters]\n */\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9zZGtBbmFseXRpY3MvYmFzZTY0TWFwLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHdEQUFTO0FBQ25CO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ3FCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdJTjEwXFxEZXNrdG9wXFxrYWxpaGFcXGtoYWxpaGF0YW5iYWFcXG5vZGVfbW9kdWxlc1xcQGNsb3VkaW5hcnlcXHVybC1nZW5cXHNka0FuYWx5dGljc1xcYmFzZTY0TWFwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN0cmluZ1BhZCB9IGZyb20gXCIuL3N0cmluZ1BhZC5qc1wiO1xuLyoqXG4gKiBUaGlzIGZpbGUgbWFwcyBzZXF1ZW5jZXMgb2YgNiBiaXQgYmluYXJ5IGRpZ2l0cyB0byBhIGNoYXJhY3RlciBpbiBiYXNlNjQuXG4gKiAwMDAwMDAgLT4gQVxuICogMTEwMDExIC0+IFpcbiAqIDExMTExMSAtPiAvXG4gKi9cbmNvbnN0IGNoYXJzID0gJ0FCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5Ky8nO1xuY29uc3QgYmFzZTY0TWFwID0ge307XG5sZXQgbnVtID0gMDtcbmNoYXJzLnNwbGl0KCcnKS5mb3JFYWNoKChjaGFyKSA9PiB7XG4gICAgbGV0IGtleSA9IG51bS50b1N0cmluZygyKTtcbiAgICBrZXkgPSBzdHJpbmdQYWQoa2V5LCA2LCAnMCcpO1xuICAgIGJhc2U2NE1hcFtrZXldID0gY2hhcjtcbiAgICBudW0rKztcbn0pO1xuLyoqXG4gKiBNYXAgb2YgW3NpeC1iaXQgYmluYXJ5IGNvZGVzXSAtPiBbQmFzZTY0IGNoYXJhY3RlcnNdXG4gKi9cbmV4cG9ydCB7IGJhc2U2NE1hcCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/base64Map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/encodeVersion.js":
/*!************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/sdkAnalytics/encodeVersion.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeVersion: () => (/* binding */ encodeVersion)\n/* harmony export */ });\n/* harmony import */ var _base64Map_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base64Map.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/base64Map.js\");\n/* harmony import */ var _stringPad_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stringPad.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js\");\n/* harmony import */ var _reverseVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reverseVersion.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/reverseVersion.js\");\n\n\n\n/**\n * @private\n * @description Encodes a semVer-like version string\n * @param {string} semVer Input can be either x.y.z or x.y\n * @return {string} A string built from 3 characters of the base64 table that encode the semVer\n */\nfunction encodeVersion(semVer) {\n    let strResult = '';\n    // support x.y or x.y.z by using 'parts' as a variable\n    const parts = semVer.split('.').length;\n    const paddedStringLength = parts * 6; // we pad to either 12 or 18 characters\n    // reverse (but don't mirror) the version. 1.5.15 -> 15.5.1\n    // Pad to two spaces, 15.5.1 -> 15.05.01\n    const paddedReversedSemver = (0,_reverseVersion_js__WEBPACK_IMPORTED_MODULE_0__.reverseVersion)(semVer);\n    // turn 15.05.01 to a string '150501' then to a number 150501\n    const num = parseInt(paddedReversedSemver.split('.').join(''));\n    // Represent as binary, add left padding to 12 or 18 characters.\n    // 150,501 -> 100100101111100101\n    let paddedBinary = num.toString(2);\n    paddedBinary = (0,_stringPad_js__WEBPACK_IMPORTED_MODULE_1__.stringPad)(paddedBinary, paddedStringLength, '0');\n    // Stop in case an invalid version number was provided\n    // paddedBinary must be built from sections of 6 bits\n    if (paddedBinary.length % 6 !== 0) {\n        throw 'Version must be smaller than 43.21.26)';\n    }\n    // turn every 6 bits into a character using the base64Map\n    paddedBinary.match(/.{1,6}/g).forEach((bitString) => {\n        // console.log(bitString);\n        strResult += _base64Map_js__WEBPACK_IMPORTED_MODULE_2__.base64Map[bitString];\n    });\n    return strResult;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/encodeVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/getAnalyticsOptions.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/sdkAnalytics/getAnalyticsOptions.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAnalyticsOptions: () => (/* binding */ getAnalyticsOptions)\n/* harmony export */ });\n/**\n * @private\n * @description Gets the analyticsOptions from options- should include sdkSemver, techVersion, sdkCode, and feature\n * @param {ITrackedPropertiesThroughAnalytics} options\n * @returns {IAnalyticsOptions}\n */\nfunction getAnalyticsOptions(options) {\n    const analyticsOptions = {\n        sdkSemver: options.sdkSemver,\n        techVersion: options.techVersion,\n        sdkCode: options.sdkCode,\n        product: options.product,\n        feature: '0',\n    };\n    if (options.accessibility) {\n        analyticsOptions.feature = 'D';\n    }\n    if (options.lazyload) {\n        analyticsOptions.feature = 'C';\n    }\n    if (options.responsive) {\n        analyticsOptions.feature = 'A';\n    }\n    if (options.placeholder) {\n        analyticsOptions.feature = 'B';\n    }\n    return analyticsOptions;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9zZGtBbmFseXRpY3MvZ2V0QW5hbHl0aWNzT3B0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvQ0FBb0M7QUFDL0MsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdXJsLWdlblxcc2RrQW5hbHl0aWNzXFxnZXRBbmFseXRpY3NPcHRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHByaXZhdGVcbiAqIEBkZXNjcmlwdGlvbiBHZXRzIHRoZSBhbmFseXRpY3NPcHRpb25zIGZyb20gb3B0aW9ucy0gc2hvdWxkIGluY2x1ZGUgc2RrU2VtdmVyLCB0ZWNoVmVyc2lvbiwgc2RrQ29kZSwgYW5kIGZlYXR1cmVcbiAqIEBwYXJhbSB7SVRyYWNrZWRQcm9wZXJ0aWVzVGhyb3VnaEFuYWx5dGljc30gb3B0aW9uc1xuICogQHJldHVybnMge0lBbmFseXRpY3NPcHRpb25zfVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0QW5hbHl0aWNzT3B0aW9ucyhvcHRpb25zKSB7XG4gICAgY29uc3QgYW5hbHl0aWNzT3B0aW9ucyA9IHtcbiAgICAgICAgc2RrU2VtdmVyOiBvcHRpb25zLnNka1NlbXZlcixcbiAgICAgICAgdGVjaFZlcnNpb246IG9wdGlvbnMudGVjaFZlcnNpb24sXG4gICAgICAgIHNka0NvZGU6IG9wdGlvbnMuc2RrQ29kZSxcbiAgICAgICAgcHJvZHVjdDogb3B0aW9ucy5wcm9kdWN0LFxuICAgICAgICBmZWF0dXJlOiAnMCcsXG4gICAgfTtcbiAgICBpZiAob3B0aW9ucy5hY2Nlc3NpYmlsaXR5KSB7XG4gICAgICAgIGFuYWx5dGljc09wdGlvbnMuZmVhdHVyZSA9ICdEJztcbiAgICB9XG4gICAgaWYgKG9wdGlvbnMubGF6eWxvYWQpIHtcbiAgICAgICAgYW5hbHl0aWNzT3B0aW9ucy5mZWF0dXJlID0gJ0MnO1xuICAgIH1cbiAgICBpZiAob3B0aW9ucy5yZXNwb25zaXZlKSB7XG4gICAgICAgIGFuYWx5dGljc09wdGlvbnMuZmVhdHVyZSA9ICdBJztcbiAgICB9XG4gICAgaWYgKG9wdGlvbnMucGxhY2Vob2xkZXIpIHtcbiAgICAgICAgYW5hbHl0aWNzT3B0aW9ucy5mZWF0dXJlID0gJ0InO1xuICAgIH1cbiAgICByZXR1cm4gYW5hbHl0aWNzT3B0aW9ucztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/getAnalyticsOptions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/getSDKAnalyticsSignature.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/sdkAnalytics/getSDKAnalyticsSignature.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSDKAnalyticsSignature: () => (/* binding */ getSDKAnalyticsSignature)\n/* harmony export */ });\n/* harmony import */ var _encodeVersion_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./encodeVersion.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/encodeVersion.js\");\n/* harmony import */ var _getAnalyticsOptions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getAnalyticsOptions.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/getAnalyticsOptions.js\");\n/* harmony import */ var _internal_utils_packageVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/utils/packageVersion.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/internal/utils/packageVersion.js\");\n\n\n\n/**\n * @private\n * @description Try to get the node version out of process, if browser just return 0.0.0\n */\nfunction getNodeVersion() {\n    const failedVersion = '0.0.0';\n    if (typeof window !== 'undefined') {\n        return failedVersion;\n    }\n    else {\n        // node env\n        try {\n            return process.versions.node || failedVersion;\n        }\n        catch (e) {\n            return failedVersion;\n        }\n    }\n}\n/**\n * @private\n * @description Ensure that all values ITrackedPropertiesThroughAnalytics are populated.\n * Accept a partial map of values and returns the complete interface of ITrackedPropertiesThroughAnalytics\n * @param {ITrackedPropertiesThroughAnalytics} trackedAnalytics\n * @param {ITrackedPropertiesThroughAnalytics} trackedAnalytics\n */\nfunction ensureShapeOfTrackedProperties(trackedAnalytics) {\n    // try to get the process version from node, but if we're on the client return 0.0.0\n    const defaults = {\n        techVersion: getNodeVersion(),\n        sdkCode: 'T',\n        sdkSemver: _internal_utils_packageVersion_js__WEBPACK_IMPORTED_MODULE_0__.packageVersion.split('-')[0],\n        product: 'A',\n        responsive: false,\n        placeholder: false,\n        lazyload: false,\n        accessibility: false\n    };\n    if (!trackedAnalytics) {\n        return defaults;\n    }\n    else {\n        return Object.assign(Object.assign({}, defaults), trackedAnalytics);\n    }\n}\n/**\n * @private\n * @description Creates the complete SDK signature by using all the values provided by ITrackedPropertiesThroughAnalytics\n *              Creation of the signature\n *              - Set the AlgoVersion of the encoding, this is an internal letter that represents the version\n *                of our encoding algorithm, it will allow us to perform breaking changes if we'll need them.\n *              - Take the constant SDK code (Arbitrary letter chosen for each SDK, for Base that letter is 'T')\n *                this is used to tell apart which SDK is being tracked.\n *              - Take the {major.minor} versions of the node version (techVersion) (14.2, 16.2 etc.)\n *              - Take the full semver of the SDK you wish to track\n *              - Take the features used(lazy, placeholder etc.) and turn them to a letter (for example accessibility -> D)\n *              - Before appending the string, the Versions must be encoded, see the function `encodeVersion` for more details\n *              - Append all the variables to a single string\n *              - In any case of an error, return the single letter 'E'\n *\n * @return {string} sdkAnalyticsSignature\n */\nfunction getSDKAnalyticsSignature(_trackedAnalytics) {\n    const trackedAnalytics = ensureShapeOfTrackedProperties(_trackedAnalytics);\n    const analyticsOptions = (0,_getAnalyticsOptions_js__WEBPACK_IMPORTED_MODULE_1__.getAnalyticsOptions)(trackedAnalytics);\n    try {\n        const twoPartVersion = removePatchFromSemver(analyticsOptions.techVersion);\n        const encodedSDKVersion = (0,_encodeVersion_js__WEBPACK_IMPORTED_MODULE_2__.encodeVersion)(analyticsOptions.sdkSemver);\n        const encodedTechVersion = (0,_encodeVersion_js__WEBPACK_IMPORTED_MODULE_2__.encodeVersion)(twoPartVersion);\n        const featureCode = analyticsOptions.feature;\n        const SDKCode = analyticsOptions.sdkCode;\n        const product = analyticsOptions.product;\n        const algoVersion = 'B'; // The algo version is determined here, it should not be an argument\n        return `${algoVersion}${product}${SDKCode}${encodedSDKVersion}${encodedTechVersion}${featureCode}`;\n    }\n    catch (e) {\n        // Either SDK or Node versions were unparsable\n        return 'E';\n    }\n}\n/**\n * @private\n * @description Removes patch version from the semver if it exists\n *              Turns x.y.z OR x.y into x.y\n * @param {'x.y.z' | 'x.y' | string} semVerStr\n */\nfunction removePatchFromSemver(semVerStr) {\n    const parts = semVerStr.split('.');\n    return `${parts[0]}.${parts[1]}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/getSDKAnalyticsSignature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/reverseVersion.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/sdkAnalytics/reverseVersion.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reverseVersion: () => (/* binding */ reverseVersion)\n/* harmony export */ });\n/* harmony import */ var _stringPad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringPad.js */ \"(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js\");\n\n/**\n * @private\n * @description Reverses the version positions, x.y.z turns to z.y.x\n *              Pads each segment with '0' so they have length of 2\n *              Example: 1.2.3 -> 03.02.01\n * @param {string} semVer Input can be either x.y.z or x.y\n * @return {string} in the form of zz.yy.xx (\n */\nfunction reverseVersion(semVer) {\n    if (semVer.split('.').length < 2) {\n        throw new Error('invalid semVer, must have at least two segments');\n    }\n    // Split by '.', reverse, create new array with padded values and concat it together\n    return semVer.split('.').reverse().map((segment) => {\n        // try to cast to number\n        const asNumber = +segment;\n        if (isNaN(asNumber) || asNumber < 0) {\n            throw 'Invalid version number provided';\n        }\n        return (0,_stringPad_js__WEBPACK_IMPORTED_MODULE_0__.stringPad)(segment, 2, '0');\n    }).join('.');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNsb3VkaW5hcnkvdXJsLWdlbi9zZGtBbmFseXRpY3MvcmV2ZXJzZVZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZLFFBQVE7QUFDcEI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSx3REFBUztBQUN4QixLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcbm9kZV9tb2R1bGVzXFxAY2xvdWRpbmFyeVxcdXJsLWdlblxcc2RrQW5hbHl0aWNzXFxyZXZlcnNlVmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdHJpbmdQYWQgfSBmcm9tIFwiLi9zdHJpbmdQYWQuanNcIjtcbi8qKlxuICogQHByaXZhdGVcbiAqIEBkZXNjcmlwdGlvbiBSZXZlcnNlcyB0aGUgdmVyc2lvbiBwb3NpdGlvbnMsIHgueS56IHR1cm5zIHRvIHoueS54XG4gKiAgICAgICAgICAgICAgUGFkcyBlYWNoIHNlZ21lbnQgd2l0aCAnMCcgc28gdGhleSBoYXZlIGxlbmd0aCBvZiAyXG4gKiAgICAgICAgICAgICAgRXhhbXBsZTogMS4yLjMgLT4gMDMuMDIuMDFcbiAqIEBwYXJhbSB7c3RyaW5nfSBzZW1WZXIgSW5wdXQgY2FuIGJlIGVpdGhlciB4LnkueiBvciB4LnlcbiAqIEByZXR1cm4ge3N0cmluZ30gaW4gdGhlIGZvcm0gb2YgenoueXkueHggKFxuICovXG5leHBvcnQgZnVuY3Rpb24gcmV2ZXJzZVZlcnNpb24oc2VtVmVyKSB7XG4gICAgaWYgKHNlbVZlci5zcGxpdCgnLicpLmxlbmd0aCA8IDIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdpbnZhbGlkIHNlbVZlciwgbXVzdCBoYXZlIGF0IGxlYXN0IHR3byBzZWdtZW50cycpO1xuICAgIH1cbiAgICAvLyBTcGxpdCBieSAnLicsIHJldmVyc2UsIGNyZWF0ZSBuZXcgYXJyYXkgd2l0aCBwYWRkZWQgdmFsdWVzIGFuZCBjb25jYXQgaXQgdG9nZXRoZXJcbiAgICByZXR1cm4gc2VtVmVyLnNwbGl0KCcuJykucmV2ZXJzZSgpLm1hcCgoc2VnbWVudCkgPT4ge1xuICAgICAgICAvLyB0cnkgdG8gY2FzdCB0byBudW1iZXJcbiAgICAgICAgY29uc3QgYXNOdW1iZXIgPSArc2VnbWVudDtcbiAgICAgICAgaWYgKGlzTmFOKGFzTnVtYmVyKSB8fCBhc051bWJlciA8IDApIHtcbiAgICAgICAgICAgIHRocm93ICdJbnZhbGlkIHZlcnNpb24gbnVtYmVyIHByb3ZpZGVkJztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc3RyaW5nUGFkKHNlZ21lbnQsIDIsICcwJyk7XG4gICAgfSkuam9pbignLicpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/reverseVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringPad: () => (/* binding */ stringPad)\n/* harmony export */ });\n/**\n * @private\n * @description Adds left padding to a string with the desired substring the provided number of times\n * @example stringPad(foo, 3, 'a'') // -> aaafoo\n * @param {string} value\n * @param {number} _targetLength\n * @param {string} _padString\n */\nfunction stringPad(value, _targetLength, _padString) {\n    let targetLength = _targetLength >> 0; //truncate if number or convert non-number to 0;\n    let padString = String((typeof _padString !== 'undefined' ? _padString : ' '));\n    if (value.length > targetLength) {\n        return String(value);\n    }\n    else {\n        targetLength = targetLength - value.length;\n        if (targetLength > padString.length) {\n            padString += repeatStringNumTimes(padString, targetLength / padString.length);\n        }\n        return padString.slice(0, targetLength) + String(value);\n    }\n}\n/**\n * @description Repeat a string multiple times, cross-browser-safe alternative to string.repeat()\n * @param string\n * @param _times\n */\nfunction repeatStringNumTimes(string, _times) {\n    let times = _times;\n    let repeatedString = \"\";\n    while (times > 0) {\n        repeatedString += string;\n        times--;\n    }\n    return repeatedString;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js\n");

/***/ })

};
;