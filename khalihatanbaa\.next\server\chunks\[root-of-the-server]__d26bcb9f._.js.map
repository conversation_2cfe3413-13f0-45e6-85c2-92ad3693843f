{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        emailOrPhone: { label: 'البريد الإلكتروني أو رقم الهاتف', type: 'text' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone }\n            ],\n            isActive: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.phone = user.phone\n        token.avatar = user.avatar\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.phone = token.phone as string\n        session.user.avatar = token.avatar as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/login',\n    signUp: '/auth/register'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBAAE,OAAO;oBAAmC,MAAM;gBAAO;gBACvE,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/api/ads/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\n// جلب إعلان محدد\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const ad = await prisma.ad.findUnique({\n      where: { id: params.id },\n      include: {\n        user: {\n          select: { \n            id: true, \n            name: true, \n            avatar: true, \n            ratingAverage: true, \n            ratingCount: true,\n            createdAt: true\n          }\n        },\n        _count: {\n          select: {\n            favorites: true,\n            messages: true\n          }\n        }\n      }\n    })\n\n    if (!ad) {\n      return NextResponse.json(\n        { success: false, error: 'الإعلان غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    // زيادة عدد المشاهدات\n    await prisma.ad.update({\n      where: { id: params.id },\n      data: { views: { increment: 1 } }\n    })\n\n    // تحديث إحصائيات المستخدم\n    await prisma.user.update({\n      where: { id: ad.userId },\n      data: { totalViews: { increment: 1 } }\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: { ...ad, views: ad.views + 1 }\n    })\n\n  } catch (error) {\n    console.error('Error fetching ad:', error)\n    return NextResponse.json(\n      { success: false, error: 'حدث خطأ في جلب الإعلان' },\n      { status: 500 }\n    )\n  }\n}\n\n// تحديث إعلان\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: 'يجب تسجيل الدخول أولاً' },\n        { status: 401 }\n      )\n    }\n\n    const ad = await prisma.ad.findUnique({\n      where: { id: params.id },\n      select: { userId: true }\n    })\n\n    if (!ad) {\n      return NextResponse.json(\n        { success: false, error: 'الإعلان غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    // التحقق من الملكية\n    if (ad.userId !== session.user.id) {\n      return NextResponse.json(\n        { success: false, error: 'غير مسموح لك بتعديل هذا الإعلان' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n    const {\n      title,\n      description,\n      price,\n      category,\n      subCategory,\n      condition,\n      city,\n      region,\n      addressDetail,\n      imageUrls,\n      specifications\n    } = body\n\n    const updatedAd = await prisma.ad.update({\n      where: { id: params.id },\n      data: {\n        ...(title && { title }),\n        ...(description && { description }),\n        ...(price !== undefined && { price }),\n        ...(category && { category }),\n        ...(subCategory !== undefined && { subCategory }),\n        ...(condition && { condition }),\n        ...(city && { city }),\n        ...(region !== undefined && { region }),\n        ...(addressDetail !== undefined && { addressDetail }),\n        ...(imageUrls && { imageUrls }),\n        ...(specifications && { specifications })\n      },\n      include: {\n        user: {\n          select: { id: true, name: true, avatar: true }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: updatedAd,\n      message: 'تم تحديث الإعلان بنجاح'\n    })\n\n  } catch (error) {\n    console.error('Error updating ad:', error)\n    return NextResponse.json(\n      { success: false, error: 'حدث خطأ في تحديث الإعلان' },\n      { status: 500 }\n    )\n  }\n}\n\n// حذف إعلان\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: 'يجب تسجيل الدخول أولاً' },\n        { status: 401 }\n      )\n    }\n\n    const ad = await prisma.ad.findUnique({\n      where: { id: params.id },\n      select: { \n        userId: true, \n        adType: true, \n        isFreeAd: true, \n        isPromoted: true \n      }\n    })\n\n    if (!ad) {\n      return NextResponse.json(\n        { success: false, error: 'الإعلان غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    // التحقق من الملكية\n    if (ad.userId !== session.user.id) {\n      return NextResponse.json(\n        { success: false, error: 'غير مسموح لك بحذف هذا الإعلان' },\n        { status: 403 }\n      )\n    }\n\n    // حذف الإعلان وإعادة العداد للمستخدم في معاملة واحدة\n    await prisma.$transaction(async (tx) => {\n      // حذف الإعلان\n      await tx.ad.delete({\n        where: { id: params.id }\n      })\n\n      // إعادة العداد للمستخدم\n      const updateData: any = {}\n      \n      if (ad.isFreeAd) {\n        updateData.freeAdsUsed = { decrement: 1 }\n      } else if (ad.isPromoted) {\n        updateData.promotedAdsUsed = { decrement: 1 }\n      } else {\n        updateData.paidAdsUsed = { decrement: 1 }\n      }\n\n      await tx.user.update({\n        where: { id: session.user.id },\n        data: updateData\n      })\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: 'تم حذف الإعلان بنجاح'\n    })\n\n  } catch (error) {\n    console.error('Error deleting ad:', error)\n    return NextResponse.json(\n      { success: false, error: 'حدث خطأ في حذف الإعلان' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;wBACR,eAAe;wBACf,aAAa;wBACb,WAAW;oBACb;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,MAAM,CAAC;YACrB,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,MAAM;gBAAE,OAAO;oBAAE,WAAW;gBAAE;YAAE;QAClC;QAEA,0BAA0B;QAC1B,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI,GAAG,MAAM;YAAC;YACvB,MAAM;gBAAE,YAAY;oBAAE,WAAW;gBAAE;YAAE;QACvC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBAAE,GAAG,EAAE;gBAAE,OAAO,GAAG,KAAK,GAAG;YAAE;QACrC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAyB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,QAAQ;gBAAE,QAAQ;YAAK;QACzB;QAEA,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,EAAE;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,WAAW,EACX,KAAK,EACL,QAAQ,EACR,WAAW,EACX,SAAS,EACT,IAAI,EACJ,MAAM,EACN,aAAa,EACb,SAAS,EACT,cAAc,EACf,GAAG;QAEJ,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,MAAM;gBACJ,GAAI,SAAS;oBAAE;gBAAM,CAAC;gBACtB,GAAI,eAAe;oBAAE;gBAAY,CAAC;gBAClC,GAAI,UAAU,aAAa;oBAAE;gBAAM,CAAC;gBACpC,GAAI,YAAY;oBAAE;gBAAS,CAAC;gBAC5B,GAAI,gBAAgB,aAAa;oBAAE;gBAAY,CAAC;gBAChD,GAAI,aAAa;oBAAE;gBAAU,CAAC;gBAC9B,GAAI,QAAQ;oBAAE;gBAAK,CAAC;gBACpB,GAAI,WAAW,aAAa;oBAAE;gBAAO,CAAC;gBACtC,GAAI,kBAAkB,aAAa;oBAAE;gBAAc,CAAC;gBACpD,GAAI,aAAa;oBAAE;gBAAU,CAAC;gBAC9B,GAAI,kBAAkB;oBAAE;gBAAe,CAAC;YAC1C;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAC/C;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,QAAQ;gBACN,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,YAAY;YACd;QACF;QAEA,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,EAAE;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgC,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;YAC/B,cAAc;YACd,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;gBACjB,OAAO;oBAAE,IAAI,OAAO,EAAE;gBAAC;YACzB;YAEA,wBAAwB;YACxB,MAAM,aAAkB,CAAC;YAEzB,IAAI,GAAG,QAAQ,EAAE;gBACf,WAAW,WAAW,GAAG;oBAAE,WAAW;gBAAE;YAC1C,OAAO,IAAI,GAAG,UAAU,EAAE;gBACxB,WAAW,eAAe,GAAG;oBAAE,WAAW;gBAAE;YAC9C,OAAO;gBACL,WAAW,WAAW,GAAG;oBAAE,WAAW;gBAAE;YAC1C;YAEA,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBACnB,OAAO;oBAAE,IAAI,QAAQ,IAAI,CAAC,EAAE;gBAAC;gBAC7B,MAAM;YACR;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAyB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}