'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { AdLimitsCard } from '@/components/dashboard/AdLimitsCard'
import { PlaceholderImage } from '@/components/ui/PlaceholderImage'
import { 
  PlusIcon,
  EyeIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  PencilIcon,
  TrashIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline'

interface UserAd {
  id: string
  title: string
  description: string
  price: number
  category: string
  condition: string
  city: string
  region?: string
  imageUrls: string[]
  views: number
  contactsCount: number
  isActive: boolean
  adType: string
  isFreeAd: boolean
  isPromoted: boolean
  promotedUntil?: string
  expiresAt?: string
  createdAt: string
  _count: {
    favorites: number
    messages: number
  }
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [userAds, setUserAds] = useState<UserAd[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'active' | 'expired' | 'all'>('active')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login')
    }
  }, [status, router])

  useEffect(() => {
    if (status === 'authenticated') {
      fetchUserAds()
    }
  }, [status, activeTab])

  const fetchUserAds = async () => {
    try {
      const response = await fetch(`/api/user/ads?status=${activeTab}`)
      const data = await response.json()
      
      if (data.success) {
        setUserAds(data.data)
      }
    } catch (error) {
      console.error('Error fetching user ads:', error)
    } finally {
      setLoading(false)
    }
  }

  const deleteAd = async (adId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الإعلان؟')) return
    
    try {
      const response = await fetch(`/api/ads/${adId}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        setUserAds(prev => prev.filter(ad => ad.id !== adId))
      } else {
        alert(data.error || 'حدث خطأ في حذف الإعلان')
      }
    } catch (error) {
      console.error('Error deleting ad:', error)
      alert('حدث خطأ في حذف الإعلان')
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SY').format(price)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getAdTypeLabel = (ad: UserAd) => {
    if (ad.isPromoted) return 'مميز'
    if (ad.isFreeAd) return 'مجاني'
    return 'مدفوع'
  }

  const getAdTypeColor = (ad: UserAd) => {
    if (ad.isPromoted) return 'bg-yellow-100 text-yellow-800'
    if (ad.isFreeAd) return 'bg-green-100 text-green-800'
    return 'bg-blue-100 text-blue-800'
  }

  const isExpired = (expiresAt?: string) => {
    if (!expiresAt) return false
    return new Date(expiresAt) < new Date()
  }

  const isPromotionActive = (promotedUntil?: string) => {
    if (!promotedUntil) return false
    return new Date(promotedUntil) > new Date()
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-64 bg-gray-200 rounded-2xl"></div>
              </div>
              <div className="h-96 bg-gray-200 rounded-2xl"></div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-dark-800">لوحة التحكم</h1>
            <p className="text-gray-600 mt-1">إدارة إعلاناتك ومتابعة الإحصائيات</p>
          </div>
          
          <Link
            href="/ads/create"
            className="bg-primary-500 text-white px-6 py-3 rounded-lg hover:bg-primary-600 transition-colors flex items-center"
          >
            <PlusIcon className="h-5 w-5 ml-2" />
            إعلان جديد
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* قائمة الإعلانات */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200">
              {/* تبويبات الإعلانات */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 space-x-reverse px-6">
                  {[
                    { key: 'active', label: 'النشطة', count: userAds.filter(ad => ad.isActive && !isExpired(ad.expiresAt)).length },
                    { key: 'expired', label: 'المنتهية', count: userAds.filter(ad => !ad.isActive || isExpired(ad.expiresAt)).length },
                    { key: 'all', label: 'الكل', count: userAds.length }
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setActiveTab(tab.key as any)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === tab.key
                          ? 'border-primary-500 text-primary-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.label} ({tab.count})
                    </button>
                  ))}
                </nav>
              </div>

              {/* قائمة الإعلانات */}
              <div className="p-6">
                {loading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="flex space-x-4 space-x-reverse">
                          <div className="w-24 h-24 bg-gray-200 rounded-lg"></div>
                          <div className="flex-1 space-y-2">
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : userAds.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <PlusIcon className="h-12 w-12 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إعلانات</h3>
                    <p className="text-gray-500 mb-6">ابدأ بإنشاء إعلانك الأول</p>
                    <Link
                      href="/ads/create"
                      className="inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                    >
                      <PlusIcon className="h-5 w-5 ml-2" />
                      إنشاء إعلان
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {userAds.map((ad) => (
                      <div key={ad.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex space-x-4 space-x-reverse">
                          {/* صورة الإعلان */}
                          <div className="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                            <PlaceholderImage 
                              width={96} 
                              height={96} 
                              text={ad.category}
                              className="w-full h-full"
                            />
                          </div>

                          {/* تفاصيل الإعلان */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 space-x-reverse mb-1">
                                  <Link
                                    href={`/ads/${ad.id}`}
                                    className="text-lg font-semibold text-dark-800 hover:text-primary-500 transition-colors truncate"
                                  >
                                    {ad.title}
                                  </Link>
                                  
                                  {/* شارات نوع الإعلان */}
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAdTypeColor(ad)}`}>
                                    {getAdTypeLabel(ad)}
                                  </span>
                                  
                                  {ad.isPromoted && isPromotionActive(ad.promotedUntil) && (
                                    <StarIcon className="h-4 w-4 text-yellow-500" />
                                  )}
                                  
                                  {isExpired(ad.expiresAt) && (
                                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                      منتهي
                                    </span>
                                  )}
                                </div>
                                
                                <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                                  {ad.description}
                                </p>
                                
                                <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                                  <span className="font-semibold text-primary-500">
                                    {formatPrice(ad.price)} ل.س
                                  </span>
                                  <span>{ad.city}</span>
                                  <span>{formatDate(ad.createdAt)}</span>
                                </div>
                              </div>

                              {/* أزرار الإجراءات */}
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <Link
                                  href={`/ads/${ad.id}/edit`}
                                  className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                                  title="تعديل"
                                >
                                  <PencilIcon className="h-4 w-4" />
                                </Link>
                                <button
                                  onClick={() => deleteAd(ad.id)}
                                  className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                                  title="حذف"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>

                            {/* إحصائيات الإعلان */}
                            <div className="flex items-center space-x-6 space-x-reverse mt-3 pt-3 border-t border-gray-100">
                              <div className="flex items-center text-sm text-gray-500">
                                <EyeIcon className="h-4 w-4 ml-1" />
                                {ad.views} مشاهدة
                              </div>
                              <div className="flex items-center text-sm text-gray-500">
                                <HeartIcon className="h-4 w-4 ml-1" />
                                {ad._count.favorites} مفضلة
                              </div>
                              <div className="flex items-center text-sm text-gray-500">
                                <ChatBubbleLeftRightIcon className="h-4 w-4 ml-1" />
                                {ad._count.messages} رسالة
                              </div>
                              
                              {ad.expiresAt && (
                                <div className="flex items-center text-sm text-gray-500">
                                  <ClockIcon className="h-4 w-4 ml-1" />
                                  ينتهي {formatDate(ad.expiresAt)}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* الشريط الجانبي */}
          <div className="space-y-6">
            {/* بطاقة حدود الإعلانات */}
            <AdLimitsCard />

            {/* روابط سريعة */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-dark-800 mb-4">روابط سريعة</h3>
              <div className="space-y-3">
                <Link
                  href="/messages"
                  className="flex items-center text-gray-600 hover:text-primary-500 transition-colors"
                >
                  <ChatBubbleLeftRightIcon className="h-5 w-5 ml-2" />
                  الرسائل
                </Link>
                <Link
                  href="/favorites"
                  className="flex items-center text-gray-600 hover:text-primary-500 transition-colors"
                >
                  <HeartIcon className="h-5 w-5 ml-2" />
                  المفضلة
                </Link>
                <Link
                  href="/profile"
                  className="flex items-center text-gray-600 hover:text-primary-500 transition-colors"
                >
                  <PencilIcon className="h-5 w-5 ml-2" />
                  الملف الشخصي
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
