'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { CATEGORIES, SYRIAN_CITIES } from '@/types'
import { ChevronRightIcon, PhotoIcon } from '@heroicons/react/24/outline'

export default function NewAdPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    category: '',
    subCategory: '',
    condition: 'مستعمل',
    city: '',
    region: '',
    addressDetail: '',
    images: [] as File[],
    specifications: {}
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول
  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">جاري التحميل...</div>
  }

  if (status === 'unauthenticated') {
    router.push('/auth/login')
    return null
  }

  const steps = [
    { id: 1, name: 'اختيار الفئة', description: 'حدد فئة المنتج' },
    { id: 2, name: 'تفاصيل الإعلان', description: 'أضف العنوان والوصف والسعر' },
    { id: 3, name: 'الصور', description: 'أضف صور المنتج' },
    { id: 4, name: 'الموقع', description: 'حدد موقع المنتج' },
    { id: 5, name: 'المراجعة والنشر', description: 'راجع الإعلان واتشره' }
  ]

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...files].slice(0, 10) // حد أقصى 10 صور
    }))
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    setError('')

    try {
      // هنا سيتم رفع الصور إلى Cloudinary وإنشاء الإعلان
      // للآن سنعرض رسالة نجاح
      alert('تم نشر الإعلان بنجاح!')
      router.push('/')
    } catch (error) {
      setError('حدث خطأ أثناء نشر الإعلان')
    } finally {
      setIsLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-dark-800">اختر فئة المنتج</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.entries(CATEGORIES).map(([key, category]) => (
                <button
                  key={key}
                  onClick={() => setFormData(prev => ({ ...prev, category: key, subCategory: '' }))}
                  className={`p-4 border-2 rounded-lg text-center transition-colors ${
                    formData.category === key
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-primary-300'
                  }`}
                >
                  <div className="text-2xl mb-2">{category.icon}</div>
                  <div className="font-medium">{category.name}</div>
                </button>
              ))}
            </div>

            {formData.category && (
              <div className="mt-6">
                <h4 className="text-md font-medium text-dark-700 mb-3">اختر الفئة الفرعية</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {CATEGORIES[formData.category as keyof typeof CATEGORIES].subCategories.map((subCat) => (
                    <button
                      key={subCat}
                      onClick={() => setFormData(prev => ({ ...prev, subCategory: subCat }))}
                      className={`p-3 border rounded-lg text-sm transition-colors ${
                        formData.subCategory === subCat
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-200 hover:border-primary-300'
                      }`}
                    >
                      {subCat}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-dark-800">تفاصيل الإعلان</h3>
            
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                عنوان الإعلان *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                placeholder="مثال: تويوتا كامري 2018 فل كامل"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                الوصف *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                placeholder="اكتب وصفاً مفصلاً عن المنتج..."
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-dark-700 mb-2">
                  السعر (ل.س) *
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  placeholder="0"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-dark-700 mb-2">
                  حالة المنتج *
                </label>
                <select
                  name="condition"
                  value={formData.condition}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="جديد">جديد</option>
                  <option value="مستعمل">مستعمل</option>
                </select>
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-dark-800">صور المنتج</h3>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
              <div className="mt-4">
                <label htmlFor="images" className="cursor-pointer">
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    اضغط لرفع الصور أو اسحبها هنا
                  </span>
                  <input
                    id="images"
                    name="images"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="sr-only"
                  />
                </label>
                <p className="mt-1 text-xs text-gray-500">
                  PNG, JPG, GIF حتى 10 صور
                </p>
              </div>
            </div>

            {formData.images.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(image)}
                      alt={`صورة ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg"
                    />
                    <button
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-dark-800">موقع المنتج</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-dark-700 mb-2">
                  المحافظة *
                </label>
                <select
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="">اختر المحافظة</option>
                  {SYRIAN_CITIES.map((city) => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-dark-700 mb-2">
                  المنطقة
                </label>
                <input
                  type="text"
                  name="region"
                  value={formData.region}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                  placeholder="مثال: المزة، الشعلان، إلخ"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                تفاصيل العنوان (اختياري)
              </label>
              <textarea
                name="addressDetail"
                value={formData.addressDetail}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                placeholder="أضف تفاصيل إضافية عن الموقع..."
              />
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-dark-800">مراجعة الإعلان</h3>
            
            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <div>
                <span className="font-medium text-dark-700">العنوان: </span>
                <span className="text-dark-600">{formData.title}</span>
              </div>
              <div>
                <span className="font-medium text-dark-700">الفئة: </span>
                <span className="text-dark-600">{formData.category} - {formData.subCategory}</span>
              </div>
              <div>
                <span className="font-medium text-dark-700">السعر: </span>
                <span className="text-primary-600 font-bold">{formData.price} ل.س</span>
              </div>
              <div>
                <span className="font-medium text-dark-700">الحالة: </span>
                <span className="text-dark-600">{formData.condition}</span>
              </div>
              <div>
                <span className="font-medium text-dark-700">الموقع: </span>
                <span className="text-dark-600">{formData.city} - {formData.region}</span>
              </div>
              <div>
                <span className="font-medium text-dark-700">عدد الصور: </span>
                <span className="text-dark-600">{formData.images.length}</span>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                <strong>ملاحظة:</strong> بعد نشر الإعلان، سيكون متاحاً للجميع ويمكن للمهتمين التواصل معك عبر نظام الرسائل الداخلي.
              </p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* مؤشر التقدم */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step.id
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {step.id}
                </div>
                <div className="mr-3 hidden sm:block">
                  <div className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-primary-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </div>
                  <div className="text-xs text-gray-500">{step.description}</div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRightIcon className="w-5 h-5 text-gray-400 mx-4" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* محتوى الخطوة */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          {renderStepContent()}
        </div>

        {/* أزرار التنقل */}
        <div className="flex justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            السابق
          </button>
          
          {currentStep < steps.length ? (
            <button
              onClick={handleNext}
              disabled={
                (currentStep === 1 && (!formData.category || !formData.subCategory)) ||
                (currentStep === 2 && (!formData.title || !formData.description || !formData.price)) ||
                (currentStep === 4 && !formData.city)
              }
              className="px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              التالي
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={isLoading}
              className="px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'جاري النشر...' : 'نشر الإعلان'}
            </button>
          )}
        </div>

        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
            {error}
          </div>
        )}
      </main>

      <Footer />
    </div>
  )
}
