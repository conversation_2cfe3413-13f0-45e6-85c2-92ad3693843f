const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('🔍 فحص البيانات...')
    
    const users = await prisma.user.findMany({
      select: { id: true, name: true, email: true }
    })
    console.log('\n👥 المستخدمين:')
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}) - ID: ${user.id}`)
    })
    
    const ads = await prisma.ad.findMany({
      select: { id: true, title: true, userId: true }
    })
    console.log('\n📢 الإعلانات:')
    ads.forEach(ad => {
      console.log(`- ${ad.title} - ID: ${ad.id} - User: ${ad.userId}`)
    })
    
    const favorites = await prisma.favorite.findMany({
      include: {
        user: { select: { name: true } },
        ad: { select: { title: true } }
      }
    })
    console.log('\n❤️ المفضلة:')
    favorites.forEach(fav => {
      console.log(`- ${fav.user.name} يحب "${fav.ad.title}"`)
    })
    
    const messages = await prisma.message.findMany({
      include: {
        from: { select: { name: true } },
        to: { select: { name: true } }
      }
    })
    console.log('\n💬 الرسائل:')
    messages.forEach(msg => {
      console.log(`- من ${msg.from.name} إلى ${msg.to.name}: ${msg.content}`)
    })
    
  } catch (error) {
    console.error('خطأ:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
