import { BlendModeQualifier } from "./blendMode/BlendModeQualifier.js";
/**
 * @description Defines the mode of blending to use when overlaying an image.
 *
 * <b>Learn more:</b> {@link https://cloudinary.com/documentation/effects_and_artistic_enhancements#blending_and_masking_layers|Overlay blending effects}
 * @namespace BlendMode
 * @memberOf Qualifiers
 * @see To be used with an {@link Actions.Overlay|Overlay}
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.BlendMode
 * @description Add an overlay image blended using the 'multiply' blend mode.
 * @return {Qualifiers.BlendMode.BlendModeQualifier}
 */
declare function multiply(): BlendModeQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.BlendMode
 * @description Add an overlay image blended using the 'screen' blend mode.
 * @return {Qualifiers.BlendMode.BlendModeQualifier}
 */
declare function screen(): BlendModeQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.BlendMode
 * @description Add an overlay image blended using the 'overlay' blend mode.
 * @return {Qualifiers.BlendMode.BlendModeQualifier}
 */
declare function overlay(): BlendModeQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.BlendMode
 * @description Add an overlay image blended using the 'mask' blend mode.
 * @return {Qualifiers.BlendMode.BlendModeQualifier}
 */
declare function mask(): BlendModeQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.BlendMode
 * @description Add an overlay image blended using the 'antiRemoval' blend mode.
 * @param {number} lvl 	The level of distortion. (Range: 1 to 100, Server default: 50)
 * @return {Qualifiers.BlendMode.BlendModeQualifier}
 */
declare function antiRemoval(lvl?: number): BlendModeQualifier;
declare const BlendMode: {
    screen: typeof screen;
    multiply: typeof multiply;
    overlay: typeof overlay;
    mask: typeof mask;
    antiRemoval: typeof antiRemoval;
};
export { BlendMode, screen, multiply, overlay, mask, antiRemoval };
