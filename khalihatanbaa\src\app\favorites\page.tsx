'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { PlaceholderImage } from '@/components/ui/PlaceholderImage'
import { 
  HeartIcon,
  MapPinIcon,
  ClockIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

interface Favorite {
  id: string
  createdAt: string
  ad: {
    id: string
    title: string
    description: string
    price: number
    category: string
    condition: string
    city: string
    region?: string
    imageUrls: string[]
    views: number
    createdAt: string
    user: {
      id: string
      name: string
      avatar?: string
    }
  }
}

export default function FavoritesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [favorites, setFavorites] = useState<Favorite[]>([])
  const [loading, setLoading] = useState(true)
  const [removing, setRemoving] = useState<string | null>(null)

  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول
  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">جاري التحميل...</div>
  }

  if (status === 'unauthenticated') {
    router.push('/auth/login')
    return null
  }

  useEffect(() => {
    fetchFavorites()
  }, [])

  const fetchFavorites = async () => {
    try {
      const response = await fetch('/api/favorites')
      const data = await response.json()
      
      if (data.success) {
        setFavorites(data.data)
      } else {
        console.error('Error fetching favorites:', data.error)
      }
    } catch (error) {
      console.error('Error fetching favorites:', error)
    } finally {
      setLoading(false)
    }
  }

  const removeFromFavorites = async (adId: string) => {
    if (removing) return
    
    setRemoving(adId)
    try {
      const response = await fetch(`/api/favorites?adId=${adId}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        setFavorites(prev => prev.filter(fav => fav.ad.id !== adId))
      } else {
        alert(data.error || 'حدث خطأ في إزالة الإعلان من المفضلة')
      }
    } catch (error) {
      console.error('Error removing from favorites:', error)
      alert('حدث خطأ في إزالة الإعلان من المفضلة')
    } finally {
      setRemoving(null)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SY').format(price)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return 'منذ يوم واحد'
    if (diffDays < 7) return `منذ ${diffDays} أيام`
    if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`
    return `منذ ${Math.ceil(diffDays / 30)} شهر`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="h-48 bg-gray-200"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-dark-800">المفضلة</h1>
          <div className="flex items-center text-gray-600">
            <HeartSolidIcon className="h-5 w-5 text-red-500 ml-2" />
            <span>{favorites.length} إعلان</span>
          </div>
        </div>

        {favorites.length === 0 ? (
          <div className="text-center py-12">
            <HeartIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إعلانات في المفضلة</h3>
            <p className="text-gray-500 mb-6">ابدأ بإضافة الإعلانات التي تعجبك إلى المفضلة</p>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
            >
              تصفح الإعلانات
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {favorites.map((favorite) => (
              <div
                key={favorite.id}
                className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300"
              >
                {/* صورة الإعلان */}
                <div className="relative h-48 bg-gray-200">
                  <PlaceholderImage 
                    width={300} 
                    height={192} 
                    text={`صورة ${favorite.ad.category}`}
                    className="w-full h-full"
                  />
                  
                  {/* زر إزالة من المفضلة */}
                  <button
                    onClick={() => removeFromFavorites(favorite.ad.id)}
                    disabled={removing === favorite.ad.id}
                    className="absolute top-3 left-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors disabled:opacity-50"
                  >
                    {removing === favorite.ad.id ? (
                      <div className="h-5 w-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <TrashIcon className="h-5 w-5 text-red-500" />
                    )}
                  </button>

                  {/* تاريخ الإضافة للمفضلة */}
                  <div className="absolute top-3 right-3 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                    أُضيف {formatDate(favorite.createdAt)}
                  </div>
                </div>

                {/* محتوى الإعلان */}
                <div className="p-4">
                  <Link href={`/ads/${favorite.ad.id}`}>
                    <h3 className="font-semibold text-dark-800 mb-2 hover:text-primary-500 transition-colors line-clamp-2">
                      {favorite.ad.title}
                    </h3>
                  </Link>

                  <div className="flex items-center text-sm text-dark-500 mb-2">
                    <MapPinIcon className="h-4 w-4 ml-1" />
                    {favorite.ad.city} {favorite.ad.region && `- ${favorite.ad.region}`}
                  </div>

                  <div className="flex items-center text-sm text-dark-500 mb-3">
                    <ClockIcon className="h-4 w-4 ml-1" />
                    {formatDate(favorite.ad.createdAt)}
                  </div>

                  <div className="flex items-center justify-between mb-3">
                    <div className="text-lg font-bold text-primary-500">
                      {formatPrice(favorite.ad.price)} <span className="text-sm">ل.س</span>
                    </div>
                    <div className="text-sm text-dark-500">
                      {favorite.ad.views} مشاهدة
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      favorite.ad.condition === 'جديد' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {favorite.ad.condition}
                    </span>
                    
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center ml-2">
                        <span className="text-xs text-primary-600">
                          {favorite.ad.user.name.charAt(0)}
                        </span>
                      </div>
                      <span className="truncate max-w-[100px]">
                        {favorite.ad.user.name}
                      </span>
                    </div>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="mt-4 flex space-x-2 space-x-reverse">
                    <Link
                      href={`/ads/${favorite.ad.id}`}
                      className="flex-1 bg-primary-500 text-white py-2 px-4 rounded-lg hover:bg-primary-600 transition-colors text-center text-sm"
                    >
                      عرض التفاصيل
                    </Link>
                    <Link
                      href={`/messages?with=${favorite.ad.user.id}&ad=${favorite.ad.id}`}
                      className="flex-1 border border-primary-500 text-primary-500 py-2 px-4 rounded-lg hover:bg-primary-50 transition-colors text-center text-sm"
                    >
                      تواصل
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>

      <Footer />
    </div>
  )
}
