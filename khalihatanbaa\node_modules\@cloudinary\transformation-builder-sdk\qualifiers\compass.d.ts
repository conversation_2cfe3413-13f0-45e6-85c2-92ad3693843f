import { CompassQualifier } from "./gravity/qualifiers/compass/CompassQualifier.js";
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description South center part (bottom center).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function south(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description North center part (top center).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function north(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description Middle east part (right).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function east(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description Middle west part (left).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function west(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description North west corner (top left).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function northWest(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description North east corner (top right).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function northEast(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description South west corner (bottom left).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function southWest(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description South east corner (bottom right).
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function southEast(): CompassQualifier;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Compass
 * @description The center of the image.
 * @return {Qualifiers.Compass.CompassQualifier} Compass
 */
declare function center(): CompassQualifier;
/**
 * @description Defines the focal Compass for certain methods of cropping.
 * @namespace Compass
 * @memberOf Qualifiers
 * @see Visit {@link Qualifiers.Gravity|Gravity} for an example
 */
declare class Compass {
    static north: typeof north;
    static west: typeof west;
    static east: typeof east;
    static south: typeof south;
    static center: typeof center;
    static northWest: typeof northWest;
    static southEast: typeof southEast;
    static southWest: typeof southWest;
    static northEast: typeof northEast;
}
export { Compass, north, west, east, south, center, northWest, southEast, southWest, northEast };
