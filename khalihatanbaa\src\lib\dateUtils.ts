/**
 * مكتبة مساعدة لتنسيق التواريخ بشكل آمن
 */

export type DateInput = Date | string | number | null | undefined;

/**
 * تحويل المدخل إلى كائن Date صالح
 */
export function parseDate(input: DateInput): Date | null {
  if (!input) return null;
  
  try {
    const date = new Date(input);
    
    // التحقق من صحة التاريخ
    if (isNaN(date.getTime())) {
      return null;
    }
    
    return date;
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
}

/**
 * تنسيق التاريخ بالتنسيق العربي الكامل
 */
export function formatDate(input: DateInput): string {
  const date = parseDate(input);
  
  if (!date) return 'غير محدد';
  
  try {
    return new Intl.DateTimeFormat('ar-SY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'تاريخ غير صالح';
  }
}

/**
 * تنسيق التاريخ بالتنسيق المختصر
 */
export function formatDateShort(input: DateInput): string {
  const date = parseDate(input);
  
  if (!date) return 'غير محدد';
  
  try {
    return new Intl.DateTimeFormat('ar-SY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'تاريخ غير صالح';
  }
}

/**
 * تنسيق التاريخ النسبي (منذ كم يوم)
 */
export function formatRelativeDate(input: DateInput): string {
  const date = parseDate(input);
  
  if (!date) return 'غير محدد';
  
  try {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'اليوم';
    if (diffDays === 1) return 'أمس';
    if (diffDays < 7) return `منذ ${diffDays} أيام`;
    if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`;
    if (diffDays < 365) return `منذ ${Math.ceil(diffDays / 30)} شهور`;
    return `منذ ${Math.ceil(diffDays / 365)} سنوات`;
  } catch (error) {
    console.error('Error formatting relative date:', error);
    return 'تاريخ غير صالح';
  }
}

/**
 * تنسيق الوقت المتبقي (كم يوم متبقي)
 */
export function formatTimeRemaining(input: DateInput): string {
  const date = parseDate(input);
  
  if (!date) return 'غير محدد';
  
  try {
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return 'منتهي الصلاحية';
    if (diffDays === 0) return 'ينتهي اليوم';
    if (diffDays === 1) return 'ينتهي غداً';
    if (diffDays < 7) return `${diffDays} أيام متبقية`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} أسابيع متبقية`;
    return `${Math.ceil(diffDays / 30)} شهور متبقية`;
  } catch (error) {
    console.error('Error formatting time remaining:', error);
    return 'تاريخ غير صالح';
  }
}

/**
 * تنسيق التاريخ والوقت
 */
export function formatDateTime(input: DateInput): string {
  const date = parseDate(input);
  
  if (!date) return 'غير محدد';
  
  try {
    return new Intl.DateTimeFormat('ar-SY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return 'تاريخ غير صالح';
  }
}

/**
 * تنسيق الوقت فقط
 */
export function formatTime(input: DateInput): string {
  const date = parseDate(input);
  
  if (!date) return 'غير محدد';
  
  try {
    return new Intl.DateTimeFormat('ar-SY', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'وقت غير صالح';
  }
}

/**
 * التحقق من انتهاء صلاحية التاريخ
 */
export function isExpired(input: DateInput): boolean {
  const date = parseDate(input);
  
  if (!date) return false;
  
  return date < new Date();
}

/**
 * التحقق من أن التاريخ اليوم
 */
export function isToday(input: DateInput): boolean {
  const date = parseDate(input);
  
  if (!date) return false;
  
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
}

/**
 * التحقق من أن التاريخ أمس
 */
export function isYesterday(input: DateInput): boolean {
  const date = parseDate(input);
  
  if (!date) return false;
  
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  return (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  );
}
