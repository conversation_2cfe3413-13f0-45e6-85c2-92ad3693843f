// مخطط قاعدة البيانات لمنصة "خَلّيها تنْباع"
// Prisma schema for Khaliha <PERSON> platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// جدول المستخدمين
model User {
  id                String    @id @default(uuid())
  name              String
  email             String    @unique
  phone             String    @unique
  passwordHash      String
  role              String    @default("user") // "user" أو "admin"
  avatar            String?   // رابط الصورة الشخصية
  isActive          Boolean   @default(true)
  freeAdsCount      Int       @default(3) // عدد الإعلانات المجانية المتبقية
  freeAdsExpiresAt  DateTime? // تاريخ انتهاء الإعلانات المجانية
  paidAdsCount      Int       @default(0) // عدد الإعلانات المدفوعة المتبقية
  paidAdsExpiresAt  DateTime? // تاريخ انتهاء الإعلانات المدفوعة
  ratingAverage     Float     @default(0) // متوسط التقييم
  ratingCount       Int       @default(0) // عدد التقييمات
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // العلاقات
  ads               Ad[]
  messagesSent      Message[] @relation("SentMessages")
  messagesReceived  Message[] @relation("ReceivedMessages")
  favorites         Favorite[]
  ratingsGiven      Rating[]  @relation("GivenRatings")
  ratingsReceived   Rating[]  @relation("ReceivedRatings")
  adPackages        AdPackage[]

  @@map("users")
}

// جدول الإعلانات
model Ad {
  id              String    @id @default(uuid())
  title           String
  description     String
  price           Float
  category        String    // "عقارات", "سيارات", "إلكترونيات", إلخ
  subCategory     String?   // "شقة", "تويوتا", "هاتف", إلخ
  condition       String    // "جديد" أو "مستعمل"
  city            String
  region          String?
  addressDetail   String?
  imageUrls       String?   // روابط الصور مفصولة بفواصل
  specifications  Json?     // مواصفات إضافية حسب الفئة
  views           Int       @default(0)
  isActive        Boolean   @default(true)
  isFreeAd        Boolean   @default(false) // هل هو إعلان مجاني
  isPromoted      Boolean   @default(false) // هل هو إعلان مميز
  expiresAt       DateTime? // تاريخ انتهاء الإعلان
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // العلاقات
  userId          String
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages        Message[]
  favorites       Favorite[]

  @@map("ads")
}

// جدول الرسائل
model Message {
  id        String   @id @default(uuid())
  content   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  // العلاقات
  fromId    String
  toId      String
  adId      String?
  from      User     @relation("SentMessages", fields: [fromId], references: [id], onDelete: Cascade)
  to        User     @relation("ReceivedMessages", fields: [toId], references: [id], onDelete: Cascade)
  ad        Ad?      @relation(fields: [adId], references: [id], onDelete: SetNull)

  @@map("messages")
}

// جدول المفضلة
model Favorite {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())

  // العلاقات
  userId    String
  adId      String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  ad        Ad       @relation(fields: [adId], references: [id], onDelete: Cascade)

  @@unique([userId, adId])
  @@map("favorites")
}

// جدول التقييمات
model Rating {
  id        String   @id @default(uuid())
  rating    Int      // من 1 إلى 5
  comment   String?
  createdAt DateTime @default(now())

  // العلاقات
  userId    String   // المقيِّم
  sellerId  String   // البائع المُقيَّم
  giver     User     @relation("GivenRatings", fields: [userId], references: [id], onDelete: Cascade)
  receiver  User     @relation("ReceivedRatings", fields: [sellerId], references: [id], onDelete: Cascade)

  @@unique([userId, sellerId])
  @@map("ratings")
}

// جدول باقات الإعلانات
model AdPackage {
  id          String   @id @default(uuid())
  name        String   // اسم الباقة
  adsCount    Int      // عدد الإعلانات
  price       Float    // السعر بالليرة السورية
  duration    Int      // المدة بالأيام
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())

  // العلاقات
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ad_packages")
}
