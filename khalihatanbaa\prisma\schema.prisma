// مخطط قاعدة البيانات لمنصة "خَلّيها تنْباع"
// Prisma schema for Khaliha Tanbaa platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// جدول المستخدمين
model User {
  id                String    @id @default(uuid())
  name              String
  email             String    @unique
  phone             String    @unique
  passwordHash      String
  role              String    @default("user") // "user" أو "admin"
  avatar            String?   // رابط الصورة الشخصية
  isActive          Boolean   @default(true)
  // إعدادات الإعلانات المجانية
  freeAdsCount      Int       @default(3) // عدد الإعلانات المجانية المتاحة
  freeAdsUsed       Int       @default(0) // عدد الإعلانات المجانية المستخدمة
  freeAdsExpiresAt  DateTime? // تاريخ انتهاء الإعلانات المجانية

  // إعدادات الإعلانات المدفوعة
  paidAdsCount      Int       @default(0) // عدد الإعلانات المدفوعة المتاحة
  paidAdsUsed       Int       @default(0) // عدد الإعلانات المدفوعة المستخدمة
  paidAdsExpiresAt  DateTime? // تاريخ انتهاء الإعلانات المدفوعة

  // إعدادات الإعلانات المميزة
  promotedAdsCount    Int       @default(0) // عدد الإعلانات المميزة المتاحة
  promotedAdsUsed     Int       @default(0) // عدد الإعلانات المميزة المستخدمة
  promotedAdsExpiresAt DateTime? // تاريخ انتهاء الإعلانات المميزة
  // إحصائيات التقييم
  ratingAverage     Float     @default(0) // متوسط التقييم
  ratingCount       Int       @default(0) // عدد التقييمات

  // إحصائيات الاستخدام
  totalViews        Int       @default(0) // إجمالي المشاهدات لجميع إعلانات المستخدم
  totalContacts     Int       @default(0) // إجمالي التواصل مع المستخدم

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // العلاقات
  ads               Ad[]
  messagesSent      Message[] @relation("SentMessages")
  messagesReceived  Message[] @relation("ReceivedMessages")
  favorites         Favorite[]
  ratingsGiven      Rating[]  @relation("GivenRatings")
  ratingsReceived   Rating[]  @relation("ReceivedRatings")
  adPackages        AdPackage[]
  payments          Payment[]
  subscriptions     Subscription[]

  @@map("users")
}

// جدول الإعلانات
model Ad {
  id              String    @id @default(uuid())
  title           String
  description     String
  price           Float
  category        String    // "عقارات", "سيارات", "إلكترونيات", إلخ
  subCategory     String?   // "شقة", "تويوتا", "هاتف", إلخ
  condition       String    // "جديد" أو "مستعمل"
  city            String
  region          String?
  addressDetail   String?
  imageUrls       String[]  // مصفوفة روابط الصور
  specifications  Json?     // مواصفات إضافية حسب الفئة
  views           Int       @default(0)
  contactsCount   Int       @default(0) // عدد مرات التواصل
  isActive        Boolean   @default(true)

  // نوع الإعلان
  adType          String    @default("free") // "free", "paid", "promoted"
  isFreeAd        Boolean   @default(true) // هل هو إعلان مجاني
  isPromoted      Boolean   @default(false) // هل هو إعلان مميز
  promotedUntil   DateTime? // تاريخ انتهاء الترويج

  // تواريخ مهمة
  expiresAt       DateTime? // تاريخ انتهاء الإعلان
  publishedAt     DateTime? // تاريخ النشر (قد يختلف عن تاريخ الإنشاء)

  // إعدادات الدفع
  paymentId       String?   // معرف الدفعة إذا كان مدفوع
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // العلاقات
  userId          String
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages        Message[]
  favorites       Favorite[]
  stats           AdStats[]

  @@map("ads")
}

// جدول الرسائل
model Message {
  id        String   @id @default(uuid())
  content   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  // العلاقات
  fromId    String
  toId      String
  adId      String?
  from      User     @relation("SentMessages", fields: [fromId], references: [id], onDelete: Cascade)
  to        User     @relation("ReceivedMessages", fields: [toId], references: [id], onDelete: Cascade)
  ad        Ad?      @relation(fields: [adId], references: [id], onDelete: SetNull)

  @@map("messages")
}

// جدول المفضلة
model Favorite {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())

  // العلاقات
  userId    String
  adId      String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  ad        Ad       @relation(fields: [adId], references: [id], onDelete: Cascade)

  @@unique([userId, adId])
  @@map("favorites")
}

// جدول التقييمات
model Rating {
  id        String   @id @default(uuid())
  rating    Int      // من 1 إلى 5
  comment   String?
  createdAt DateTime @default(now())

  // العلاقات
  userId    String   // المقيِّم
  sellerId  String   // البائع المُقيَّم
  giver     User     @relation("GivenRatings", fields: [userId], references: [id], onDelete: Cascade)
  receiver  User     @relation("ReceivedRatings", fields: [sellerId], references: [id], onDelete: Cascade)

  @@unique([userId, sellerId])
  @@map("ratings")
}

// جدول باقات الإعلانات
model AdPackage {
  id          String   @id @default(uuid())
  name        String   // اسم الباقة
  adsCount    Int      // عدد الإعلانات
  price       Float    // السعر بالليرة السورية
  duration    Int      // المدة بالأيام
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())

  // العلاقات
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ad_packages")
}

// جدول المدفوعات
model Payment {
  id              String   @id @default(uuid())
  amount          Float    // المبلغ
  currency        String   @default("SYP") // العملة
  status          String   @default("pending") // "pending", "completed", "failed", "refunded"
  paymentMethod   String   // "stripe", "paypal", "bank_transfer"
  transactionId   String?  // معرف المعاملة من بوابة الدفع
  description     String?  // وصف الدفعة
  metadata        Json?    // بيانات إضافية
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // العلاقات
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscriptionId  String?
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)

  @@map("payments")
}

// جدول الاشتراكات
model Subscription {
  id              String   @id @default(uuid())
  name            String   // اسم الاشتراك
  type            String   // "ads_package", "promoted_ads", "premium"
  status          String   @default("active") // "active", "expired", "cancelled"
  adsCount        Int      @default(0) // عدد الإعلانات المتاحة
  promotedAdsCount Int     @default(0) // عدد الإعلانات المميزة المتاحة
  price           Float    // السعر
  duration        Int      // المدة بالأيام
  startsAt        DateTime // تاريخ البداية
  expiresAt       DateTime // تاريخ الانتهاء
  autoRenew       Boolean  @default(false) // التجديد التلقائي
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // العلاقات
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments        Payment[]

  @@map("subscriptions")
}

// جدول إحصائيات الإعلانات
model AdStats {
  id              String   @id @default(uuid())
  date            DateTime @default(now())
  views           Int      @default(0)
  contacts        Int      @default(0)
  favorites       Int      @default(0)

  // العلاقات
  adId            String
  ad              Ad       @relation(fields: [adId], references: [id], onDelete: Cascade)

  @@unique([adId, date])
  @@map("ad_stats")
}
