{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanba<PERSON>/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\n\n// مخطط التحقق من البيانات\nconst registerSchema = z.object({\n  name: z.string().min(2, 'الاسم يجب أن يكون أكثر من حرفين'),\n  email: z.string().email('البريد الإلكتروني غير صحيح'),\n  phone: z.string().min(10, 'رقم الهاتف غير صحيح'),\n  password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')\n})\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    \n    // التحقق من صحة البيانات\n    const validatedData = registerSchema.parse(body)\n    \n    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني أو رقم الهاتف\n    const existingUser = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { email: validatedData.email },\n          { phone: validatedData.phone }\n        ]\n      }\n    })\n\n    if (existingUser) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'المستخدم موجود بالفعل بهذا البريد الإلكتروني أو رقم الهاتف' \n        },\n        { status: 400 }\n      )\n    }\n\n    // تشفير كلمة المرور\n    const hashedPassword = await bcrypt.hash(validatedData.password, 12)\n\n    // إنشاء المستخدم الجديد\n    const user = await prisma.user.create({\n      data: {\n        name: validatedData.name,\n        email: validatedData.email,\n        phone: validatedData.phone,\n        passwordHash: hashedPassword,\n        freeAdsExpiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000) // 15 يوم من الآن\n      },\n      select: {\n        id: true,\n        name: true,\n        email: true,\n        phone: true,\n        role: true,\n        freeAdsCount: true,\n        freeAdsExpiresAt: true,\n        createdAt: true\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: user,\n      message: 'تم إنشاء الحساب بنجاح'\n    })\n\n  } catch (error) {\n    console.error('Registration error:', error)\n    \n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: error.errors[0].message \n        },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'حدث خطأ في إنشاء الحساب' \n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;;;;;AAEA,0BAA0B;AAC1B,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC1B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,yBAAyB;QACzB,MAAM,gBAAgB,eAAe,KAAK,CAAC;QAE3C,iEAAiE;QACjE,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,IAAI;oBACF;wBAAE,OAAO,cAAc,KAAK;oBAAC;oBAC7B;wBAAE,OAAO,cAAc,KAAK;oBAAC;iBAC9B;YACH;QACF;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,cAAc,QAAQ,EAAE;QAEjE,wBAAwB;QACxB,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,MAAM,cAAc,IAAI;gBACxB,OAAO,cAAc,KAAK;gBAC1B,OAAO,cAAc,KAAK;gBAC1B,cAAc;gBACd,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,iBAAiB;YACrF;YACA,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,cAAc;gBACd,kBAAkB;gBAClB,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAChC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}