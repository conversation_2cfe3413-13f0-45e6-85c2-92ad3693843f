"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import {
  PaperAirplaneIcon,
  ArrowLeftIcon,
  UserIcon,
  ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

interface Message {
  id: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  fromId: string;
  toId: string;
  adId?: string;
  from: {
    id: string;
    name: string;
    avatar?: string;
  };
  to: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
}

export default function ConversationPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  const userId = params.userId as string;
  const adId = searchParams.get("adId");

  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState("");
  const [otherUser, setOtherUser] = useState<any>(null);
  const [ad, setAd] = useState<any>(null);
  const [lastMessageCount, setLastMessageCount] = useState(0);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && userId) {
      fetchMessages();
    }
  }, [status, userId, adId, router]);

  // Smart scroll - only scroll for new messages or initial load
  useEffect(() => {
    if (messages.length > lastMessageCount || lastMessageCount === 0) {
      scrollToBottom();
      setLastMessageCount(messages.length);
    }
  }, [messages, lastMessageCount]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const url = new URL("/api/messages", window.location.origin);
      url.searchParams.set("with", userId);
      if (adId) url.searchParams.set("adId", adId);

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.success) {
        setMessages(data.data);

        // استخراج بيانات المستخدم الآخر والإعلان من أول رسالة
        if (data.data.length > 0) {
          const firstMessage = data.data[0];
          setOtherUser(
            firstMessage.fromId === session?.user?.id
              ? firstMessage.to
              : firstMessage.from
          );
          if (firstMessage.ad) {
            setAd(firstMessage.ad);
          }
        }
      } else {
        setError(data.error || "حدث خطأ في جلب الرسائل");
      }
    } catch (error) {
      console.error("Error fetching messages:", error);
      setError("حدث خطأ في الاتصال");
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = useCallback(async () => {
    if (!newMessage.trim() || sending) return;

    const messageContent = newMessage.trim();
    const tempId = `temp-${Date.now()}`;

    // Optimistic update - add message immediately
    const optimisticMessage: Message = {
      id: tempId,
      content: messageContent,
      isRead: false,
      createdAt: new Date().toISOString(),
      fromId: session?.user?.id || "",
      toId: userId,
      adId: adId || undefined,
      from: {
        id: session?.user?.id || "",
        name: session?.user?.name || "",
        avatar: session?.user?.image || undefined,
      },
      to: otherUser || {
        id: userId,
        name: "مستخدم غير معروف",
        avatar: undefined,
      },
      ad: ad || undefined,
    };

    // Add message immediately for instant feedback
    setMessages((prev) => [...prev, optimisticMessage]);
    setNewMessage("");
    setSending(true);
    setError("");

    // Focus back to input
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);

    try {
      const response = await fetch("/api/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toId: userId,
          content: messageContent,
          ...(adId && { adId }),
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Replace optimistic message with real one
        setMessages((prev) =>
          prev.map((msg) => (msg.id === tempId ? data.data : msg))
        );

        // Save user data if first message
        if (messages.length === 0) {
          setOtherUser(data.data.to);
          if (data.data.ad) {
            setAd(data.data.ad);
          }
        }
      } else {
        // Remove optimistic message on error
        setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
        setNewMessage(messageContent); // Restore message
        setError(data.error || "حدث خطأ في إرسال الرسالة");
      }
    } catch (error) {
      console.error("Error sending message:", error);
      // Remove optimistic message on error
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setNewMessage(messageContent); // Restore message
      setError("حدث خطأ في إرسال الرسالة");
    } finally {
      setSending(false);
    }
  }, [
    newMessage,
    sending,
    session?.user,
    userId,
    adId,
    otherUser,
    ad,
    messages.length,
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Fixed formatTime to avoid hydration mismatch
  const formatTime = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("ar-SY", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false
    });
  }, []);

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المحادثة...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-4">
          <div className="flex items-center gap-4">
            <Link
              href="/messages"
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
            </Link>

            <div className="flex items-center gap-3 flex-1">
              {otherUser?.avatar ? (
                <Image
                  src={otherUser.avatar}
                  alt={otherUser.name}
                  width={40}
                  height={40}
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <UserIcon className="h-5 w-5 text-gray-500" />
                </div>
              )}

              <div>
                <h1 className="font-medium text-gray-900">
                  {otherUser?.name || "مستخدم غير معروف"}
                </h1>
                {ad && <p className="text-sm text-gray-600">حول: {ad.title}</p>}
              </div>
            </div>
          </div>
        </div>

        {/* Ad Info */}
        {ad && (
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <Link
              href={`/ads/${ad.id}`}
              className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {ad.imageUrls?.[0] && (
                <Image
                  src={ad.imageUrls[0]}
                  alt={ad.title}
                  width={60}
                  height={60}
                  className="rounded object-cover"
                />
              )}
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{ad.title}</h3>
                <p className="text-lg font-bold text-blue-600">
                  {ad.price.toLocaleString()} ل.س
                </p>
              </div>
            </Link>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mx-4 mt-4">
            <div className="flex items-center gap-2">
              <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 min-h-[400px] max-h-[500px]">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600">لا توجد رسائل حتى الآن</p>
              <p className="text-sm text-gray-500 mt-1">
                ابدأ المحادثة بإرسال رسالة
              </p>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.fromId === session?.user?.id
                    ? "justify-end"
                    : "justify-start"
                }`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.fromId === session?.user?.id
                      ? "bg-blue-600 text-white"
                      : "bg-white text-gray-900 border border-gray-200"
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p
                    className={`text-xs mt-1 ${
                      message.fromId === session?.user?.id
                        ? "text-blue-100"
                        : "text-gray-500"
                    }`}
                  >
                    {formatTime(message.createdAt)}
                  </p>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Send Message */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex gap-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && sendMessage()}
              placeholder="اكتب رسالتك..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={sending}
            />
            <button
              onClick={sendMessage}
              disabled={!newMessage.trim() || sending}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {sending ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <PaperAirplaneIcon className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
