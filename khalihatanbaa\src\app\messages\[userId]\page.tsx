"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import {
  PaperAirplaneIcon,
  ArrowLeftIcon,
  UserIcon,
  ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

interface Message {
  id: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  fromId: string;
  toId: string;
  adId?: string;
  from: {
    id: string;
    name: string;
    avatar?: string;
  };
  to: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
}

export default function ConversationPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  const userId = params.userId as string;
  const adId = searchParams.get("adId");

  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState("");
  const [otherUser, setOtherUser] = useState<any>(null);
  const [ad, setAd] = useState<any>(null);
  const [lastMessageCount, setLastMessageCount] = useState(0);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && userId) {
      fetchMessages();
    }
  }, [status, userId, adId, router]);

  // Smart scroll - only scroll for new messages or initial load
  useEffect(() => {
    if (messages.length > lastMessageCount || lastMessageCount === 0) {
      scrollToBottom();
      setLastMessageCount(messages.length);
    }
  }, [messages, lastMessageCount]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const url = new URL("/api/messages", window.location.origin);
      url.searchParams.set("with", userId);
      if (adId) url.searchParams.set("adId", adId);

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.success) {
        setMessages(data.data);

        // استخراج بيانات المستخدم الآخر والإعلان من أول رسالة
        if (data.data.length > 0) {
          const firstMessage = data.data[0];
          setOtherUser(
            firstMessage.fromId === session?.user?.id
              ? firstMessage.to
              : firstMessage.from
          );
          if (firstMessage.ad) {
            setAd(firstMessage.ad);
          }
        }
      } else {
        setError(data.error || "حدث خطأ في جلب الرسائل");
      }
    } catch (error) {
      console.error("Error fetching messages:", error);
      setError("حدث خطأ في الاتصال");
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = useCallback(async () => {
    if (!newMessage.trim() || sending) return;

    const messageContent = newMessage.trim();
    const tempId = `temp-${Date.now()}`;

    // Optimistic update - add message immediately
    const optimisticMessage: Message = {
      id: tempId,
      content: messageContent,
      isRead: false,
      createdAt: new Date().toISOString(),
      fromId: session?.user?.id || "",
      toId: userId,
      adId: adId || undefined,
      from: {
        id: session?.user?.id || "",
        name: session?.user?.name || "",
        avatar: session?.user?.image || undefined,
      },
      to: otherUser || {
        id: userId,
        name: "مستخدم غير معروف",
        avatar: undefined,
      },
      ad: ad || undefined,
    };

    // Add message immediately for instant feedback
    setMessages((prev) => [...prev, optimisticMessage]);
    setNewMessage("");
    setSending(true);
    setError("");

    // Focus back to input
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);

    try {
      const response = await fetch("/api/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toId: userId,
          content: messageContent,
          ...(adId && { adId }),
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Replace optimistic message with real one
        setMessages((prev) =>
          prev.map((msg) => (msg.id === tempId ? data.data : msg))
        );

        // Save user data if first message
        if (messages.length === 0) {
          setOtherUser(data.data.to);
          if (data.data.ad) {
            setAd(data.data.ad);
          }
        }
      } else {
        // Remove optimistic message on error
        setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
        setNewMessage(messageContent); // Restore message
        setError(data.error || "حدث خطأ في إرسال الرسالة");
      }
    } catch (error) {
      console.error("Error sending message:", error);
      // Remove optimistic message on error
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setNewMessage(messageContent); // Restore message
      setError("حدث خطأ في إرسال الرسالة");
    } finally {
      setSending(false);
    }
  }, [
    newMessage,
    sending,
    session?.user,
    userId,
    adId,
    otherUser,
    ad,
    messages.length,
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Fixed formatTime to avoid hydration mismatch
  const formatTime = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("ar-SY", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  }, []);

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المحادثة...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-4">
          <div className="flex items-center gap-4">
            <Link
              href="/messages"
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
            </Link>

            <div className="flex items-center gap-3 flex-1">
              {otherUser?.avatar ? (
                <Image
                  src={otherUser.avatar}
                  alt={otherUser.name}
                  width={40}
                  height={40}
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <UserIcon className="h-5 w-5 text-gray-500" />
                </div>
              )}

              <div>
                <h1 className="font-medium text-gray-900">
                  {otherUser?.name || "مستخدم غير معروف"}
                </h1>
                {ad && <p className="text-sm text-gray-600">حول: {ad.title}</p>}
              </div>
            </div>
          </div>
        </div>

        {/* Ad Info */}
        {ad && (
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <Link
              href={`/ads/${ad.id}`}
              className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {ad.imageUrls?.[0] && (
                <Image
                  src={ad.imageUrls[0]}
                  alt={ad.title}
                  width={60}
                  height={60}
                  className="rounded object-cover"
                />
              )}
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{ad.title}</h3>
                <p className="text-lg font-bold text-blue-600">
                  {ad.price.toLocaleString()} ل.س
                </p>
              </div>
            </Link>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mx-4 mt-4">
            <div className="flex items-center gap-2">
              <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3 min-h-[400px] max-h-[500px]">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <p className="text-gray-600 font-medium">
                لا توجد رسائل حتى الآن
              </p>
              <p className="text-sm text-gray-500 mt-1">
                ابدأ المحادثة بإرسال رسالة
              </p>
            </div>
          ) : (
            messages.map((message) => {
              const isOwn = message.fromId === session?.user?.id;
              const isTemp = message.id.startsWith("temp-");

              return (
                <div
                  key={message.id}
                  className={`flex ${isOwn ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${
                      isOwn
                        ? `bg-blue-600 text-white ${isTemp ? "opacity-70" : ""}`
                        : "bg-white text-gray-900 border border-gray-200"
                    } ${isTemp ? "animate-pulse" : ""}`}
                  >
                    <p className="text-sm leading-relaxed break-words">
                      {message.content}
                    </p>
                    <div
                      className={`flex items-center justify-between mt-2 ${
                        isOwn ? "text-blue-100" : "text-gray-500"
                      }`}
                    >
                      <p className="text-xs">{formatTime(message.createdAt)}</p>
                      {isOwn && (
                        <div className="flex items-center gap-1">
                          {isTemp ? (
                            <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <svg
                              className="w-3 h-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
          <div ref={messagesEndRef} className="h-1" />
        </div>

        {/* Send Message */}
        <div className="bg-white border-t border-gray-200 p-4 sticky bottom-0">
          {error && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          <div className="flex gap-3 items-end">
            <div className="flex-1">
              <input
                ref={inputRef}
                type="text"
                value={newMessage}
                onChange={(e) => {
                  setNewMessage(e.target.value);
                  setError(""); // Clear error when typing
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                  }
                }}
                placeholder="اكتب رسالتك..."
                className="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200 placeholder-gray-500"
                disabled={sending}
                maxLength={1000}
              />
              {newMessage.length > 800 && (
                <p className="text-xs text-gray-500 mt-1 text-right">
                  {1000 - newMessage.length} حرف متبقي
                </p>
              )}
            </div>

            <button
              onClick={sendMessage}
              disabled={!newMessage.trim() || sending}
              className="p-3 bg-blue-600 text-white rounded-2xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95"
              title="إرسال الرسالة"
            >
              {sending ? (
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
              ) : (
                <PaperAirplaneIcon className="h-5 w-5 transform rotate-180" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
