/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ratings/route";
exports.ids = ["app/api/ratings/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fratings%2Froute&page=%2Fapi%2Fratings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fratings%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fratings%2Froute&page=%2Fapi%2Fratings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fratings%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ratings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ratings/route.ts */ \"(rsc)/./src/app/api/ratings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ratings/route\",\n        pathname: \"/api/ratings\",\n        filename: \"route\",\n        bundlePath: \"app/api/ratings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\api\\\\ratings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ratings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fratings%2Froute&page=%2Fapi%2Fratings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fratings%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ratings/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/ratings/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n\n// مخطط التحقق من إضافة تقييم\nconst ratingSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    sellerId: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, 'معرف البائع مطلوب'),\n    rating: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().min(1, 'التقييم يجب أن يكون من 1 إلى 5').max(5, 'التقييم يجب أن يكون من 1 إلى 5'),\n    comment: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional()\n});\n// جلب التقييمات\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const sellerId = searchParams.get('sellerId');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const skip = (page - 1) * limit;\n        if (!sellerId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف البائع مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // جلب التقييمات\n        const ratings = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.findMany({\n            where: {\n                sellerId: sellerId\n            },\n            include: {\n                giver: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            skip,\n            take: limit\n        });\n        // حساب العدد الإجمالي\n        const total = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.count({\n            where: {\n                sellerId: sellerId\n            }\n        });\n        // حساب متوسط التقييم\n        const avgRating = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.aggregate({\n            where: {\n                sellerId: sellerId\n            },\n            _avg: {\n                rating: true\n            }\n        });\n        // حساب توزيع التقييمات\n        const ratingDistribution = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.groupBy({\n            by: [\n                'rating'\n            ],\n            where: {\n                sellerId: sellerId\n            },\n            _count: {\n                rating: true\n            }\n        });\n        const distribution = [\n            1,\n            2,\n            3,\n            4,\n            5\n        ].map((star)=>({\n                star,\n                count: ratingDistribution.find((r)=>r.rating === star)?._count.rating || 0\n            }));\n        const totalPages = Math.ceil(total / limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ratings,\n                summary: {\n                    total,\n                    average: avgRating._avg.rating || 0,\n                    distribution\n                },\n                pagination: {\n                    page,\n                    limit,\n                    total,\n                    totalPages\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching ratings:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في جلب التقييمات'\n        }, {\n            status: 500\n        });\n    }\n}\n// إضافة تقييم جديد\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يجب تسجيل الدخول أولاً'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = ratingSchema.parse(body);\n        // التحقق من عدم تقييم النفس\n        if (validatedData.sellerId === session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكنك تقييم نفسك'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود البائع\n        const seller = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: validatedData.sellerId\n            }\n        });\n        if (!seller) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'البائع غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من عدم وجود تقييم سابق\n        const existingRating = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.findUnique({\n            where: {\n                userId_sellerId: {\n                    userId: session.user.id,\n                    sellerId: validatedData.sellerId\n                }\n            }\n        });\n        if (existingRating) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لقد قمت بتقييم هذا البائع من قبل'\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء التقييم\n        const rating = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.create({\n            data: {\n                userId: session.user.id,\n                sellerId: validatedData.sellerId,\n                rating: validatedData.rating,\n                comment: validatedData.comment\n            },\n            include: {\n                giver: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                },\n                receiver: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        // تحديث متوسط التقييم للبائع\n        const avgRating = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.aggregate({\n            where: {\n                sellerId: validatedData.sellerId\n            },\n            _avg: {\n                rating: true\n            },\n            _count: {\n                rating: true\n            }\n        });\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.update({\n            where: {\n                id: validatedData.sellerId\n            },\n            data: {\n                ratingAverage: avgRating._avg.rating || 0,\n                ratingCount: avgRating._count.rating || 0\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: rating,\n            message: 'تم إضافة التقييم بنجاح'\n        });\n    } catch (error) {\n        console.error('Error adding rating:', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_4__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: error.errors[0].message\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في إضافة التقييم'\n        }, {\n            status: 500\n        });\n    }\n}\n// تحديث تقييم موجود\nasync function PUT(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يجب تسجيل الدخول أولاً'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { ratingId, rating: newRating, comment } = body;\n        if (!ratingId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف التقييم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // البحث عن التقييم\n        const existingRating = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.findUnique({\n            where: {\n                id: ratingId\n            }\n        });\n        if (!existingRating) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'التقييم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من الملكية\n        if (existingRating.userId !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'غير مسموح لك بتعديل هذا التقييم'\n            }, {\n                status: 403\n            });\n        }\n        // تحديث التقييم\n        const updatedRating = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.update({\n            where: {\n                id: ratingId\n            },\n            data: {\n                ...newRating && {\n                    rating: newRating\n                },\n                ...comment !== undefined && {\n                    comment\n                }\n            },\n            include: {\n                giver: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                },\n                receiver: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        // إعادة حساب متوسط التقييم للبائع\n        const avgRating = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.rating.aggregate({\n            where: {\n                sellerId: existingRating.sellerId\n            },\n            _avg: {\n                rating: true\n            },\n            _count: {\n                rating: true\n            }\n        });\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.update({\n            where: {\n                id: existingRating.sellerId\n            },\n            data: {\n                ratingAverage: avgRating._avg.rating || 0,\n                ratingCount: avgRating._count.rating || 0\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedRating,\n            message: 'تم تحديث التقييم بنجاح'\n        });\n    } catch (error) {\n        console.error('Error updating rating:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في تحديث التقييم'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ratings/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                emailOrPhone: {\n                    label: \"البريد الإلكتروني أو رقم الهاتف\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"كلمة المرور\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.emailOrPhone || !credentials?.password) {\n                    return null;\n                }\n                // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findFirst({\n                    where: {\n                        OR: [\n                            {\n                                email: credentials.emailOrPhone\n                            },\n                            {\n                                phone: credentials.emailOrPhone\n                            }\n                        ],\n                        isActive: true\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                // التحقق من كلمة المرور\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(credentials.password, user.passwordHash);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    name: user.name,\n                    email: user.email,\n                    phone: user.phone,\n                    role: user.role,\n                    avatar: user.avatar || undefined\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.phone = user.phone;\n                token.avatar = user.avatar;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.phone = token.phone;\n                session.user.avatar = token.avatar;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO0tBQVE7QUFDaEIsR0FBRTtBQUVKLElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknXSxcbiAgfSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fratings%2Froute&page=%2Fapi%2Fratings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fratings%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();