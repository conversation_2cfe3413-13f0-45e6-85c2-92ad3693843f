module.exports = {

"[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) return obj;
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") return {
        default: obj
    };
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) return cache.get(obj);
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);
            else newObj[key] = obj[key];
        }
    }
    newObj.default = obj;
    if (cache) cache.set(obj, newObj);
    return newObj;
}
exports._ = _interop_require_wildcard;
}}),
"[project]/node_modules/@swc/helpers/cjs/_class_private_field_loose_base.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _class_private_field_loose_base(receiver, privateKey) {
    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {
        throw new TypeError("attempted to use private field on non-instance");
    }
    return receiver;
}
exports._ = _class_private_field_loose_base;
}}),
"[project]/node_modules/@swc/helpers/cjs/_class_private_field_loose_key.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var id = 0;
function _class_private_field_loose_key(name) {
    return "__private_" + id++ + "_" + name;
}
exports._ = _class_private_field_loose_key;
}}),
"[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
exports._ = _interop_require_default;
}}),
"[project]/node_modules/@swc/helpers/cjs/_tagged_template_literal_loose.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _tagged_template_literal_loose(strings, raw) {
    if (!raw) raw = strings.slice(0);
    strings.raw = raw;
    return strings;
}
exports._ = _tagged_template_literal_loose;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function MagnifyingGlassIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(MagnifyingGlassIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js [app-ssr] (ecmascript) <export default as MagnifyingGlassIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MagnifyingGlassIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MagnifyingGlassIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MagnifyingGlassIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/PlusIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function PlusIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M12 4.5v15m7.5-7.5h-15"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(PlusIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/PlusIcon.js [app-ssr] (ecmascript) <export default as PlusIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PlusIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PlusIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PlusIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/PlusIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function ChatBubbleLeftRightIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(ChatBubbleLeftRightIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js [app-ssr] (ecmascript) <export default as ChatBubbleLeftRightIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChatBubbleLeftRightIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChatBubbleLeftRightIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChatBubbleLeftRightIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/HeartIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function HeartIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(HeartIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/HeartIcon.js [app-ssr] (ecmascript) <export default as HeartIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HeartIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$HeartIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$HeartIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/HeartIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/UserIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function UserIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(UserIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/UserIcon.js [app-ssr] (ecmascript) <export default as UserIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UserIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$UserIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$UserIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/UserIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function Bars3Icon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(Bars3Icon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js [app-ssr] (ecmascript) <export default as Bars3Icon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Bars3Icon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$Bars3Icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$Bars3Icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function XMarkIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M6 18 18 6M6 6l12 12"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(XMarkIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js [app-ssr] (ecmascript) <export default as XMarkIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "XMarkIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$XMarkIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$XMarkIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function ChartBarIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(ChartBarIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js [app-ssr] (ecmascript) <export default as ChartBarIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChartBarIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChartBarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChartBarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function PhotoIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(PhotoIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js [app-ssr] (ecmascript) <export default as PhotoIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PhotoIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PhotoIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PhotoIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/StarIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function StarIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(StarIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/StarIcon.js [app-ssr] (ecmascript) <export default as StarIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "StarIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$StarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$StarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/StarIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/solid/esm/StarIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function StarIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 24 24",
        fill: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        fillRule: "evenodd",
        d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",
        clipRule: "evenodd"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(StarIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/solid/esm/StarIcon.js [app-ssr] (ecmascript) <export default as StarIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "StarIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$StarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$StarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/solid/esm/StarIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ShareIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function ShareIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(ShareIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ShareIcon.js [app-ssr] (ecmascript) <export default as ShareIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ShareIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ShareIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ShareIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ShareIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function MapPinIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(MapPinIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js [app-ssr] (ecmascript) <export default as MapPinIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MapPinIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MapPinIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MapPinIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ClockIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function ClockIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(ClockIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/ClockIcon.js [app-ssr] (ecmascript) <export default as ClockIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ClockIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClockIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClockIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ClockIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/EyeIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function EyeIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        strokeWidth: 1.5,
        stroke: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(EyeIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/outline/esm/EyeIcon.js [app-ssr] (ecmascript) <export default as EyeIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EyeIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$EyeIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$EyeIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/EyeIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@heroicons/react/24/solid/esm/HeartIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function HeartIcon({ title, titleId, ...props }, svgRef) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 24 24",
        fill: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z"
    }));
}
const ForwardRef = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(HeartIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}}),
"[project]/node_modules/@heroicons/react/24/solid/esm/HeartIcon.js [app-ssr] (ecmascript) <export default as HeartIcon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HeartIcon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$HeartIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$HeartIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/solid/esm/HeartIcon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@cloudinary-util/util/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/lib/cloudinary.ts
__turbopack_context__.s({
    "convertColorHexToRgb": (()=>convertColorHexToRgb),
    "encodeBase64": (()=>encodeBase64),
    "getFormat": (()=>getFormat),
    "getPublicId": (()=>getPublicId),
    "getTransformations": (()=>getTransformations),
    "objectHasKey": (()=>objectHasKey),
    "parseUrl": (()=>parseUrl),
    "pollForProcessingImage": (()=>pollForProcessingImage),
    "sortByKey": (()=>sortByKey),
    "testColorIsHex": (()=>testColorIsHex)
});
var REGEX_VERSION = /\/v\d+\//;
var REGEX_FORMAT = /\.(ai|avif|gif|png|webp|bmp|bw|djvu|dng|ps|ept|eps|eps3|fbx|flif|gif|glb|gltf|heif|heic|ico|indd|jpg|jpe|jpeg|jp2|wdp|jxr|hdp|obj|pdf|ply|png|psd|arw|cr2|svg|tga|tif|tiff|u3ma|usdz|webp|3g2|3gp|avi|flv|m3u8|ts|m2ts|mts|mov|mkv|mp4|mpeg|mpd|mxf|ogv|webm|wmv)$/i;
var REGEX_URL = /https?:\/\/(?<host>[^/]+)\/(?<cloudName>[^/]+)?\/?(?<assetType>image|images|video|videos|raw|files)\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)?\/?(?<signature>s--([a-zA-Z0-9_-]{8}|[a-zA-Z0-9_-]{32})--)?\/?(?<transformations>(?:[^_/]+_[^,/]+,?\/?)*\/)*(?<version>v\d+|\w{1,2})\/(?<publicId>[^\s]+)$/;
var ASSET_TYPES_SEO = [
    "images",
    "videos",
    "files"
];
var CLOUDINARY_DEFAULT_HOST = "res.cloudinary.com";
function parseUrl(src) {
    if (typeof src !== "string") {
        throw new Error(`Failed to parse URL - Invalid src: Is not a string`);
    }
    const hasVersion = REGEX_VERSION.test(src);
    if (!hasVersion) {
        throw new Error(`Failed to parse URL - Invalid src: Does not include version (Ex: /v1234/)`);
    }
    const [baseUrlWithExtension, queryString] = src.split("?");
    const format = getFormat(baseUrlWithExtension);
    let baseUrl = baseUrlWithExtension;
    if (format) {
        baseUrl = baseUrlWithExtension.replace(new RegExp(`${format}$`), "");
    }
    const results = baseUrl.match(REGEX_URL);
    const transformations = results?.groups?.transformations?.split("/").filter((t)=>!!t);
    const parts = {
        ...results?.groups,
        format,
        seoSuffix: void 0,
        transformations: transformations || [],
        queryParams: {},
        version: results?.groups?.version ? parseInt(results.groups.version.replace("v", "")) : void 0
    };
    if (parts.host === CLOUDINARY_DEFAULT_HOST && !parts.cloudName) {
        throw new Error("Failed to parse URL - Invalid src: Cloudinary URL delivered from res.cloudinary.com must include Cloud Name (ex: res.cloudinary.com/<Cloud Name>/image/...)");
    }
    if (queryString) {
        parts.queryParams = queryString.split("&").reduce((prev, curr)=>{
            const [key, value] = curr.split("=");
            prev[key] = value;
            return prev;
        }, {});
    }
    if (parts.assetType && ASSET_TYPES_SEO.includes(parts.assetType)) {
        const publicIdParts = parts.publicId?.split("/") || [];
        parts.seoSuffix = publicIdParts.pop();
        parts.publicId = publicIdParts.join("/");
    }
    if (parts.publicId) {
        parts.publicId = decodeURIComponent(parts.publicId);
    }
    return parts;
}
function getPublicId(src) {
    const { publicId } = parseUrl(src) || {};
    return publicId;
}
function getTransformations(src) {
    const { transformations = [] } = parseUrl(src) || {};
    return transformations.map((t)=>t.split(","));
}
function getFormat(src) {
    const matches = src.match(REGEX_FORMAT);
    if (matches === null) return;
    return matches[0];
}
async function pollForProcessingImage(options) {
    try {
        const response = await fetch(options.src);
        if (response.status === 423) {
            await new Promise((resolve)=>setTimeout(resolve, 500));
            return await pollForProcessingImage(options);
        }
        if (!response.ok) {
            return {
                success: false,
                status: response.status,
                error: response.headers.get("x-cld-error") || "Unknown error"
            };
        }
        return {
            success: true,
            status: response.status
        };
    } catch (error) {
        return {
            success: false,
            status: 500,
            error: error.message || "Network error"
        };
    }
}
// src/lib/colors.ts
function testColorIsHex(value) {
    if (typeof value !== "string") return false;
    return !!value.startsWith("#");
}
function convertColorHexToRgb(value) {
    return `rgb:${value.replace("#", "")}`;
}
// src/lib/util.ts
function encodeBase64(value) {
    if (typeof btoa === "function") {
        return btoa(value);
    }
    if (typeof Buffer !== "undefined") {
        return Buffer.from(value).toString("base64");
    }
}
function objectHasKey(obj, key) {
    return Object.prototype.hasOwnProperty.call(obj, key);
}
function sortByKey(array = [], key, type = "asc") {
    function compare(a, b) {
        let keyA = a[key];
        let keyB = b[key];
        if (typeof keyA === "string") {
            keyA = keyA.toLowerCase();
        }
        if (typeof keyB === "string") {
            keyB = keyB.toLowerCase();
        }
        if (keyA < keyB) return -1;
        if (keyA > keyB) return 1;
        return 0;
    }
    let newArray = [
        ...array
    ];
    if (typeof key !== "string") return newArray;
    newArray = newArray.sort(compare);
    if (type === "desc") {
        return newArray.reverse();
    }
    return newArray;
}
;
}}),
"[project]/node_modules/@cloudinary-util/url-loader/node_modules/@cloudinary-util/util/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/lib/cloudinary.ts
__turbopack_context__.s({
    "convertColorHexToRgb": (()=>convertColorHexToRgb),
    "encodeBase64": (()=>encodeBase64),
    "getFormat": (()=>getFormat),
    "getPublicId": (()=>getPublicId),
    "getTransformations": (()=>getTransformations),
    "objectHasKey": (()=>objectHasKey),
    "parseUrl": (()=>parseUrl),
    "pollForProcessingImage": (()=>pollForProcessingImage),
    "sortByKey": (()=>sortByKey),
    "testColorIsHex": (()=>testColorIsHex)
});
var REGEX_VERSION = /\/v\d+\//;
var REGEX_FORMAT = /\.(ai|avif|gif|png|webp|bmp|bw|djvu|dng|ps|ept|eps|eps3|fbx|flif|gif|glb|gltf|heif|heic|ico|indd|jpg|jpe|jpeg|jp2|wdp|jxr|hdp|obj|pdf|ply|png|psd|arw|cr2|svg|tga|tif|tiff|u3ma|usdz|webp|3g2|3gp|avi|flv|m3u8|ts|m2ts|mts|mov|mkv|mp4|mpeg|mpd|mxf|ogv|webm|wmv)$/i;
var REGEX_URL = /https?:\/\/(?<host>[^/]+)\/(?<cloudName>[^/]+)?\/?(?<assetType>image|images|video|videos|raw|files)\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)?\/?(?<signature>s--([a-zA-Z0-9_-]{8}|[a-zA-Z0-9_-]{32})--)?\/?(?<transformations>(?:[^_/]+_[^,/]+,?\/?)*\/)*(?<version>v\d+|\w{1,2})\/(?<publicId>[^\s]+)$/;
var ASSET_TYPES_SEO = [
    "images",
    "videos",
    "files"
];
var CLOUDINARY_DEFAULT_HOST = "res.cloudinary.com";
function parseUrl(src) {
    if (typeof src !== "string") {
        throw new Error(`Failed to parse URL - Invalid src: Is not a string`);
    }
    const hasVersion = REGEX_VERSION.test(src);
    if (!hasVersion) {
        throw new Error(`Failed to parse URL - Invalid src: Does not include version (Ex: /v1234/)`);
    }
    const [baseUrlWithExtension, queryString] = src.split("?");
    const format = getFormat(baseUrlWithExtension);
    let baseUrl = baseUrlWithExtension;
    if (format) {
        baseUrl = baseUrlWithExtension.replace(new RegExp(`${format}$`), "");
    }
    const results = baseUrl.match(REGEX_URL);
    const transformations = results?.groups?.transformations?.split("/").filter((t)=>!!t);
    const parts = {
        ...results?.groups,
        format,
        seoSuffix: void 0,
        transformations: transformations || [],
        queryParams: {},
        version: results?.groups?.version ? parseInt(results.groups.version.replace("v", "")) : void 0
    };
    if (parts.host === CLOUDINARY_DEFAULT_HOST && !parts.cloudName) {
        throw new Error("Failed to parse URL - Invalid src: Cloudinary URL delivered from res.cloudinary.com must include Cloud Name (ex: res.cloudinary.com/<Cloud Name>/image/...)");
    }
    if (queryString) {
        parts.queryParams = queryString.split("&").reduce((prev, curr)=>{
            const [key, value] = curr.split("=");
            prev[key] = value;
            return prev;
        }, {});
    }
    if (parts.assetType && ASSET_TYPES_SEO.includes(parts.assetType)) {
        const publicIdParts = parts.publicId?.split("/") || [];
        parts.seoSuffix = publicIdParts.pop();
        parts.publicId = publicIdParts.join("/");
    }
    if (parts.publicId) {
        parts.publicId = decodeURIComponent(parts.publicId);
    }
    return parts;
}
function getPublicId(src) {
    const { publicId } = parseUrl(src) || {};
    return publicId;
}
function getTransformations(src) {
    const { transformations = [] } = parseUrl(src) || {};
    return transformations.map((t)=>t.split(","));
}
function getFormat(src) {
    const matches = src.match(REGEX_FORMAT);
    if (matches === null) return;
    return matches[0];
}
async function pollForProcessingImage(options) {
    try {
        const response = await fetch(options.src);
        if (response.status === 423) {
            await new Promise((resolve)=>setTimeout(resolve, 500));
            return await pollForProcessingImage(options);
        }
        return response.ok;
    } catch  {
        return false;
    }
}
// src/lib/colors.ts
function testColorIsHex(value) {
    if (typeof value !== "string") return false;
    return !!value.startsWith("#");
}
function convertColorHexToRgb(value) {
    return `rgb:${value.replace("#", "")}`;
}
// src/lib/util.ts
function encodeBase64(value) {
    if (typeof btoa === "function") {
        return btoa(value);
    }
    if (typeof Buffer !== "undefined") {
        return Buffer.from(value).toString("base64");
    }
}
function objectHasKey(obj, key) {
    return Object.prototype.hasOwnProperty.call(obj, key);
}
function sortByKey(array = [], key, type = "asc") {
    function compare(a, b) {
        let keyA = a[key];
        let keyB = b[key];
        if (typeof keyA === "string") {
            keyA = keyA.toLowerCase();
        }
        if (typeof keyB === "string") {
            keyB = keyB.toLowerCase();
        }
        if (keyA < keyB) {
            return -1;
        }
        if (keyA > keyB) {
            return 1;
        }
        return 0;
    }
    let newArray = [
        ...array
    ];
    if (typeof key !== "string") return newArray;
    newArray = newArray.sort(compare);
    if (type === "desc") {
        return newArray.reverse();
    }
    return newArray;
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @summary SDK
 * @memberOf SDK
 */ __turbopack_context__.s({
    "QualifierValue": (()=>QualifierValue)
});
class QualifierValue {
    /**
     *
     * @param {QualifierValue | QualifierValue[] | any[] | string | number}qualifierValue
     */ constructor(qualifierValue){
        this.values = [];
        this.delimiter = ':'; // {value}{delimiter}{value}...
        if (this.hasValue(qualifierValue)) {
            this.addValue(qualifierValue);
        }
    }
    /**
     * @description Joins the provided values with the provided delimiter
     */ toString() {
        return this.values.join(this.delimiter);
    }
    /**
     * @description Checks if the provided argument has a value
     * @param {any} v
     * @private
     * @return {boolean}
     */ hasValue(v) {
        return typeof v !== 'undefined' && v !== null && v !== '';
    }
    /**
     * @desc Adds a value for the this qualifier instance
     * @param {any} value
     * @return {this}
     */ addValue(value) {
        // Append value or array of values
        if (Array.isArray(value)) {
            this.values = this.values.concat(value);
        } else {
            this.values.push(value);
        }
        // Remove falsy values
        this.values = this.values.filter((v)=>this.hasValue(v));
        return this;
    }
    /**
     * @description Sets the delimiter for this instance
     * @param delimiter
     */ setDelimiter(delimiter) {
        this.delimiter = delimiter;
        return this;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UnsupportedError": (()=>UnsupportedError),
    "createUnsupportedError": (()=>createUnsupportedError)
});
class UnsupportedError extends Error {
    constructor(message = 'Unsupported'){
        super(message);
    }
}
/**
 * Creates a new UnsupportedError
 * @param message
 */ function createUnsupportedError(message) {
    return new UnsupportedError(message);
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/qualifierToJson.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "qualifierToJson": (()=>qualifierToJson)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$unsupportedError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js [app-ssr] (ecmascript)");
;
function qualifierToJson() {
    return this._qualifierModel || {
        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$unsupportedError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createUnsupportedError"])(`unsupported qualifier ${this.constructor.name}`)
    };
}
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/QualifierModel.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QualifierModel": (()=>QualifierModel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$qualifierToJson$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/qualifierToJson.js [app-ssr] (ecmascript)");
;
class QualifierModel {
    constructor(){
        this._qualifierModel = {};
    }
    toJson() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$qualifierToJson$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["qualifierToJson"].apply(this);
    }
}
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Qualifier": (()=>Qualifier)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$QualifierModel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/QualifierModel.js [app-ssr] (ecmascript)");
;
;
/**
 * @summary SDK
 * @memberOf SDK
 */ class Qualifier extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$QualifierModel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QualifierModel"] {
    constructor(key, qualifierValue){
        super();
        this.delimiter = '_'; // {key}{delimiter}{qualifierValue}
        this.key = key;
        if (qualifierValue instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QualifierValue"]) {
            this.qualifierValue = qualifierValue;
        } else {
            this.qualifierValue = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QualifierValue"]();
            this.qualifierValue.addValue(qualifierValue);
        }
    }
    toString() {
        const { key, delimiter, qualifierValue } = this;
        return `${key}${delimiter}${qualifierValue.toString()}`;
    }
    addValue(value) {
        this.qualifierValue.addValue(value);
        return this;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FlagQualifier": (()=>FlagQualifier)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js [app-ssr] (ecmascript)");
;
;
/**
 * @memberOf Qualifiers.Flag
 * @extends {SDK.Qualifier}
 * @description the FlagQualifier class
 */ class FlagQualifier extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Qualifier"] {
    constructor(flagType, flagValue){
        let qualifierValue;
        if (flagValue) {
            qualifierValue = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QualifierValue"]([
                flagType,
                `${flagValue}`
            ]).setDelimiter(':');
        } else {
            qualifierValue = flagType;
        }
        super('fl', qualifierValue);
        this.flagValue = flagValue;
    }
    toString() {
        return super.toString().replace(/\./g, '%2E');
    }
    getFlagValue() {
        return this.flagValue;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/dataStructureUtils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Sort a map by key
 * @private
 * @param map <string, any>
 * @Return array of map's values sorted by key
 */ __turbopack_context__.s({
    "isString": (()=>isString),
    "mapToSortedArray": (()=>mapToSortedArray)
});
function mapToSortedArray(map, flags) {
    const array = Array.from(map.entries());
    // objects from the Array.from() method above are stored in array of arrays:
    // [[qualifierKey, QualifierObj], [qualifierKey, QualifierObj]]
    // Flags is an array of FlagQualifierObj
    // We need to convert it to the same form: [flagQualifier] =>  ['fl', flagQualifier]
    flags.forEach((flag)=>{
        array.push([
            'fl',
            flag
        ]); // push ['fl', flagQualifier]
    });
    return array.sort().map((v)=>v[1]);
}
/**
 * Checks if `value` is a string.
 * @private
 * @param {*} value The value to check.
 * @return {boolean} `true` if `value` is a string, else `false`.
 */ function isString(value) {
    return typeof value === 'string' || value instanceof String;
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/actionToJson.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "actionToJson": (()=>actionToJson)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$unsupportedError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js [app-ssr] (ecmascript)");
;
function actionToJson() {
    var _a, _b, _c;
    const actionModelIsNotEmpty = this._actionModel && Object.keys(this._actionModel).length;
    const sourceTransformationError = (_c = (_b = (_a = this._actionModel) === null || _a === void 0 ? void 0 : _a.source) === null || _b === void 0 ? void 0 : _b.transformation) === null || _c === void 0 ? void 0 : _c.error;
    // Should return error when there is unsupported transformation inside
    if (sourceTransformationError && sourceTransformationError instanceof Error) {
        return {
            error: sourceTransformationError
        };
    }
    if (actionModelIsNotEmpty) {
        return this._actionModel;
    }
    return {
        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$unsupportedError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createUnsupportedError"])(`unsupported action ${this.constructor.name}`)
    };
}
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/ActionModel.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ActionModel": (()=>ActionModel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$actionToJson$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/actionToJson.js [app-ssr] (ecmascript)");
;
class ActionModel {
    constructor(){
        this._actionModel = {};
    }
    toJson() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$actionToJson$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["actionToJson"].apply(this);
    }
}
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Action": (()=>Action)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$dataStructureUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/dataStructureUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$ActionModel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/ActionModel.js [app-ssr] (ecmascript)");
;
;
;
;
/**
 * @summary SDK
 * @memberOf SDK
 * @description Defines the category of transformation to perform.
 */ class Action extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$ActionModel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionModel"] {
    constructor(){
        super(...arguments);
        // We're using map, to overwrite existing keys. for example:
        // addParam(w_100).addQualifier(w_200) should result in w_200. and not w_100,w_200
        this.qualifiers = new Map();
        // Unlike regular qualifiers, there can be multiple flags in each url component /fl_1,fl_2/
        // If the falgs are added to the qualifiers map, only a single flag could exist in a component (it's a map)
        // So flags are stored separately until the very end because of that reason
        this.flags = [];
        this.delimiter = ','; // {qualifier}{delimiter}{qualifier} for example: `${'w_100'}${','}${'c_fill'}`
        this.actionTag = ''; // A custom name tag to identify this action in the future
    }
    prepareQualifiers() {}
    /**
     * @description Returns the custom name tag that was given to this action
     * @return {string}
     */ getActionTag() {
        return this.actionTag;
    }
    /**
     * @description Sets the custom name tag for this action
     * @return {this}
     */ setActionTag(tag) {
        this.actionTag = tag;
        return this;
    }
    /**
     * @description Calls toString() on all child qualifiers (implicitly by using .join()).
     * @return {string}
     */ toString() {
        this.prepareQualifiers();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$dataStructureUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapToSortedArray"])(this.qualifiers, this.flags).join(this.delimiter);
    }
    /**
     * @description Adds the parameter to the action.
     * @param {SDK.Qualifier} qualifier
     * @return {this}
     */ addQualifier(qualifier) {
        // if string, find the key and value
        if (typeof qualifier === 'string') {
            const [key, value] = qualifier.toLowerCase().split('_');
            if (key === 'fl') {
                // if string qualifier is a flag, store it in the flags arrays
                this.flags.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"](value));
            } else {
                // if the string qualifier is not a flag, create a new qualifier from it
                this.qualifiers.set(key, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Qualifier"](key, value));
            }
        } else {
            // if a qualifier object, insert to the qualifiers map
            this.qualifiers.set(qualifier.key, qualifier);
        }
        return this;
    }
    /**
     * @description Adds a flag to the current action.
     * @param {Qualifiers.Flag} flag
     * @return {this}
     */ addFlag(flag) {
        if (typeof flag === 'string') {
            this.flags.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"](flag));
        } else {
            if (flag instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]) {
                this.flags.push(flag);
            }
        }
        return this;
    }
    addValueToQualifier(qualifierKey, qualifierValue) {
        this.qualifiers.get(qualifierKey).addValue(qualifierValue);
        return this;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/prepareColor.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Returns RGB or Color
 * @private
 * @param color
 */ __turbopack_context__.s({
    "prepareColor": (()=>prepareColor)
});
function prepareColor(color) {
    if (color) {
        return color.match(/^#/) ? `rgb:${color.substr(1)}` : color;
    } else {
        return color;
    }
}
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/actions/background/actions/BackgroundColor.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BackgroundColor": (()=>BackgroundColor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$Action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$prepareColor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/prepareColor.js [app-ssr] (ecmascript)");
;
;
;
;
/**
 * @extends SDK.Action
 * @description A class for background transformations.
 */ class BackgroundColor extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$Action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"] {
    constructor(color){
        super();
        this._actionModel = {};
        this.addQualifier(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Qualifier"]('b', new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QualifierValue"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$prepareColor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["prepareColor"])(color)).setDelimiter('_')));
        this._actionModel.color = color;
        this._actionModel.actionType = 'backgroundColor';
    }
    static fromJson(actionModel) {
        const { color } = actionModel;
        // We are using this() to allow inheriting classes to use super.fromJson.apply(this, [actionModel])
        // This allows the inheriting classes to determine the class to be created
        const result = new this(color);
        return result;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/RawAction.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RawAction": (()=>RawAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$unsupportedError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/unsupportedError.js [app-ssr] (ecmascript)");
;
/**
 * @summary SDK
 * @memberOf SDK
 * @description Defines an action that's a string literal, no validations or manipulations are performed
 */ class RawAction {
    constructor(raw){
        this.raw = raw;
    }
    toString() {
        return this.raw;
    }
    toJson() {
        return {
            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$unsupportedError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createUnsupportedError"])(`unsupported action ${this.constructor.name}`)
        };
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/IErrorObject.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Validates obj is an instance of IErrorObject
 * @param obj
 */ __turbopack_context__.s({
    "isErrorObject": (()=>isErrorObject)
});
function isErrorObject(obj) {
    const errorObj = obj;
    return 'error' in errorObj && !!errorObj.error;
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @description Defines flags that you can use to alter the default transformation behavior.
 * @namespace Flag
 * @memberOf Qualifiers
 */ __turbopack_context__.s({
    "Flag": (()=>Flag),
    "animated": (()=>animated),
    "animatedPng": (()=>animatedPng),
    "animatedWebP": (()=>animatedWebP),
    "anyFormat": (()=>anyFormat),
    "attachment": (()=>attachment),
    "clip": (()=>clip),
    "clipEvenOdd": (()=>clipEvenOdd),
    "custom": (()=>custom),
    "forceIcc": (()=>forceIcc),
    "forceStrip": (()=>forceStrip),
    "getInfo": (()=>getInfo),
    "hlsv3": (()=>hlsv3),
    "ignoreInitialAspectRatio": (()=>ignoreInitialAspectRatio),
    "ignoreMaskChannels": (()=>ignoreMaskChannels),
    "immutableCache": (()=>immutableCache),
    "keepAttribution": (()=>keepAttribution),
    "keepDar": (()=>keepDar),
    "keepIptc": (()=>keepIptc),
    "layerApply": (()=>layerApply),
    "lossy": (()=>lossy),
    "mono": (()=>mono),
    "noOverflow": (()=>noOverflow),
    "noStream": (()=>noStream),
    "png24": (()=>png24),
    "png32": (()=>png32),
    "png8": (()=>png8),
    "preserveTransparency": (()=>preserveTransparency),
    "progressive": (()=>progressive),
    "rasterize": (()=>rasterize),
    "regionRelative": (()=>regionRelative),
    "relative": (()=>relative),
    "sanitize": (()=>sanitize),
    "splice": (()=>splice),
    "streamingAttachment": (()=>streamingAttachment),
    "stripProfile": (()=>stripProfile),
    "tiff8Lzw": (()=>tiff8Lzw),
    "tiled": (()=>tiled),
    "truncateTS": (()=>truncateTS),
    "waveform": (()=>waveform)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js [app-ssr] (ecmascript)");
;
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Used when delivering a video file as an image format that supports animation, such as animated WebP.
 * Plays all frames rather than just delivering the first one as a static image.
 * Use this flag in addition to the flag or parameter controlling the delivery format,
 * for example f_auto or fl_awebp.

 * Note: When delivering a video in GIF format, it is delivered as an animated GIF by default and this flag is not
 * necessary. To deliver a single frame of a video in GIF format, use the page parameter.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function animated() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('animated');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description When converting animated images to WebP format, generate an animated WebP from all the frames in the
 * original
 * animated file instead of only from the first still frame.
 *
 * Note that animated WebPs are not supported in all browsers and versions.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function animatedWebP() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('awebp');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description When used together with automatic quality (q_auto):
 * allow switching to PNG8 encoding if the quality algorithm decides that it's more efficient.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function anyFormat() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('any_format');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description When converting animated images to PNG format, generates an animated PNG from all the frames in the
 * original
 * animated file instead of only from the first still frame.
 *
 * Note that animated PNGs are not supported in all browsers and versions.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function animatedPng() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('apng');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Trims pixels according to a clipping path included in the original image
 * (e.g., manually created using PhotoShop).
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function clip() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('clip');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Trims pixels according to a clipping path included in the original image (e.g., manually created
 * using PhotoShop)
 * using an evenodd clipping rule.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function clipEvenOdd() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('clip_evenodd');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Instructs Cloudinary to clear all image meta-data (IPTC, Exif and XMP) while applying an incoming
 * transformation.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function forceStrip() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('force_strip');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Allows custom flag
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function custom(value) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"](value);
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Adds ICC color space metadata to the image, even when the original image doesn't contain any ICC data.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function forceIcc() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('force_icc');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Delivers the image as an attachment.
 * @param {string} filename The attachment's filename
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function attachment(filename) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('attachment', filename);
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Returns metadata of the input asset and of the transformed output asset in JSON instead of the
 * transformed image.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function getInfo() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('getinfo');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Deliver an HLS adaptive bitrate streaming file as HLS v3 instead of the default version (HLS v4).
 * Delivering in this format requires a private CDN configuration.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function hlsv3() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('hlsv3');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Sets the cache-control to immutable for the asset.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function immutableCache() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('immutable_cache');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description * Allows specifying only either width or height so the value of the second axis remains as is, and is not
 * recalculated to maintain the aspect ratio of the original image.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function ignoreInitialAspectRatio() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('ignore_aspect_ratio');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Keeps the copyright related fields when stripping meta-data.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function keepAttribution() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('keep_attribution');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * Keep the Display Aspect Ratio metadata of the uploaded video (if it’s different from the current video
 * dimensions).
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function keepDar() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('keep_dar');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Keeps all meta-data.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function keepIptc() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('keep_iptc');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Applies all chained transformations, until a transformation component that includes this flag, on the last added
 * overlay or underlay instead of applying on the containing image.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function layerApply() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('layer_apply');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Automatically use lossy compression when delivering animated GIF files.
 *
 * This flag can also be used as a conditional flag for delivering PNG files: it tells Cloudinary to deliver the
 * image in PNG format (as requested) unless there is no transparency channel - in which case deliver in JPEG
 * format.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function lossy() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('lossy');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Convert the audio channel to mono
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function mono() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('mono');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Used internally by Position within an Overlay, this flag will tile the overlay across your image.
 *
 * <b>Learn more:</b> {@link https://cloudinary.com/documentation/transformation_reference#fl_no_overflow|Overflow in overlays}
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function noOverflow() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('no_overflow');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Don't stream a video that is currently being generated on the fly. Wait until the video is fully generated.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function noStream() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('no_stream');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Generate PNG images in the png24 format.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function png24() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('png24');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Generate PNG images in the png32 format.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function png32() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('png32');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Generate PNG images in the PNG8 format.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function png8() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('png8');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description When used with automatic fetch_format (f_auto): ensures that images with a transparency channel will be
 * delivered in PNG format.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function preserveTransparency() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('preserve_transparency');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Generates a JPG image using the progressive (interlaced) JPG format.
 *
 * This format allows the browser to quickly show a low-quality rendering of the image until the full-quality
 * image is loaded.
 *
 * @param {string} mode? The mode to determine a specific progressive outcome as follows:
 * * semi - A smart optimization of the decoding time, compression level and progressive rendering
 *          (less iterations). This is the default mode when using q_auto.
 * * steep - Delivers a preview very quickly, and in a single later phase improves the image to
 *           the required resolution.
 * * none  - Use this to deliver a non-progressive image. This is the default mode when setting
 *           a specific value for quality.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function progressive(mode) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('progressive', mode);
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Modifies percentage-based width & height parameters of overlays and underlays (e.g., 1.0) to be relative to the overlaid region
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function regionRelative() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('region_relative');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Reduces the image to one flat pixelated layer (as opposed to the default vector based graphic) in
 * order to enable
 * PDF resizing and overlay manipulations.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function rasterize() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('rasterize');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Modifies percentage-based width & height parameters of overlays and underlays (e.g., 1.0) to be relative to the containing image instead of the added layer.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function relative() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('relative');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Instructs Cloudinary to run a sanitizer on the image (relevant only for the SVG format).
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function sanitize() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('sanitize');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Splices the video stipulated as an overlay on to the end of the container video instead of adding it as an
 * overlay.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function splice() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('splice');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Instructs Cloudinary to clear all ICC color profile data included with the image.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function stripProfile() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('strip_profile');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description If the requested video transformation has already been generated, this flag works identically to
 * Flag::attachment.
 *
 *  However, if the video transformation is being requested for the first time, this flag causes the video download
 * to begin immediately, streaming it as a fragmented video file.
 *
 * In contrast, if the regular fl_attachment flag is used when a user requests a new video transformation,
 * the download will begin only after the complete transformed video has been generated.
 *
 * Most standard video players successfully play fragmented video files without issue.
 *
 * @param {string} filename The attachment's filename
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function streamingAttachment(filename) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('streaming_attachment', filename);
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Generates TIFF images using LZW compression and in the TIFF8 format.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function tiff8Lzw() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('tiff8_lzw');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Used internally by Position within an Overlay, this flag will tile the overlay across your image.
 *
 * <b>Learn more:</b> {@link https://cloudinary.com/documentation/layers#automatic_tiling|Tiling overlay}
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function tiled() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('tiled');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Truncate (trim) a video file based on the start time defined in the metadata (relevant only where the metadata
 * includes a directive to play only a section of the video).
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function truncateTS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('truncate_ts');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description Create a waveform image (in the format specified by the file extension) from the audio or video file.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function waveform() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('waveform');
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.Flag
 * @description A qualifier that ensures that an alpha channel is not applied to a TIFF image if it is a mask channel.
 * @return {Qualifiers.Flag.FlagQualifier}
 */ function ignoreMaskChannels() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"]('ignore_mask_channels');
}
const Flag = {
    animated,
    anyFormat,
    animatedPng,
    animatedWebP,
    clipEvenOdd,
    lossy,
    preserveTransparency,
    png8,
    png24,
    png32,
    progressive,
    rasterize,
    sanitize,
    stripProfile,
    tiff8Lzw,
    attachment,
    forceIcc,
    forceStrip,
    getInfo,
    immutableCache,
    keepAttribution,
    keepIptc,
    custom,
    streamingAttachment,
    hlsv3,
    keepDar,
    noStream,
    mono,
    layerApply,
    relative,
    regionRelative,
    splice,
    truncateTS,
    waveform,
    ignoreInitialAspectRatio,
    clip,
    tiled,
    noOverflow,
    ignoreMaskChannels
};
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/format/FormatQualifier.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FormatQualifier": (()=>FormatQualifier)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/QualifierValue.js [app-ssr] (ecmascript)");
;
/**
 * @memberOf Qualifiers.Format
 * @extends {SDK.QualifierValue}
 */ class FormatQualifier extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$QualifierValue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QualifierValue"] {
    constructor(val){
        super(val);
        this.val = val;
    }
    getValue() {
        return this.val;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/objectFlip.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Flip keys and values for given object
 * @param obj
 */ __turbopack_context__.s({
    "objectFlip": (()=>objectFlip)
});
function objectFlip(obj) {
    const result = {};
    Object.keys(obj).forEach((key)=>{
        result[obj[key]] = key;
    });
    return result;
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/internalConstants.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * This file is for internal constants only.
 * It is not intended for public use and is not part of the public API
 */ __turbopack_context__.s({
    "ACTION_TYPE_TO_BLEND_MODE_MAP": (()=>ACTION_TYPE_TO_BLEND_MODE_MAP),
    "ACTION_TYPE_TO_CROP_MODE_MAP": (()=>ACTION_TYPE_TO_CROP_MODE_MAP),
    "ACTION_TYPE_TO_DELIVERY_MODE_MAP": (()=>ACTION_TYPE_TO_DELIVERY_MODE_MAP),
    "ACTION_TYPE_TO_EFFECT_MODE_MAP": (()=>ACTION_TYPE_TO_EFFECT_MODE_MAP),
    "ACTION_TYPE_TO_QUALITY_MODE_MAP": (()=>ACTION_TYPE_TO_QUALITY_MODE_MAP),
    "ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP": (()=>ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP),
    "CHROMA_MODEL_ENUM_TO_CHROMA_VALUE": (()=>CHROMA_MODEL_ENUM_TO_CHROMA_VALUE),
    "CHROMA_VALUE_TO_CHROMA_MODEL_ENUM": (()=>CHROMA_VALUE_TO_CHROMA_MODEL_ENUM),
    "COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP": (()=>COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP),
    "COLOR_SPACE_MODE_TO_COLOR_SPACE_MODEL_MODE_MAP": (()=>COLOR_SPACE_MODE_TO_COLOR_SPACE_MODEL_MODE_MAP),
    "CONDITIONAL_OPERATORS": (()=>CONDITIONAL_OPERATORS),
    "CROP_MODE_TO_ACTION_TYPE_MAP": (()=>CROP_MODE_TO_ACTION_TYPE_MAP),
    "DELIVERY_MODE_TO_ACTION_TYPE_MAP": (()=>DELIVERY_MODE_TO_ACTION_TYPE_MAP),
    "EFFECT_MODE_TO_ACTION_TYPE_MAP": (()=>EFFECT_MODE_TO_ACTION_TYPE_MAP),
    "QUALITY_MODE_TO_ACTION_TYPE_MAP": (()=>QUALITY_MODE_TO_ACTION_TYPE_MAP),
    "RESERVED_NAMES": (()=>RESERVED_NAMES),
    "STREAMING_PROFILE_TO_ACTION_TYPE_MAP": (()=>STREAMING_PROFILE_TO_ACTION_TYPE_MAP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/utils/objectFlip.js [app-ssr] (ecmascript)");
;
const CONDITIONAL_OPERATORS = {
    "=": "eq",
    "!=": "ne",
    "<": "lt",
    ">": "gt",
    "<=": "lte",
    ">=": "gte",
    "&&": "and",
    "||": "or",
    "*": "mul",
    "/": "div",
    "+": "add",
    "-": "sub",
    "^": "pow"
};
const RESERVED_NAMES = {
    "aspect_ratio": "ar",
    "aspectRatio": "ar",
    "current_page": "cp",
    "currentPage": "cp",
    "duration": "du",
    "face_count": "fc",
    "faceCount": "fc",
    "height": "h",
    "initial_aspect_ratio": "iar",
    "initial_height": "ih",
    "initial_width": "iw",
    "initialAspectRatio": "iar",
    "initialHeight": "ih",
    "initialWidth": "iw",
    "initial_duration": "idu",
    "initialDuration": "idu",
    "page_count": "pc",
    "page_x": "px",
    "page_y": "py",
    "pageCount": "pc",
    "pageX": "px",
    "pageY": "py",
    "tags": "tags",
    "width": "w",
    "trimmed_aspect_ratio": "tar",
    "current_public_id": "cpi",
    "initial_density": "idn",
    "page_names": "pgnames"
};
const ACTION_TYPE_TO_CROP_MODE_MAP = {
    limitFit: 'limit',
    limitFill: 'lfill',
    minimumFit: 'mfit',
    thumbnail: 'thumb',
    limitPad: 'lpad',
    minimumPad: 'mpad',
    autoPad: 'auto_pad'
};
const ACTION_TYPE_TO_DELIVERY_MODE_MAP = {
    colorSpace: 'cs',
    dpr: 'dpr',
    density: 'dn',
    defaultImage: 'd',
    format: 'f',
    quality: 'q'
};
const ACTION_TYPE_TO_EFFECT_MODE_MAP = {
    redEye: 'redeye',
    advancedRedEye: 'adv_redeye',
    oilPaint: 'oil_paint',
    unsharpMask: 'unsharp_mask',
    makeTransparent: 'make_transparent',
    generativeRestore: 'gen_restore',
    upscale: 'upscale'
};
const ACTION_TYPE_TO_QUALITY_MODE_MAP = {
    autoBest: 'auto:best',
    autoEco: 'auto:eco',
    autoGood: 'auto:good',
    autoLow: 'auto:low',
    jpegminiHigh: 'jpegmini:1',
    jpegminiMedium: 'jpegmini:2',
    jpegminiBest: 'jpegmini:0'
};
const ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP = {
    fullHd: 'full_hd',
    fullHdWifi: 'full_hd_wifi',
    fullHdLean: 'full_hd_lean',
    hdLean: 'hd_lean'
};
const CHROMA_VALUE_TO_CHROMA_MODEL_ENUM = {
    444: "CHROMA_444",
    420: "CHROMA_420"
};
const COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP = {
    'noCmyk': 'no_cmyk',
    'keepCmyk': 'keep_cmyk',
    'tinySrgb': 'tinysrgb',
    'srgbTrueColor': 'srgb:truecolor'
};
const ACTION_TYPE_TO_BLEND_MODE_MAP = {
    'antiRemoval': 'anti_removal'
};
const CHROMA_MODEL_ENUM_TO_CHROMA_VALUE = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectFlip"])(CHROMA_VALUE_TO_CHROMA_MODEL_ENUM);
const COLOR_SPACE_MODE_TO_COLOR_SPACE_MODEL_MODE_MAP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectFlip"])(COLOR_SPACE_MODEL_MODE_TO_COLOR_SPACE_MODE_MAP);
const CROP_MODE_TO_ACTION_TYPE_MAP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectFlip"])(ACTION_TYPE_TO_CROP_MODE_MAP);
const DELIVERY_MODE_TO_ACTION_TYPE_MAP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectFlip"])(ACTION_TYPE_TO_DELIVERY_MODE_MAP);
const EFFECT_MODE_TO_ACTION_TYPE_MAP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectFlip"])(ACTION_TYPE_TO_EFFECT_MODE_MAP);
const QUALITY_MODE_TO_ACTION_TYPE_MAP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectFlip"])(ACTION_TYPE_TO_QUALITY_MODE_MAP);
const STREAMING_PROFILE_TO_ACTION_TYPE_MAP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$utils$2f$objectFlip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectFlip"])(ACTION_TYPE_TO_STREAMING_PROFILE_MODE_MAP);
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryAction.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DeliveryAction": (()=>DeliveryAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$Action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$format$2f$FormatQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/format/FormatQualifier.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/qualifier/Qualifier.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$internalConstants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/internalConstants.js [app-ssr] (ecmascript)");
;
;
;
;
/**
 * @description Qualifies the delivery of an asset.
 * @memberOf Actions.Delivery
 * @extends SDK.Action
 */ class DeliveryAction extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$Action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"] {
    /**
     * @param {string} deliveryKey A generic Delivery Action Key (such as q, f, dn, etc.)
     * @param {string} deliveryType A Format Qualifiers for the action, such as Quality.auto()
     * @param {string} modelProperty internal model property of the action, for example quality uses `level` while dpr uses `density`
     * @see Visit {@link Actions.Delivery|Delivery} for an example
     */ constructor(deliveryKey, deliveryType, modelProperty){
        super();
        this._actionModel = {};
        let deliveryTypeValue;
        if (deliveryType instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$format$2f$FormatQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormatQualifier"]) {
            deliveryTypeValue = deliveryType.getValue();
        } else {
            deliveryTypeValue = deliveryType;
        }
        this._actionModel.actionType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$internalConstants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DELIVERY_MODE_TO_ACTION_TYPE_MAP"][deliveryKey];
        this._actionModel[modelProperty] = deliveryTypeValue;
        this.addQualifier(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$qualifier$2f$Qualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Qualifier"](deliveryKey, deliveryType));
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/progressive.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @description Contains functions to select the mode when using a progressive format.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/transformation_reference#fl_progressive|Progressive modes}
 * @memberOf Qualifiers
 * @namespace Progressive
 * @example
 * import {Cloudinary} from "@cloudinary/url-gen";
 * import {format} from "@cloudinary/url-gen/actions/delivery";
 * import {jpg} from "@cloudinary/url-gen/qualifiers/format";
 * import {steep} from "@cloudinary/url-gen/qualifiers/progressive";
 *
 * const yourCldInstance = new Cloudinary({cloud: {cloudName: 'demo'}});
 * const image = yourCldInstance.image('woman');
 * image.delivery(format(jpg()).progressive(steep()))
 */ __turbopack_context__.s({
    "Progressive": (()=>Progressive),
    "ProgressiveQualifier": (()=>ProgressiveQualifier),
    "none": (()=>none),
    "progressive": (()=>progressive),
    "semi": (()=>semi),
    "steep": (()=>steep)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js [app-ssr] (ecmascript)");
;
class ProgressiveQualifier extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"] {
    constructor(mode){
        super('progressive', mode);
    }
}
/**
 * @memberOf Qualifiers.Progressive
 */ function none() {
    return new ProgressiveQualifier('none');
}
/**
 * @memberOf Qualifiers.Progressive
 */ function semi() {
    return new ProgressiveQualifier('semi');
}
/**
 * @memberOf Qualifiers.Progressive
 */ function steep() {
    return new ProgressiveQualifier('steep');
}
/**
 * @memberOf Qualifiers.Progressive
 */ function progressive() {
    return new ProgressiveQualifier();
}
const Progressive = {
    semi,
    none,
    steep,
    progressive,
    ProgressiveQualifier
};
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DeliveryFormatAction": (()=>DeliveryFormatAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryAction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$progressive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/progressive.js [app-ssr] (ecmascript)");
;
;
;
/**
 * @memberOf Actions.Delivery
 * @extends {Actions.Delivery.DeliveryAction}
 * @see Visit {@link Actions.Delivery|Delivery} for an example
 */ class DeliveryFormatAction extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DeliveryAction"] {
    constructor(deliveryKey, deliveryType){
        super(deliveryKey, deliveryType, 'formatType');
    }
    /**
     * @description Uses lossy compression when delivering animated GIF files.
     * @return {this}
     */ lossy() {
        this._actionModel.lossy = true;
        this.addFlag((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["lossy"])());
        return this;
    }
    /**
     * @description Uses progressive compression when delivering JPG file format.
     * @return {this}
     */ progressive(mode) {
        if (mode instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$progressive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressiveQualifier"]) {
            this._actionModel.progressive = {
                mode: mode.getFlagValue()
            };
            this.addFlag(mode);
        } else {
            this._actionModel.progressive = {
                mode: mode
            };
            this.addFlag((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["progressive"])(mode));
        }
        return this;
    }
    /**
     * @description Ensures that images with a transparency channel are delivered in PNG format.
     */ preserveTransparency() {
        this._actionModel.preserveTransparency = true;
        this.addFlag((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["preserveTransparency"])());
        return this;
    }
    static fromJson(actionModel) {
        const { formatType, lossy, progressive, preserveTransparency } = actionModel;
        let result;
        if (formatType) {
            result = new this('f', formatType);
        } else {
            result = new this('f');
        }
        if (progressive) {
            if (progressive.mode) {
                result.progressive(progressive.mode);
            } else {
                result.progressive();
            }
        }
        lossy && result.lossy();
        preserveTransparency && result.preserveTransparency();
        return result;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Transformation": (()=>Transformation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$Action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/Action.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$background$2f$actions$2f$BackgroundColor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/actions/background/actions/BackgroundColor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/qualifiers/flag/FlagQualifier.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$RawAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/RawAction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$IErrorObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/internal/models/IErrorObject.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryFormatAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
/**
 * @summary SDK
 * @description - Defines how to transform an asset
 * @memberOf SDK
 */ class Transformation {
    constructor(){
        this.actions = [];
    }
    /**
     * @param {SDK.Action | string} action
     * @return {this}
     */ addAction(action) {
        let actionToAdd;
        if (typeof action === 'string') {
            if (action.indexOf('/') >= 0) {
                throw 'addAction cannot accept a string with a forward slash in it - /, use .addTransformation() instead';
            } else {
                actionToAdd = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$RawAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RawAction"](action);
            }
        } else {
            actionToAdd = action;
        }
        this.actions.push(actionToAdd);
        return this;
    }
    /**
     * @description Allows the injection of a raw transformation as a string into the transformation, or a Transformation instance that was previously created
     * @param {string | SDK.Transformation} tx
     * @example
     * import {Transformation} from "@cloudinary/url-gen";
     *
     * const transformation = new Transformation();
     * transformation.addTransformation('w_100/w_200/w_300');
     * @return {this}
     */ addTransformation(tx) {
        if (tx instanceof Transformation) {
            // Concat the new actions into the existing actions
            this.actions = this.actions.concat(tx.actions);
        } else {
            this.actions.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$RawAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RawAction"](tx));
        }
        return this;
    }
    /**
     * @return {string}
     */ toString() {
        return this.actions.map((action)=>{
            return action.toString();
        }).filter((a)=>a).join('/');
    }
    /**
     * @description Delivers an animated GIF.
     * @param {AnimatedAction} animatedAction
     * @return {this}
     */ animated(animatedAction) {
        return this.addAction(animatedAction);
    }
    /**
     * @description Adds a border around the image.
     * @param {Border} borderAction
     * @return {this}
     */ border(borderAction) {
        return this.addAction(borderAction);
    }
    /**
     * @description Adjusts the shape of the delivered image. </br>
     * <b>Learn more:</b> {@link https://cloudinary.com/documentation/effects_and_artistic_enhancements#distort|Shape changes and distortion effects}
     * @param {IReshape} reshapeAction
     * @return {this}
     */ reshape(reshapeAction) {
        return this.addAction(reshapeAction);
    }
    /**
     * @description Resize the asset using provided resize action
     * @param {ResizeSimpleAction} resizeAction
     * @return {this}
     */ resize(resizeAction) {
        return this.addAction(resizeAction);
    }
    /**
     * @desc An alias to Action Delivery.quality
     * @param {string|number} quality
     * @return {this}
     */ quality(quality) {
        this.addAction(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryFormatAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DeliveryFormatAction"]('q', quality));
        return this;
    }
    /**
     * @desc An alias to Action Delivery.format
     * @param {string} format
     * @return {this}
     */ format(format) {
        this.addAction(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryFormatAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DeliveryFormatAction"]('f', format));
        return this;
    }
    /**
     * @description Rounds the specified corners of an image.
     * @param roundCornersAction
     * @return {this}
     */ roundCorners(roundCornersAction) {
        return this.addAction(roundCornersAction);
    }
    /**
     * @description Adds an overlay over the base image.
     * @param {LayerAction} overlayAction
     * @return {this}
     */ overlay(overlayAction) {
        return this.addAction(overlayAction);
    }
    /**
     * @description Adds an underlay under the base image.
     * @param {LayerAction} underlayAction
     * @return {this}
     */ underlay(underlayAction) {
        underlayAction.setLayerType('u');
        return this.addAction(underlayAction);
    }
    /**
     * @description Defines an new user variable.
     * @param {VariableAction} variableAction
     * @return {this}
     */ addVariable(variableAction) {
        return this.addAction(variableAction);
    }
    /**
     * @description Specifies a condition to be met before applying a transformation.
     * @param {ConditionalAction} conditionAction
     * @return {this}
     */ conditional(conditionAction) {
        return this.addAction(conditionAction);
    }
    /**
     * @description Applies a filter or an effect on an asset.
     * @param {SimpleEffectAction} effectAction
     * @return {this}
     */ effect(effectAction) {
        return this.addAction(effectAction);
    }
    /**
     * @description Applies adjustment effect on an asset.
     * @param action
     * @return {this}
     */ adjust(action) {
        return this.addAction(action);
    }
    /**
     * @description Rotates the asset by the given angle.
     * @param {RotateAction} rotateAction
     * @return {this}
     */ rotate(rotateAction) {
        return this.addAction(rotateAction);
    }
    /**
     * @description Applies a pre-defined named transformation of the given name.
     * @param {NamedTransformation} namedTransformation
     * @return {this}
     */ namedTransformation(namedTransformation) {
        return this.addAction(namedTransformation);
    }
    /**
     * @description Applies delivery action.
     * @param deliveryAction
     * @return {this}
     */ delivery(deliveryAction) {
        return this.addAction(deliveryAction);
    }
    /**
     * @description Sets the color of the background.
     * @param {Qualifiers.Color} color
     * @return {this}
     */ backgroundColor(color) {
        return this.addAction(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$background$2f$actions$2f$BackgroundColor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BackgroundColor"](color));
    }
    /**
     * @description Adds a layer in a Photoshop document.
     * @param action
     * @return {this}
     */ psdTools(action) {
        return this.addAction(action);
    }
    /**
     * @description Extracts an image or a page using an index, a range, or a name from a layered media asset.
     * @param action
     * @return {this}
     */ extract(action) {
        return this.addAction(action);
    }
    /**
     * @description Adds a flag as a separate action.
     * @param {Qualifiers.Flag | string} flagQualifier
     * @return {this}
     */ addFlag(flagQualifier) {
        const action = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$Action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"]();
        let flagToAdd = flagQualifier;
        if (typeof flagQualifier === 'string') {
            flagToAdd = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$qualifiers$2f$flag$2f$FlagQualifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlagQualifier"](flagQualifier);
        }
        action.addQualifier(flagToAdd);
        return this.addAction(action);
    }
    /**
     * @description Inject a custom function into the image transformation pipeline.
     * @return {this}
     */ customFunction(customFunction) {
        return this.addAction(customFunction);
    }
    /**
     * Transcodes the video (or audio) to another format.
     * @param {Action} action
     * @return {this}
     */ transcode(action) {
        return this.addAction(action);
    }
    /**
     * Applies the specified video edit action.
     *
     * @param {videoEditType} action
     * @return {this}
     */ videoEdit(action) {
        return this.addAction(action);
    }
    toJson() {
        const actions = [];
        for (const action of this.actions){
            const json = action.toJson();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$internal$2f$models$2f$IErrorObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isErrorObject"])(json)) {
                // Fail early and return an IErrorObject
                return json;
            }
            actions.push(json);
        }
        return {
            actions
        };
    }
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/transformation/ImageTransformation.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ImageTransformation": (()=>ImageTransformation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$Transformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js [app-ssr] (ecmascript)");
;
/**
 * @summary SDK
 * @extends {SDK.Transformation}
 * @memberOf SDK
 */ class ImageTransformation extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$Transformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Transformation"] {
}
;
}}),
"[project]/node_modules/@cloudinary/transformation-builder-sdk/transformation/VideoTransformation.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "VideoTransformation": (()=>VideoTransformation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$Transformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/transformation/Transformation.js [app-ssr] (ecmascript)");
;
/**
 * @summary SDK
 * @extends {SDK.Transformation}
 * @memberOf SDK
 */ class VideoTransformation extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$Transformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Transformation"] {
}
;
}}),
"[project]/node_modules/@cloudinary/url-gen/internal/url/urlUtils/isUrl.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 *
 * @param publicID
 */ __turbopack_context__.s({
    "isUrl": (()=>isUrl)
});
function isUrl(publicID) {
    return publicID.match(/^https?:\//);
}
}}),
"[project]/node_modules/@cloudinary/url-gen/internal/url/urlUtils/isFileName.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 *
 * @param publicID
 */ __turbopack_context__.s({
    "isFileName": (()=>isFileName)
});
function isFileName(publicID) {
    return publicID.indexOf('/') < 0;
}
}}),
"[project]/node_modules/@cloudinary/url-gen/internal/url/urlUtils/publicIDContainsVersion.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 *
 * @param publicID
 */ __turbopack_context__.s({
    "publicIDContainsVersion": (()=>publicIDContainsVersion)
});
function publicIDContainsVersion(publicID) {
    return publicID.match(/^v[0-9]+/);
}
}}),
"[project]/node_modules/@cloudinary/url-gen/internal/url/cloudinaryURL.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getUrlPrefix": (()=>getUrlPrefix),
    "getUrlVersion": (()=>getUrlVersion),
    "handleAssetType": (()=>handleAssetType),
    "handleDeliveryType": (()=>handleDeliveryType)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$urlUtils$2f$isUrl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/internal/url/urlUtils/isUrl.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$urlUtils$2f$isFileName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/internal/url/urlUtils/isFileName.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$urlUtils$2f$publicIDContainsVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/internal/url/urlUtils/publicIDContainsVersion.js [app-ssr] (ecmascript)");
;
;
;
/**
 * Create the URL prefix for Cloudinary resources.
 * Available use cases
 * http://res.cloudinary.com/{cloudName}
 * https://res.cloudinary.com/{cloudName}
 * https://{cloudName}-res.cloudinary.com/
 * http://{domain}/${cloudName}
 * https://{domain}/${cloudName}
 * https://{domain}
 * @private
 *
 * @param {string} cloudName
 * @param {IURLConfig} urlConfig
 */ function getUrlPrefix(cloudName, urlConfig) {
    const secure = urlConfig.secure;
    const privateCDN = urlConfig.privateCdn;
    const cname = urlConfig.cname;
    const secureDistribution = urlConfig.secureDistribution;
    if (!secure && !cname) {
        return `http://res.cloudinary.com/${cloudName}`;
    }
    if (secure && !secureDistribution && privateCDN) {
        return `https://${cloudName}-res.cloudinary.com`;
    }
    if (secure && !secureDistribution) {
        return `https://res.cloudinary.com/${cloudName}`;
    }
    if (secure && secureDistribution && privateCDN) {
        return `https://${secureDistribution}`;
    }
    if (secure && secureDistribution) {
        return `https://${secureDistribution}/${cloudName}`;
    }
    if (!secure && cname) {
        return `http://${cname}/${cloudName}`;
    } else {
        return 'ERROR';
    }
}
/**
 * @private
 * @param assetType
 */ function handleAssetType(assetType) {
    //default to image
    if (!assetType) {
        return 'image';
    }
    return assetType;
}
/**
 * @private
 * @param deliveryType
 */ function handleDeliveryType(deliveryType) {
    //default to upload
    if (!deliveryType) {
        return 'upload';
    }
    return deliveryType;
}
/**
 *
 * @param {string} publicID
 * @param {number} version
 * @param {boolean} forceVersion
 */ function getUrlVersion(publicID, version, forceVersion) {
    const shouldForceVersion = forceVersion !== false;
    if (version) {
        return `v${version}`;
    }
    // In all these conditions we never force a version
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$urlUtils$2f$publicIDContainsVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["publicIDContainsVersion"])(publicID) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$urlUtils$2f$isUrl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isUrl"])(publicID) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$urlUtils$2f$isFileName$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFileName"])(publicID)) {
        return '';
    }
    return shouldForceVersion ? 'v1' : '';
}
;
}}),
"[project]/node_modules/@cloudinary/url-gen/config/BaseConfig.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 *
 * @private
 * @param {any} a
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function isObject(a) {
    if (typeof a !== 'object' || a instanceof Array) {
        return false;
    } else {
        return true;
    }
}
class Config {
    filterOutNonSupportedKeys(userProvidedConfig, validKeys) {
        const obj = Object.create({});
        if (isObject(userProvidedConfig)) {
            Object.keys(userProvidedConfig).forEach((key)=>{
                if (validKeys.indexOf(key) >= 0) {
                    obj[key] = userProvidedConfig[key];
                } else {
                    console.warn('Warning - unsupported key provided to configuration: ', key);
                }
            });
            return obj;
        } else {
            return Object.create({});
        }
    }
}
const __TURBOPACK__default__export__ = Config;
}}),
"[project]/node_modules/@cloudinary/url-gen/internal/internalConstants.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * This file is for internal constants only.
 * It is not intended for public use and is not part of the public API
 */ /**
 * @private
 */ __turbopack_context__.s({
    "ALLOWED_CLOUD_CONFIG": (()=>ALLOWED_CLOUD_CONFIG),
    "ALLOWED_URL_CONFIG": (()=>ALLOWED_URL_CONFIG)
});
const ALLOWED_URL_CONFIG = [
    'cname',
    'secureDistribution',
    'privateCdn',
    'signUrl',
    'longUrlSignature',
    'shorten',
    'useRootPath',
    'secure',
    'forceVersion',
    'analytics',
    'queryParams'
];
const ALLOWED_CLOUD_CONFIG = [
    'cloudName',
    'apiKey',
    'apiSecret',
    'authToken'
];
}}),
"[project]/node_modules/@cloudinary/url-gen/config/URLConfig.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$config$2f$BaseConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/config/BaseConfig.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$internalConstants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/internal/internalConstants.js [app-ssr] (ecmascript)");
;
;
class URLConfig extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$config$2f$BaseConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    /**
     * @param {IURLConfig} userURLConfig
     */ constructor(userURLConfig){
        super();
        const urlConfig = this.filterOutNonSupportedKeys(userURLConfig, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$internalConstants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ALLOWED_URL_CONFIG"]);
        Object.assign(this, {
            secure: true
        }, urlConfig);
    }
    extend(userURLConfig) {
        const urlConfig = this.filterOutNonSupportedKeys(userURLConfig, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$internalConstants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ALLOWED_URL_CONFIG"]);
        return new URLConfig(Object.assign({}, this, urlConfig));
    }
    /**
     * @param {string} value Sets the cname
     */ setCname(value) {
        this.cname = value;
        return this;
    }
    /**
     * @param {string} value Sets the secureDistribution
     */ setSecureDistribution(value) {
        this.secureDistribution = value;
        return this;
    }
    /**
     * @param {boolean} value Sets whether to use a private CDN (Removes cloudName from URL)
     */ setPrivateCdn(value) {
        this.privateCdn = value;
        return this;
    }
    /**
     * @param value Sets whether or not to sign the URL
     */ setSignUrl(value) {
        this.signUrl = value;
        return this;
    }
    /**
     * @param value Sets whether or not to use a long signature
     */ setLongUrlSignature(value) {
        this.longUrlSignature = value;
        return this;
    }
    /**
     * @param value Sets whether or not to shorten the URL
     */ setShorten(value) {
        this.shorten = value;
        return this;
    }
    /**
     * @param value Sets whether or not to use a root path
     */ setUseRootPath(value) {
        this.useRootPath = value;
        return this;
    }
    /**
     * @param value Sets whether or not to deliver the asset through https
     */ setSecure(value) {
        this.secure = value;
        return this;
    }
    /**
     * @param value Sets whether to force a version in the URL
     */ setForceVersion(value) {
        this.forceVersion = value;
        return this;
    }
    /**
     * @param params Sets additional params
     */ setQueryParams(params) {
        this.queryParams = params;
        return this;
    }
}
const __TURBOPACK__default__export__ = URLConfig;
}}),
"[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @private
 * @description Adds left padding to a string with the desired substring the provided number of times
 * @example stringPad(foo, 3, 'a'') // -> aaafoo
 * @param {string} value
 * @param {number} _targetLength
 * @param {string} _padString
 */ __turbopack_context__.s({
    "stringPad": (()=>stringPad)
});
function stringPad(value, _targetLength, _padString) {
    let targetLength = _targetLength >> 0; //truncate if number or convert non-number to 0;
    let padString = String(typeof _padString !== 'undefined' ? _padString : ' ');
    if (value.length > targetLength) {
        return String(value);
    } else {
        targetLength = targetLength - value.length;
        if (targetLength > padString.length) {
            padString += repeatStringNumTimes(padString, targetLength / padString.length);
        }
        return padString.slice(0, targetLength) + String(value);
    }
}
/**
 * @description Repeat a string multiple times, cross-browser-safe alternative to string.repeat()
 * @param string
 * @param _times
 */ function repeatStringNumTimes(string, _times) {
    let times = _times;
    let repeatedString = "";
    while(times > 0){
        repeatedString += string;
        times--;
    }
    return repeatedString;
}
}}),
"[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/base64Map.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "base64Map": (()=>base64Map)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$stringPad$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js [app-ssr] (ecmascript)");
;
/**
 * This file maps sequences of 6 bit binary digits to a character in base64.
 * 000000 -> A
 * 110011 -> Z
 * 111111 -> /
 */ const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
const base64Map = {};
let num = 0;
chars.split('').forEach((char)=>{
    let key = num.toString(2);
    key = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$stringPad$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stringPad"])(key, 6, '0');
    base64Map[key] = char;
    num++;
});
;
}}),
"[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/reverseVersion.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "reverseVersion": (()=>reverseVersion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$stringPad$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js [app-ssr] (ecmascript)");
;
function reverseVersion(semVer) {
    if (semVer.split('.').length < 2) {
        throw new Error('invalid semVer, must have at least two segments');
    }
    // Split by '.', reverse, create new array with padded values and concat it together
    return semVer.split('.').reverse().map((segment)=>{
        // try to cast to number
        const asNumber = +segment;
        if (isNaN(asNumber) || asNumber < 0) {
            throw 'Invalid version number provided';
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$stringPad$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stringPad"])(segment, 2, '0');
    }).join('.');
}
}}),
"[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/encodeVersion.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "encodeVersion": (()=>encodeVersion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$base64Map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/base64Map.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$stringPad$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/stringPad.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$reverseVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/reverseVersion.js [app-ssr] (ecmascript)");
;
;
;
function encodeVersion(semVer) {
    let strResult = '';
    // support x.y or x.y.z by using 'parts' as a variable
    const parts = semVer.split('.').length;
    const paddedStringLength = parts * 6; // we pad to either 12 or 18 characters
    // reverse (but don't mirror) the version. 1.5.15 -> 15.5.1
    // Pad to two spaces, 15.5.1 -> 15.05.01
    const paddedReversedSemver = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$reverseVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reverseVersion"])(semVer);
    // turn 15.05.01 to a string '150501' then to a number 150501
    const num = parseInt(paddedReversedSemver.split('.').join(''));
    // Represent as binary, add left padding to 12 or 18 characters.
    // 150,501 -> 100100101111100101
    let paddedBinary = num.toString(2);
    paddedBinary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$stringPad$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stringPad"])(paddedBinary, paddedStringLength, '0');
    // Stop in case an invalid version number was provided
    // paddedBinary must be built from sections of 6 bits
    if (paddedBinary.length % 6 !== 0) {
        throw 'Version must be smaller than 43.21.26)';
    }
    // turn every 6 bits into a character using the base64Map
    paddedBinary.match(/.{1,6}/g).forEach((bitString)=>{
        // console.log(bitString);
        strResult += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$base64Map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["base64Map"][bitString];
    });
    return strResult;
}
}}),
"[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/getAnalyticsOptions.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @private
 * @description Gets the analyticsOptions from options- should include sdkSemver, techVersion, sdkCode, and feature
 * @param {ITrackedPropertiesThroughAnalytics} options
 * @returns {IAnalyticsOptions}
 */ __turbopack_context__.s({
    "getAnalyticsOptions": (()=>getAnalyticsOptions)
});
function getAnalyticsOptions(options) {
    const analyticsOptions = {
        sdkSemver: options.sdkSemver,
        techVersion: options.techVersion,
        sdkCode: options.sdkCode,
        product: options.product,
        feature: '0'
    };
    if (options.accessibility) {
        analyticsOptions.feature = 'D';
    }
    if (options.lazyload) {
        analyticsOptions.feature = 'C';
    }
    if (options.responsive) {
        analyticsOptions.feature = 'A';
    }
    if (options.placeholder) {
        analyticsOptions.feature = 'B';
    }
    return analyticsOptions;
}
}}),
"[project]/node_modules/@cloudinary/url-gen/internal/utils/packageVersion.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "packageVersion": (()=>packageVersion)
});
const packageVersion = '1.15.0';
;
}}),
"[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/getSDKAnalyticsSignature.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSDKAnalyticsSignature": (()=>getSDKAnalyticsSignature)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$encodeVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/encodeVersion.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$getAnalyticsOptions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/getAnalyticsOptions.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$utils$2f$packageVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/internal/utils/packageVersion.js [app-ssr] (ecmascript)");
;
;
;
/**
 * @private
 * @description Try to get the node version out of process, if browser just return 0.0.0
 */ function getNodeVersion() {
    const failedVersion = '0.0.0';
    if (typeof window !== 'undefined') {
        return failedVersion;
    } else {
        // node env
        try {
            return process.versions.node || failedVersion;
        } catch (e) {
            return failedVersion;
        }
    }
}
/**
 * @private
 * @description Ensure that all values ITrackedPropertiesThroughAnalytics are populated.
 * Accept a partial map of values and returns the complete interface of ITrackedPropertiesThroughAnalytics
 * @param {ITrackedPropertiesThroughAnalytics} trackedAnalytics
 * @param {ITrackedPropertiesThroughAnalytics} trackedAnalytics
 */ function ensureShapeOfTrackedProperties(trackedAnalytics) {
    // try to get the process version from node, but if we're on the client return 0.0.0
    const defaults = {
        techVersion: getNodeVersion(),
        sdkCode: 'T',
        sdkSemver: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$utils$2f$packageVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["packageVersion"].split('-')[0],
        product: 'A',
        responsive: false,
        placeholder: false,
        lazyload: false,
        accessibility: false
    };
    if (!trackedAnalytics) {
        return defaults;
    } else {
        return Object.assign(Object.assign({}, defaults), trackedAnalytics);
    }
}
function getSDKAnalyticsSignature(_trackedAnalytics) {
    const trackedAnalytics = ensureShapeOfTrackedProperties(_trackedAnalytics);
    const analyticsOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$getAnalyticsOptions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAnalyticsOptions"])(trackedAnalytics);
    try {
        const twoPartVersion = removePatchFromSemver(analyticsOptions.techVersion);
        const encodedSDKVersion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$encodeVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["encodeVersion"])(analyticsOptions.sdkSemver);
        const encodedTechVersion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$encodeVersion$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["encodeVersion"])(twoPartVersion);
        const featureCode = analyticsOptions.feature;
        const SDKCode = analyticsOptions.sdkCode;
        const product = analyticsOptions.product;
        const algoVersion = 'B'; // The algo version is determined here, it should not be an argument
        return `${algoVersion}${product}${SDKCode}${encodedSDKVersion}${encodedTechVersion}${featureCode}`;
    } catch (e) {
        // Either SDK or Node versions were unparsable
        return 'E';
    }
}
/**
 * @private
 * @description Removes patch version from the semver if it exists
 *              Turns x.y.z OR x.y into x.y
 * @param {'x.y.z' | 'x.y' | string} semVerStr
 */ function removePatchFromSemver(semVerStr) {
    const parts = semVerStr.split('.');
    return `${parts[0]}.${parts[1]}`;
}
}}),
"[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryFile.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CloudinaryFile": (()=>CloudinaryFile),
    "SEO_TYPES": (()=>SEO_TYPES)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$cloudinaryURL$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/internal/url/cloudinaryURL.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$config$2f$URLConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/config/URLConfig.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$getSDKAnalyticsSignature$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/sdkAnalytics/getSDKAnalyticsSignature.js [app-ssr] (ecmascript)");
;
;
;
const SEO_TYPES = {
    "image/upload": "images",
    "image/private": "private_images",
    "image/authenticated": "authenticated_images",
    "raw/upload": "files",
    "video/upload": "videos"
};
/**
 * @description Cloudinary file without a transformation
 * @summary SDK
 * @memberOf SDK
 */ class CloudinaryFile {
    constructor(publicID, cloudConfig = {}, urlConfig){
        this.setPublicID(publicID);
        this.setCloudConfig(cloudConfig);
        this.setURLConfig(urlConfig);
    }
    /**
     * @description Sets the URL Config for this asset
     * @param urlConfig
     * @return {this}
     */ setURLConfig(urlConfig) {
        this.urlConfig = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$config$2f$URLConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](urlConfig);
        return this;
    }
    /**
     * @description Sets the Cloud Config for this asset
     * @param urlConfig
     * @return {this}
     */ setCloudConfig(cloudConfig) {
        this.cloudName = cloudConfig.cloudName;
        this.apiKey = cloudConfig.apiKey;
        this.apiSecret = cloudConfig.apiSecret;
        this.authToken = cloudConfig.authToken;
        return this;
    }
    /**
     * @description Sets the public ID of the asset.
     * @param {string} publicID The public ID of the asset.
     * @return {this}
     */ setPublicID(publicID) {
        // PublicID must be a string!
        this.publicID = publicID ? publicID.toString() : '';
        return this;
    }
    /**
     * @description Sets the delivery type of the asset.
     * @param {DELIVERY_TYPE | string} newType The type of the asset.
     * @return {this}
     */ setDeliveryType(newType) {
        this.deliveryType = newType;
        return this;
    }
    /**
     * @description Sets the URL SEO suffix of the asset.
     * @param {string} newSuffix The SEO suffix.
     * @return {this}
     */ setSuffix(newSuffix) {
        this.suffix = newSuffix;
        return this;
    }
    /**
     * @description Sets the signature of the asset.
     * @param {string} signature The signature.
     * @return {this}
     */ setSignature(signature) {
        this.signature = signature;
        return this;
    }
    /**
     * @description Sets the version of the asset.
     * @param {string} newVersion The version of the asset.
     * @return {this}
     */ setVersion(newVersion) {
        if (newVersion) {
            this.version = newVersion;
        }
        return this;
    }
    /**
     * @description Sets the asset type.
     * @param {string} newType The type of the asset.
     * @return {this}
     */ setAssetType(newType) {
        if (newType) {
            this.assetType = newType;
        }
        return this;
    }
    sign() {
        return this;
    }
    /**
     * @description Serializes to URL string
     * @param overwriteOptions
     */ toURL(overwriteOptions = {}) {
        return this.createCloudinaryURL(null, overwriteOptions.trackedAnalytics);
    }
    /**
     * @description Validate various options before attempting to create a URL
     * The function will throw in case a violation
     * @throws Validation errors
     */ validateAssetForURLCreation() {
        if (typeof this.cloudName === 'undefined') {
            throw 'You must supply a cloudName when initializing the asset';
        }
        const suffixContainsDot = this.suffix && this.suffix.indexOf('.') >= 0;
        const suffixContainsSlash = this.suffix && this.suffix.indexOf('/') >= 0;
        if (suffixContainsDot || suffixContainsSlash) {
            throw '`suffix`` should not include . or /';
        }
    }
    /**
     * @description return an SEO friendly name for a combination of asset/delivery, some examples:
     * * image/upload -> images
     * * video/upload -> videos
     * If no match is found, return `{asset}/{delivery}`
     */ getResourceType() {
        const assetType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$cloudinaryURL$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["handleAssetType"])(this.assetType);
        const deliveryType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$cloudinaryURL$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["handleDeliveryType"])(this.deliveryType);
        const hasSuffix = !!this.suffix;
        const regularSEOType = `${assetType}/${deliveryType}`;
        const shortSEOType = SEO_TYPES[`${assetType}/${deliveryType}`];
        const useRootPath = this.urlConfig.useRootPath;
        const shorten = this.urlConfig.shorten;
        // Quick exit incase of useRootPath
        if (useRootPath) {
            if (regularSEOType === 'image/upload') {
                return ''; // For image/upload we're done, just return nothing
            } else {
                throw new Error(`useRootPath can only be used with assetType: 'image' and deliveryType: 'upload'. Provided: ${regularSEOType} instead`);
            }
        }
        if (shorten && regularSEOType === 'image/upload') {
            return 'iu';
        }
        if (hasSuffix) {
            if (shortSEOType) {
                return shortSEOType;
            } else {
                throw new Error(`URL Suffix only supported for ${Object.keys(SEO_TYPES).join(', ')}, Provided: ${regularSEOType} instead`);
            }
        }
        // If all else fails, return the regular image/upload combination (asset/delivery)
        return regularSEOType;
    }
    getSignature() {
        if (this.signature) {
            return `s--${this.signature}--`;
        } else {
            return '';
        }
    }
    /**
     *
     * @description Creates a fully qualified CloudinaryURL
     * @return {string} CloudinaryURL
     * @throws Validation Errors
     */ createCloudinaryURL(transformation, trackedAnalytics) {
        // In accordance with the existing implementation, if no publicID exists we should return nothing.
        if (!this.publicID) {
            return '';
        }
        // Throws if some options are mis-configured
        // See the function for more information on when it throws
        this.validateAssetForURLCreation();
        const prefix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$cloudinaryURL$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrlPrefix"])(this.cloudName, this.urlConfig);
        const transformationString = transformation ? transformation.toString() : '';
        const version = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$internal$2f$url$2f$cloudinaryURL$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrlVersion"])(this.publicID, this.version, this.urlConfig.forceVersion);
        const publicID = this.publicID;
        if (typeof transformation === 'string') {
            const url = [
                prefix,
                this.getResourceType(),
                this.getSignature(),
                transformationString,
                version,
                publicID.replace(/,/g, '%2C'),
                this.suffix
            ].filter((a)=>a).join('/');
            return url;
        } else {
            // Avoid applying encodeURI on entire string in case where we have transformations with comma (i.e. f_auto,q_auto)
            // Since encodeURI does not encode commas we replace commas *only* on the publicID
            const safeURL = [
                encodeURI(prefix),
                this.getResourceType(),
                this.getSignature(),
                encodeURI(transformationString),
                version,
                encodeURI(publicID).replace(/,/g, '%2C'),
                this.suffix && encodeURI(this.suffix)
            ].filter((a)=>a).join('/').replace(/\?/g, '%3F').replace(/=/g, '%3D');
            const shouldAddAnalytics = this.urlConfig.analytics !== false && !publicID.includes('?');
            let queryParamsString = '';
            if (typeof this.urlConfig.queryParams === 'object') {
                try {
                    const queryParams = new URLSearchParams(this.urlConfig.queryParams);
                    if (shouldAddAnalytics) {
                        queryParams.set("_a", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$getSDKAnalyticsSignature$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSDKAnalyticsSignature"])(trackedAnalytics));
                    }
                    queryParamsString = queryParams.toString();
                } catch (err) {
                    console.error('Error: URLSearchParams is not available so the queryParams object cannot be parsed, please try passing as an already parsed string');
                }
            } else {
                queryParamsString = this.urlConfig.queryParams || '';
                if (shouldAddAnalytics) {
                    queryParamsString += `${queryParamsString.length > 0 ? '&' : ''}_a=${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$sdkAnalytics$2f$getSDKAnalyticsSignature$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSDKAnalyticsSignature"])(trackedAnalytics)}`;
                }
            }
            if (queryParamsString) {
                return `${safeURL}?${queryParamsString}`;
            } else {
                return safeURL;
            }
        }
    }
}
;
}}),
"[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CloudinaryTransformable": (()=>CloudinaryTransformable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryFile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryFile.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryFormatAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/actions/delivery/DeliveryFormatAction.js [app-ssr] (ecmascript)");
;
;
/**
 * @desc Cloudinary Transformable interface, extended by any class that needs a Transformation Interface
 * @summary SDK
 * @memberOf SDK
 */ class CloudinaryTransformable extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryFile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CloudinaryFile"] {
    constructor(publicID, cloudConfig, urlConfig, transformation){
        /* istanbul ignore next */ super(publicID, cloudConfig, urlConfig);
        this.transformation = transformation;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Animated} animated
     * @return {this}
     */ animated(animated) {
        this.transformation.animated(animated);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Border} border
     * @return {this}
     */ border(border) {
        this.transformation.border(border);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Reshape} reshape
     * @return {this}
     */ reshape(reshape) {
        this.transformation.reshape(reshape);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Resize} resize
     * @return {this}
     */ resize(resize) {
        this.transformation.resize(resize);
        return this;
    }
    /**
     * @desc An alias to Action Delivery.quality
     * @param {string|number} quality
     * @return {this}
     */ quality(quality) {
        this.addAction(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryFormatAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DeliveryFormatAction"]('q', quality));
        return this;
    }
    /**
     * @desc An alias to Action Delivery.format
     * @param {string} format
     * @return {this}
     */ format(format) {
        this.addAction(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$actions$2f$delivery$2f$DeliveryFormatAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DeliveryFormatAction"]('f', format));
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.RoundCorners} roundCorners
     * @return {this}
     */ roundCorners(roundCorners) {
        this.transformation.roundCorners(roundCorners);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @return {this}
     */ overlay(overlayAction) {
        this.transformation.overlay(overlayAction);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Variable} variableAction
     * @return {this}
     */ addVariable(variableAction) {
        this.transformation.addVariable(variableAction);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Condition} conditionalAction
     * @return {this}
     */ conditional(conditionalAction) {
        this.transformation.conditional(conditionalAction);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Effect} effect
     * @return {this}
     */ effect(effect) {
        this.transformation.effect(effect);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Adjust} action
     * @return {this}
     */ adjust(action) {
        this.transformation.adjust(action);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Rotate} rotate
     * @return {this}
     */ rotate(rotate) {
        this.transformation.rotate(rotate);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.NamedTransformation} namedTransformation
     * @return {this}
     */ namedTransformation(namedTransformation) {
        this.transformation.namedTransformation(namedTransformation);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Delivery} deliveryAction
     * @return {this}
     */ delivery(deliveryAction) {
        this.transformation.delivery(deliveryAction);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Qualifiers.color} color
     * @return {this}
     */ backgroundColor(color) {
        this.transformation.backgroundColor(color);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.PSDTools} action
     * @return {this}
     */ psdTools(action) {
        this.transformation.psdTools(action);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Extract} action
     * @return {this}
     */ extract(action) {
        this.transformation.extract(action);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Qualifiers.Flag | string} flagQualifier
     * @return {this}
     */ addFlag(flagQualifier) {
        this.transformation.addFlag(flagQualifier);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.CustomFunction} customFunction
     * @return {this}
     */ customFunction(customFunction) {
        this.transformation.customFunction(customFunction);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {SDK.Action | string} action
     * @return {this}
     */ addAction(action) {
        this.transformation.addAction(action);
        return this;
    }
    /**
     * @description Extend your transformation with another transformation
     * @param { string | SDK.Transformation } tx
     */ addTransformation(tx) {
        this.transformation.addTransformation(tx);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @return {string}
     */ toString() {
        return this.transformation.toString();
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @return {this}
     */ underlay(underlayAction) {
        this.transformation.underlay(underlayAction);
        return this;
    }
    toURL(overwriteOptions = {}) {
        return this.createCloudinaryURL(this.transformation, overwriteOptions === null || overwriteOptions === void 0 ? void 0 : overwriteOptions.trackedAnalytics);
    }
}
;
}}),
"[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryImage.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CloudinaryImage": (()=>CloudinaryImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$ImageTransformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/transformation/ImageTransformation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryTransformable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js [app-ssr] (ecmascript)");
;
;
/**
 * @desc Cloudinary image asset, with image-related transformations
 * @summary SDK
 * @memberOf SDK
 */ class CloudinaryImage extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryTransformable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CloudinaryTransformable"] {
    constructor(publicID, cloudConfig, urlConfig){
        /* istanbul ignore next */ super(publicID, cloudConfig, urlConfig, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$ImageTransformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ImageTransformation"]());
    }
}
;
}}),
"[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryVideo.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CloudinaryVideo": (()=>CloudinaryVideo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryTransformable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryTransformable.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$VideoTransformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/transformation-builder-sdk/transformation/VideoTransformation.js [app-ssr] (ecmascript)");
;
;
/**
 * @desc Cloudinary video asset, with video-related transformations
 * @summary SDK
 * @memberOf SDK
 */ class CloudinaryVideo extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryTransformable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CloudinaryTransformable"] {
    constructor(publicID, cloudConfig, urlConfig){
        /* istanbul ignore next */ super(publicID, cloudConfig, urlConfig, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$transformation$2d$builder$2d$sdk$2f$transformation$2f$VideoTransformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VideoTransformation"]());
        this.assetType = 'video';
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.Transcode} action
     * @return {this}
     */ transcode(action) {
        this.transformation.transcode(action);
        return this;
    }
    /**
     * @desc A proxy to {@link SDK.Transformation| Transformation} - Calls the same method contained in this.transformation
     * @param {Actions.VideoEdit} action
     * @return {this}
     */ videoEdit(action) {
        this.transformation.videoEdit(action);
        return this;
    }
}
;
}}),
"[project]/node_modules/@cloudinary/url-gen/instance/Cloudinary.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Cloudinary": (()=>Cloudinary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryImage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryImage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryVideo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary/url-gen/assets/CloudinaryVideo.js [app-ssr] (ecmascript)");
;
;
class Cloudinary {
    constructor(cloudinaryConfig){
        if (cloudinaryConfig) {
            this.cloudinaryConfig = cloudinaryConfig;
        }
    }
    image(publicID) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryImage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CloudinaryImage"](publicID, this.cloudinaryConfig.cloud, this.cloudinaryConfig.url);
    }
    video(publicID) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2f$url$2d$gen$2f$assets$2f$CloudinaryVideo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CloudinaryVideo"](publicID, this.cloudinaryConfig.cloud, this.cloudinaryConfig.url);
    }
    setConfig(cloudinaryConfig) {
        this.cloudinaryConfig = cloudinaryConfig;
        return this;
    }
    getConfig() {
        return this.cloudinaryConfig;
    }
    extendConfig() {
    // Future implementation
    }
}
;
}}),
"[project]/node_modules/next-cloudinary/dist/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CldImage": (()=>le),
    "CldOgImage": (()=>se),
    "CldUploadButton": (()=>ge),
    "CldUploadWidget": (()=>j),
    "CldVideoPlayer": (()=>fe),
    "cloudinaryLoader": (()=>z),
    "getCldImageUrl": (()=>L),
    "getCldOgImageUrl": (()=>$),
    "getCldVideoUrl": (()=>Be)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$util$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary-util/util/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$chunk$2d$L3YIXMGG$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary-util/url-loader/dist/chunk-L3YIXMGG.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$package$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/node_modules/next/package.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$noop$2d$head$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/noop-head.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/script.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@cloudinary-util/url-loader/dist/index.js [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
var te = {
    name: "next-cloudinary",
    version: "6.16.0",
    license: "MIT",
    main: "./dist/index.js",
    module: "./dist/index.mjs",
    types: "./dist/index.d.ts",
    source: "src/index.ts",
    scripts: {
        build: "tsup",
        dev: "tsup --watch",
        prepublishOnly: "cp ../README.md . && cp ../LICENSE . && pnpm build",
        postpublish: "rm ./README.md && rm ./LICENSE",
        test: "vitest run",
        "test:app": 'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="test" pnpm build && cd tests/nextjs-app && npm install && npm run build',
        "test:watch": "vitest"
    },
    dependencies: {
        "@cloudinary-util/types": "1.5.10",
        "@cloudinary-util/url-loader": "5.10.4",
        "@cloudinary-util/util": "4.0.0"
    },
    devDependencies: {
        "@babel/core": "^7.25.2",
        "@babel/preset-env": "^7.25.3",
        "@tsconfig/recommended": "^1.0.7",
        "@types/node": "^22.0.2",
        "@types/react": "^18.3.3",
        "@types/react-dom": "^18.3.0",
        dotenv: "^16.4.5",
        mkdirp: "^3.0.1",
        tsup: "^8.2.3",
        typescript: "^5.5.4",
        vitest: "^2.0.5"
    },
    peerDependencies: {
        next: "^12 || ^13 || ^14 || >=15.0.0-rc || ^15",
        react: "^17 || ^18 || >=19.0.0-beta || ^19"
    }
};
var oe = "A", re = "V", ne = de(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$package$2e$json__$28$json$29$__["default"].version), ie = de(te.version);
function de(e) {
    let t = e;
    return t.includes("-") && (t = t.split("-")[0]), t;
}
function A(e) {
    let t = e?.cloud?.cloudName ?? ("TURBOPACK compile-time value", "deswustuu");
    if (!t) throw new Error("A Cloudinary Cloud name is required, please make sure NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME is set and configured in your environment.");
    let l = e?.cloud?.apiKey ?? process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY, a = e?.url?.secureDistribution ?? process.env.NEXT_PUBLIC_CLOUDINARY_SECURE_DISTRIBUTION, o = e?.url?.privateCdn ?? process.env.NEXT_PUBLIC_CLOUDINARY_PRIVATE_CDN;
    return Object.assign({
        cloud: {
            ...e?.cloud,
            apiKey: l,
            cloudName: t
        },
        url: {
            ...e?.url,
            secureDistribution: a,
            privateCdn: o
        }
    }, e);
}
function R(e) {
    return Object.assign({
        product: oe,
        sdkCode: re,
        sdkSemver: ie,
        techVersion: ne,
        feature: ""
    }, e);
}
function L(e, t, l) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$chunk$2d$L3YIXMGG$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["constructCloudinaryUrl"])({
        options: e,
        config: A(t),
        analytics: R(l)
    });
}
function z({ loaderOptions: e, imageProps: t, cldOptions: l, cldConfig: a = {} }) {
    let o = {
        ...t,
        ...l
    };
    if (o.width = typeof o.width == "string" ? parseInt(o.width) : o.width, o.height = typeof o.height == "string" ? parseInt(o.height) : o.height, typeof e?.width == "number" && typeof o.width == "number" && e.width !== o.width) {
        let r = e.width / o.width;
        o.width = e.width, typeof o.height == "number" && (o.height = Math.floor(o.height * r));
    } else typeof e?.width == "number" && typeof o?.width != "number" && (o.width = e?.width);
    return L(o, a);
}
var Ae = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(function(t, l) {
    let a = !1, o = [
        "assetType",
        "config",
        "deliveryType",
        "strictTransformations"
    ];
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$chunk$2d$L3YIXMGG$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformationPlugins"].forEach(({ props: n })=>{
        Object.keys(n).forEach((y)=>{
            if (o.includes(y)) throw new Error(`Option ${y} already exists!`);
            o.push(y);
        });
    });
    let r = {
        alt: t.alt,
        src: t.src
    };
    Object.keys(t).filter((n)=>typeof n == "string" && !o.includes(n)).forEach((n)=>r[n] = t[n]);
    let p = Object.keys(r).map((n)=>`${n}:${r[n]}`).join(";"), [C, f] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(p), d = {};
    o.forEach((n)=>{
        let m = t[n];
        m && (d[n] = m);
    });
    let s = ("TURBOPACK compile-time value", JSON.parse('{"deviceSizes":[640,750,828,1080,1200,1920,2048,3840],"imageSizes":[16,32,48,64,96,128,256,384],"path":"/_next/image","loader":"default","dangerouslyAllowSVG":false,"unoptimized":false,"domains":[],"remotePatterns":[]}')) || {};
    (t.unoptimized === !0 || s?.unoptimized === !0) && (r.src = L({
        ...d,
        width: r.width,
        height: r.height,
        src: r.src,
        format: "default",
        quality: "default"
    }, t.config));
    async function P(n) {
        let m = !0;
        if (a) return;
        if (a = !0, typeof t.onError == "function") {
            let I = t.onError(n);
            typeof I == "boolean" && I === !1 && (m = !1);
        } else typeof t.onError == "boolean" && t.onError === !1 && (m = !1);
        if (m === !1) return;
        let y = n.target, O = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$util$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pollForProcessingImage"])({
            src: y.src
        });
        typeof O.error == "string" && ("TURBOPACK compile-time value", "development") === "development" && console.error(`[CldImage] Failed to load image ${t.src}: ${O.error}`), O.success && f(`${p};${Date.now()}`);
    }
    let _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(P, [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$util$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pollForProcessingImage"],
        p
    ]), u = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
    return "default" in u && (u = u.default), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(u, {
        key: C,
        ...r,
        loader: (n)=>z({
                loaderOptions: n,
                imageProps: r,
                cldOptions: d,
                cldConfig: t.config
            }),
        onError: _,
        ref: l
    });
}), le = Ae;
;
;
function $(e) {
    return L({
        ...e,
        format: e.format || "jpg",
        width: e.width || 1200,
        height: e.height || 627,
        crop: e.crop || {
            type: "fill",
            gravity: "center",
            source: !0
        }
    });
}
var xe = "summary_large_image", Le = ({ excludeTags: e = [], twitterTitle: t, keys: l = {}, ...a })=>{
    let { alt: o } = a, { width: r = 1200, height: p = 627 } = a;
    r = typeof r == "string" ? parseInt(r) : r, p = typeof p == "string" ? parseInt(p) : p;
    let C = $({
        ...a,
        width: r,
        height: p
    }), f = $({
        ...a,
        width: r,
        height: p,
        format: a.format || "webp"
    }), d = {
        "og:image": "og-image",
        "og:image:secure_url": "og-image-secureurl",
        "og:image:width": "og-image-width",
        "og:image:height": "og-image-height",
        "og:image:alt": "og-image-alt",
        "twitter:title": "twitter-title",
        "twitter:card": "twitter-card",
        "twitter:image": "twitter-image",
        ...l
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$noop$2d$head$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["og:image"],
        property: "og:image",
        content: C
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["og:image:secure_url"],
        property: "og:image:secure_url",
        content: C
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["og:image:width"],
        property: "og:image:width",
        content: `${r}`
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["og:image:height"],
        property: "og:image:height",
        content: `${p}`
    }), o && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["og:image:alt"],
        property: "og:image:alt",
        content: o
    }), !e.includes("twitter:title") && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["twitter:title"],
        property: "twitter:title",
        content: t || " "
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["twitter:card"],
        property: "twitter:card",
        content: xe
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("meta", {
        key: d["twitter:image"],
        property: "twitter:image",
        content: f
    }));
}, se = Le;
;
;
;
;
function pe(e) {
    return window && "requestIdleCallback" in window ? requestIdleCallback(e) : setTimeout(()=>e(), 1);
}
var Me = ({ children: e, config: t, onError: l, onOpen: a, onUpload: o, options: r, signatureEndpoint: p, uploadPreset: C, ...f })=>{
    let d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(), [P, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(void 0), [u, n] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(void 0), [m, y] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!0), O = A(t), I = p && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateSignatureCallback"])({
        signatureEndpoint: String(p),
        fetch
    }), b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUploadWidgetOptions"])({
        uploadPreset: C || process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET,
        uploadSignature: I,
        ...r
    }, O), w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateUploadWidgetResultCallback"])({
        onError: (i)=>{
            _(i), typeof l == "function" && l(i, {
                widget: s.current,
                ...D
            });
        },
        onResult: (i)=>{
            if (typeof i?.event != "string") return;
            n(i);
            let h = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["UPLOAD_WIDGET_EVENTS"][i.event];
            if (typeof h == "string" && typeof f[h] == "function") {
                let V = f[h];
                V(i, {
                    widget: s.current,
                    ...D
                });
            }
            let S = `${h}Action`;
            if (S && typeof f[S] == "function") {
                let V = f[S];
                V(i);
            }
        }
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (typeof u > "u") return;
        u.event === "success" && typeof o == "function" && (("TURBOPACK compile-time value", "development") === "development" && console.warn("The onUpload callback is deprecated. Please use onSuccess instead."), o(u, s.current));
    }, [
        u
    ]);
    function U() {
        y(!1), d.current || (d.current = window.cloudinary), pe(()=>{
            s.current || (s.current = G());
        });
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>()=>{
            s.current?.destroy(), s.current = void 0;
        }, []);
    function c(i, h = []) {
        if (s.current || (s.current = G()), typeof s?.current[i] == "function") return s.current[i](...h);
    }
    function k(i) {
        c("close", [
            i
        ]);
    }
    function x(i) {
        return c("destroy", [
            i
        ]);
    }
    function N() {
        c("hide");
    }
    function W() {
        return c("isDestroyed");
    }
    function M() {
        return c("isMinimized");
    }
    function g() {
        return c("isShowing");
    }
    function T() {
        c("minimize");
    }
    function H(i, h) {
        c("open", [
            i,
            h
        ]), typeof a == "function" && a(s.current);
    }
    function X() {
        c("show");
    }
    function F() {
        c("update");
    }
    let D = {
        close: k,
        destroy: x,
        hide: N,
        isDestroyed: W,
        isMinimized: M,
        isShowing: g,
        minimize: T,
        open: H,
        show: X,
        update: F
    };
    function G() {
        return d.current?.createUploadWidget(b, w);
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, typeof e == "function" && e({
        cloudinary: d.current,
        widget: s.current,
        results: u,
        error: P,
        isLoading: m,
        ...D
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        id: `cloudinary-uploadwidget-${Math.floor(Math.random() * 100)}`,
        src: "https://upload-widget.cloudinary.com/global/all.js",
        onLoad: U,
        onError: (i)=>console.error(`Failed to load Cloudinary Upload Widget: ${i.message}`)
    }));
}, j = Me;
var De = ({ className: e, children: t, onClick: l, onError: a, onOpen: o, onUpload: r, onAbort: p, onBatchCancelled: C, onClose: f, onDisplayChanged: d, onPublicId: s, onQueuesEnd: P, onQueuesStart: _, onRetry: u, onShowCompleted: n, onSourceChanged: m, onSuccess: y, onTags: O, onUploadAdded: I, options: b, signatureEndpoint: w, uploadPreset: U, onAbortAction: c, onBatchCancelledAction: k, onCloseAction: x, onDisplayChangedAction: N, onPublicIdAction: W, onQueuesEndAction: M, onQueuesStartAction: g, onRetryAction: T, onShowCompletedAction: H, onSourceChangedAction: X, onSuccessAction: F, onTagsAction: D, onUploadAddedAction: G, ...i })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(j, {
        onError: a,
        onOpen: o,
        onUpload: r,
        onAbort: p,
        onBatchCancelled: C,
        onClose: f,
        onDisplayChanged: d,
        onPublicId: s,
        onQueuesEnd: P,
        onQueuesStart: _,
        onRetry: u,
        onShowCompleted: n,
        onSourceChanged: m,
        onSuccess: y,
        onTags: O,
        onUploadAdded: I,
        options: b,
        signatureEndpoint: w,
        uploadPreset: U,
        onAbortAction: c,
        onBatchCancelledAction: k,
        onCloseAction: x,
        onDisplayChangedAction: N,
        onPublicIdAction: W,
        onQueuesEndAction: M,
        onQueuesStartAction: g,
        onRetryAction: T,
        onShowCompletedAction: H,
        onSourceChangedAction: X,
        onSuccessAction: F,
        onTagsAction: D,
        onUploadAddedAction: G
    }, ({ open: h, isLoading: S })=>{
        function V(ee) {
            ee.preventDefault(), h(), typeof l == "function" && l(ee);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("button", {
            ...i,
            className: e || "",
            onClick: V,
            disabled: S
        }, t || "Upload");
    })), ge = De;
;
;
;
;
var Y = [], me = "1.11.1", $e = (e)=>{
    let { className: t, config: l, height: a, id: o, onDataLoad: r, onError: p, onMetadataLoad: C, onPause: f, onPlay: d, onEnded: s, width: P } = e, _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), u = A(l), n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getVideoPlayerOptions"])(e, u), { publicId: m } = n;
    if (typeof m > "u") throw new Error("Video Player requires a Public ID or Cloudinary URL - please specify a src prop");
    let y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(), O = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(), I = e.videoRef || O, b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(), w = e.playerRef || b, U = o || `player-${_.replace(/:/g, "")}`, c = "cld-video-player cld-fluid";
    t && (c = `${c} ${t}`), Y.filter((g)=>g === U).length > 1 ? console.warn(`Multiple instances of the same video detected on the
     page which may cause some features to not work.
    Try adding a unique id to each player.`) : Y.push(U);
    let x = {
        error: p,
        loadeddata: r,
        loadedmetadata: C,
        pause: f,
        play: d,
        ended: s
    };
    function N(g) {
        let T = x[g.type];
        typeof T == "function" && T(M());
    }
    function W() {
        "cloudinary" in window && (y.current = window.cloudinary, w.current = y.current.videoPlayer(I.current, n), Object.keys(x).forEach((g)=>{
            typeof x[g] == "function" && w.current?.on(g, N);
        }));
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>()=>{
            w.current?.videojs.cloudinary.dispose(), Y = Y.filter((g)=>g !== U);
        }, []);
    function M() {
        return {
            player: w.current,
            video: I.current
        };
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$noop$2d$head$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("link", {
        href: `https://unpkg.com/cloudinary-video-player@${me}/dist/cld-video-player.min.css`,
        rel: "stylesheet"
    })), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        style: {
            width: "100%",
            aspectRatio: `${P} / ${a}`
        }
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("video", {
        ref: I,
        id: U,
        className: c,
        width: P,
        height: a
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        id: `cloudinary-videoplayer-${U}`,
        src: `https://unpkg.com/cloudinary-video-player@${me}/dist/cld-video-player.min.js`,
        onLoad: W,
        onError: (g)=>console.error(`Failed to load Cloudinary Video Player: ${g.message}`)
    })));
}, fe = $e;
;
function Be(e, t, l) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$cloudinary$2d$util$2f$url$2d$loader$2f$dist$2f$chunk$2d$L3YIXMGG$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["constructCloudinaryUrl"])({
        options: {
            assetType: "video",
            format: "auto:video",
            ...e
        },
        config: A(t),
        analytics: R(l)
    });
}
;
 //# sourceMappingURL=index.mjs.map
}}),

};

//# sourceMappingURL=node_modules_183f910f._.js.map