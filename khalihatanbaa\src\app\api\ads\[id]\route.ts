import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const ad = await prisma.ad.findUnique({
      where: {
        id: id,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true,
            ratingAverage: true,
            ratingCount: true,
            createdAt: true,
          },
        },
      },
    });

    if (!ad) {
      return NextResponse.json(
        { success: false, error: "الإعلان غير موجود" },
        { status: 404 }
      );
    }

    // زيادة عدد المشاهدات
    await prisma.ad.update({
      where: { id: id },
      data: { views: { increment: 1 } },
    });

    return NextResponse.json({
      success: true,
      data: { ...ad, views: ad.views + 1 },
    });
  } catch (error) {
    console.error("Error fetching ad:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في جلب الإعلان" },
      { status: 500 }
    );
  }
}
