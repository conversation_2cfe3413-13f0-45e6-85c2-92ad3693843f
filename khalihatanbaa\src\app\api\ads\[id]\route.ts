import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// جلب إعلان محدد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const ad = await prisma.ad.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: { 
            id: true, 
            name: true, 
            avatar: true, 
            ratingAverage: true, 
            ratingCount: true,
            createdAt: true
          }
        },
        _count: {
          select: {
            favorites: true,
            messages: true
          }
        }
      }
    })

    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      )
    }

    // زيادة عدد المشاهدات
    await prisma.ad.update({
      where: { id: params.id },
      data: { views: { increment: 1 } }
    })

    // تحديث إحصائيات المستخدم
    await prisma.user.update({
      where: { id: ad.userId },
      data: { totalViews: { increment: 1 } }
    })

    return NextResponse.json({
      success: true,
      data: { ...ad, views: ad.views + 1 }
    })

  } catch (error) {
    console.error('Error fetching ad:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب الإعلان' },
      { status: 500 }
    )
  }
}

// تحديث إعلان
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const ad = await prisma.ad.findUnique({
      where: { id: params.id },
      select: { userId: true }
    })

    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من الملكية
    if (ad.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'غير مسموح لك بتعديل هذا الإعلان' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      price,
      category,
      subCategory,
      condition,
      city,
      region,
      addressDetail,
      imageUrls,
      specifications
    } = body

    const updatedAd = await prisma.ad.update({
      where: { id: params.id },
      data: {
        ...(title && { title }),
        ...(description && { description }),
        ...(price !== undefined && { price }),
        ...(category && { category }),
        ...(subCategory !== undefined && { subCategory }),
        ...(condition && { condition }),
        ...(city && { city }),
        ...(region !== undefined && { region }),
        ...(addressDetail !== undefined && { addressDetail }),
        ...(imageUrls && { imageUrls }),
        ...(specifications && { specifications })
      },
      include: {
        user: {
          select: { id: true, name: true, avatar: true }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedAd,
      message: 'تم تحديث الإعلان بنجاح'
    })

  } catch (error) {
    console.error('Error updating ad:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تحديث الإعلان' },
      { status: 500 }
    )
  }
}

// حذف إعلان
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const ad = await prisma.ad.findUnique({
      where: { id: params.id },
      select: { 
        userId: true, 
        adType: true, 
        isFreeAd: true, 
        isPromoted: true 
      }
    })

    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من الملكية
    if (ad.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'غير مسموح لك بحذف هذا الإعلان' },
        { status: 403 }
      )
    }

    // حذف الإعلان وإعادة العداد للمستخدم في معاملة واحدة
    await prisma.$transaction(async (tx) => {
      // حذف الإعلان
      await tx.ad.delete({
        where: { id: params.id }
      })

      // إعادة العداد للمستخدم
      const updateData: any = {}
      
      if (ad.isFreeAd) {
        updateData.freeAdsUsed = { decrement: 1 }
      } else if (ad.isPromoted) {
        updateData.promotedAdsUsed = { decrement: 1 }
      } else {
        updateData.paidAdsUsed = { decrement: 1 }
      }

      await tx.user.update({
        where: { id: session.user.id },
        data: updateData
      })
    })

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإعلان بنجاح'
    })

  } catch (error) {
    console.error('Error deleting ad:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في حذف الإعلان' },
      { status: 500 }
    )
  }
}
