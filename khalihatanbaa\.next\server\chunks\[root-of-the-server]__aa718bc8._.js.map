{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        emailOrPhone: { label: 'البريد الإلكتروني أو رقم الهاتف', type: 'text' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone }\n            ],\n            isActive: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.phone = user.phone\n        token.avatar = user.avatar\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.phone = token.phone as string\n        session.user.avatar = token.avatar as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/login',\n    signUp: '/auth/register'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBAAE,OAAO;oBAAmC,MAAM;gBAAO;gBACvE,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanba<PERSON>/src/app/api/conversations/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\nimport { prisma } from \"@/lib/prisma\";\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    // جلب جميع الرسائل للمستخدم\n    const allMessages = await prisma.message.findMany({\n      where: {\n        OR: [{ fromId: session.user.id }, { toId: session.user.id }],\n      },\n      orderBy: { createdAt: \"desc\" },\n    });\n\n    // تجميع الرسائل حسب المحادثة\n    const conversationMap = new Map();\n\n    for (const message of allMessages) {\n      const otherUserId =\n        message.fromId === session.user.id ? message.toId : message.fromId;\n      const conversationKey = `${otherUserId}-${message.adId || \"general\"}`;\n\n      if (!conversationMap.has(conversationKey)) {\n        conversationMap.set(conversationKey, {\n          ...message,\n          otherUserId,\n        });\n      }\n    }\n\n    const conversations = Array.from(conversationMap.values());\n\n    // جلب بيانات المستخدمين والإعلانات\n    const userIds = [...new Set(conversations.map((c) => c.otherUserId))];\n    const adIds = [\n      ...new Set(conversations.map((c) => c.adId).filter(Boolean)),\n    ];\n\n    const [users, ads] = await Promise.all([\n      prisma.user.findMany({\n        where: { id: { in: userIds } },\n        select: { id: true, name: true, avatar: true },\n      }),\n      adIds.length > 0\n        ? prisma.ad.findMany({\n            where: { id: { in: adIds } },\n            select: { id: true, title: true, price: true, imageUrls: true },\n          })\n        : [],\n    ]);\n\n    // تجميع البيانات\n    const formattedConversations = conversations.map((conv) => {\n      const otherUser = users.find((u) => u.id === conv.otherUserId);\n      const ad = conv.adId ? ads.find((a) => a.id === conv.adId) : null;\n\n      // حساب الرسائل غير المقروءة\n      const unreadCount = 0; // يمكن تحسينه لاحقاً\n\n      return {\n        id: `${conv.otherUserId}-${conv.adId || \"general\"}`,\n        content: conv.content,\n        createdAt: conv.createdAt,\n        adId: conv.adId,\n        otherUser: {\n          id: otherUser?.id || conv.otherUserId,\n          name: otherUser?.name || \"مستخدم غير معروف\",\n          avatar: otherUser?.avatar,\n        },\n        ad: ad\n          ? {\n              id: ad.id,\n              title: ad.title,\n              price: ad.price,\n              imageUrls: ad.imageUrls,\n            }\n          : null,\n        unreadCount,\n      };\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: formattedConversations,\n    });\n  } catch (error) {\n    console.error(\"Error fetching conversations:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في جلب المحادثات\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAChD,OAAO;gBACL,IAAI;oBAAC;wBAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBAAC;oBAAG;wBAAE,MAAM,QAAQ,IAAI,CAAC,EAAE;oBAAC;iBAAE;YAC9D;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB,IAAI;QAE5B,KAAK,MAAM,WAAW,YAAa;YACjC,MAAM,cACJ,QAAQ,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,GAAG,QAAQ,MAAM;YACpE,MAAM,kBAAkB,GAAG,YAAY,CAAC,EAAE,QAAQ,IAAI,IAAI,WAAW;YAErE,IAAI,CAAC,gBAAgB,GAAG,CAAC,kBAAkB;gBACzC,gBAAgB,GAAG,CAAC,iBAAiB;oBACnC,GAAG,OAAO;oBACV;gBACF;YACF;QACF;QAEA,MAAM,gBAAgB,MAAM,IAAI,CAAC,gBAAgB,MAAM;QAEvD,mCAAmC;QACnC,MAAM,UAAU;eAAI,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,IAAM,EAAE,WAAW;SAAG;QACrE,MAAM,QAAQ;eACT,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,EAAE,MAAM,CAAC;SACpD;QAED,MAAM,CAAC,OAAO,IAAI,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrC,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB,OAAO;oBAAE,IAAI;wBAAE,IAAI;oBAAQ;gBAAE;gBAC7B,QAAQ;oBAAE,IAAI;oBAAM,MAAM;oBAAM,QAAQ;gBAAK;YAC/C;YACA,MAAM,MAAM,GAAG,IACX,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,QAAQ,CAAC;gBACjB,OAAO;oBAAE,IAAI;wBAAE,IAAI;oBAAM;gBAAE;gBAC3B,QAAQ;oBAAE,IAAI;oBAAM,OAAO;oBAAM,OAAO;oBAAM,WAAW;gBAAK;YAChE,KACA,EAAE;SACP;QAED,iBAAiB;QACjB,MAAM,yBAAyB,cAAc,GAAG,CAAC,CAAC;YAChD,MAAM,YAAY,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,WAAW;YAC7D,MAAM,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,IAAI,IAAI;YAE7D,4BAA4B;YAC5B,MAAM,cAAc,GAAG,qBAAqB;YAE5C,OAAO;gBACL,IAAI,GAAG,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,WAAW;gBACnD,SAAS,KAAK,OAAO;gBACrB,WAAW,KAAK,SAAS;gBACzB,MAAM,KAAK,IAAI;gBACf,WAAW;oBACT,IAAI,WAAW,MAAM,KAAK,WAAW;oBACrC,MAAM,WAAW,QAAQ;oBACzB,QAAQ,WAAW;gBACrB;gBACA,IAAI,KACA;oBACE,IAAI,GAAG,EAAE;oBACT,OAAO,GAAG,KAAK;oBACf,OAAO,GAAG,KAAK;oBACf,WAAW,GAAG,SAAS;gBACzB,IACA;gBACJ;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}