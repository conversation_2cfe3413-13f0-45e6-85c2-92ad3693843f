'use client'

import { useState, useRef } from 'react'
import { PhotoIcon, XMarkIcon, PlusIcon } from '@heroicons/react/24/outline'

interface SimpleImageUploadProps {
  images: string[]
  onImagesChange: (images: string[]) => void
  maxImages?: number
  className?: string
}

export function SimpleImageUpload({ 
  images, 
  onImagesChange, 
  maxImages = 5,
  className = '' 
}: SimpleImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setUploading(true)

    try {
      const newImages: string[] = []

      for (let i = 0; i < files.length && images.length + newImages.length < maxImages; i++) {
        const file = files[i]
        
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
          alert('يرجى اختيار ملفات صور فقط')
          continue
        }

        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت')
          continue
        }

        // تحويل الصورة إلى base64
        const base64 = await fileToBase64(file)
        newImages.push(base64)
      }

      onImagesChange([...images, ...newImages])
    } catch (error) {
      console.error('Error uploading images:', error)
      alert('حدث خطأ في رفع الصور')
    } finally {
      setUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  }

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    onImagesChange(newImages)
  }

  const canAddMore = images.length < maxImages

  return (
    <div className={`space-y-4 ${className}`}>
      {/* عرض الصور المرفوعة */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div key={index} className="relative group">
              <img
                src={image}
                alt={`صورة ${index + 1}`}
                className="w-full h-32 object-cover rounded-lg border border-gray-200"
              />
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
              {index === 0 && (
                <div className="absolute bottom-2 left-2 bg-primary-500 text-white text-xs px-2 py-1 rounded">
                  الصورة الرئيسية
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* زر إضافة صور */}
      {canAddMore && (
        <div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="w-full h-32 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-gray-500 hover:border-primary-500 hover:text-primary-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {uploading ? (
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
                <span className="mt-2 text-sm">جاري الرفع...</span>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <PlusIcon className="h-8 w-8 mb-2" />
                <span className="text-sm font-medium">إضافة صور</span>
                <span className="text-xs text-gray-400 mt-1">
                  {images.length}/{maxImages} صور
                </span>
              </div>
            )}
          </button>
        </div>
      )}

      {/* نصائح */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• يمكنك رفع حتى {maxImages} صور</p>
        <p>• الحد الأقصى لحجم الصورة: 5 ميجابايت</p>
        <p>• الصيغ المدعومة: JPG, PNG, WebP</p>
        <p>• الصورة الأولى ستكون الصورة الرئيسية</p>
      </div>
    </div>
  )
}
