"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { PlaceholderImage } from "@/components/ui/PlaceholderImage";
import { RatingModal } from "@/components/ratings/RatingModal";
import { RatingsList } from "@/components/ratings/RatingsList";
import { Ad } from "@/types";
import {
  HeartIcon,
  ShareIcon,
  MapPinIcon,
  ClockIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import {
  HeartIcon as HeartSolidIcon,
  StarIcon as StarSolidIcon,
} from "@heroicons/react/24/solid";

// بيانات تجريبية للإعلان
const sampleAd: Ad = {
  id: "1",
  title: "تويوتا كامري 2018 فل كامل",
  description: `سيارة تويوتا كامري موديل 2018 بحالة ممتازة جداً
  
المواصفات:
- فل كامل (جلد، فتحة سقف، شاشة، كاميرا خلفية)
- صيانة دورية في الوكالة
- لون أبيض لؤلؤي
- عدد الكيلومترات: 85,000 كم
- جميع الأوراق سليمة
- فحص كامل متاح

السيارة بحالة ممتازة ولا تحتاج أي مصاريف. جاهزة للاستخدام فوراً.

للجادين فقط، يرجى التواصل عبر الرسائل أولاً.`,
  price: 45000000,
  category: "سيارات",
  subCategory: "تويوتا",
  condition: "مستعمل",
  city: "دمشق",
  region: "المزة",
  addressDetail: "قريب من جامع المزة",
  imageUrls: ["/placeholder-car.jpg"],
  views: 125,
  isActive: true,
  isFreeAd: false,
  isPromoted: true,
  createdAt: new Date("2024-01-15"),
  updatedAt: new Date("2024-01-15"),
  userId: "user1",
  specifications: {
    year: 2018,
    mileage: 85000,
    fuel: "بنزين",
    color: "أبيض لؤلؤي",
    transmission: "أوتوماتيك",
    engine: "2.5 لتر",
  },
  user: {
    id: "user1",
    name: "أحمد محمد",
    email: "<EMAIL>",
    phone: "+963991234567",
    role: "user",
    avatar: "",
    isActive: true,
    freeAdsCount: 2,
    freeAdsExpiresAt: new Date(),
    paidAdsCount: 0,
    paidAdsExpiresAt: new Date(),
    ratingAverage: 4.5,
    ratingCount: 12,
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-15"),
  },
};

// إعلانات مشابهة تجريبية
const similarAds: Ad[] = [
  {
    id: "2",
    title: "تويوتا كامري 2019",
    description: "سيارة بحالة جيدة",
    price: 48000000,
    category: "سيارات",
    subCategory: "تويوتا",
    condition: "مستعمل",
    city: "دمشق",
    region: "الشعلان",
    imageUrls: [],
    views: 89,
    isActive: true,
    isFreeAd: false,
    isPromoted: false,
    createdAt: new Date("2024-01-14"),
    updatedAt: new Date("2024-01-14"),
    userId: "user2",
  },
];

export default function AdDetailsPage() {
  const params = useParams();
  const { data: session } = useSession();
  const [ad, setAd] = useState<Ad | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [ratingsRefresh, setRatingsRefresh] = useState(0);

  useEffect(() => {
    // محاكاة جلب بيانات الإعلان
    setTimeout(() => {
      setAd(sampleAd);
      setLoading(false);
    }, 1000);

    // جلب حالة المفضلة إذا كان المستخدم مسجل دخول
    if (session?.user?.id) {
      checkFavoriteStatus();
    }
  }, [params.id, session]);

  const checkFavoriteStatus = async () => {
    try {
      const response = await fetch("/api/favorites");
      const data = await response.json();

      if (data.success) {
        const isFav = data.data.some((fav: any) => fav.ad.id === params.id);
        setIsFavorite(isFav);
      }
    } catch (error) {
      console.error("Error checking favorite status:", error);
    }
  };

  const toggleFavorite = async () => {
    if (!session?.user?.id) {
      alert("يجب تسجيل الدخول أولاً");
      return;
    }

    try {
      if (isFavorite) {
        // إزالة من المفضلة
        const response = await fetch(`/api/favorites?adId=${params.id}`, {
          method: "DELETE",
        });
        const data = await response.json();

        if (data.success) {
          setIsFavorite(false);
        } else {
          alert(data.error || "حدث خطأ في إزالة الإعلان من المفضلة");
        }
      } else {
        // إضافة إلى المفضلة
        const response = await fetch("/api/favorites", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ adId: params.id }),
        });
        const data = await response.json();

        if (data.success) {
          setIsFavorite(true);
        } else {
          alert(data.error || "حدث خطأ في إضافة الإعلان إلى المفضلة");
        }
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      alert("حدث خطأ في تحديث المفضلة");
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("ar-SY").format(price);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("ar-SY", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: ad?.title,
        text: ad?.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert("تم نسخ الرابط!");
    }
  };

  const nextImage = () => {
    if (ad && ad.imageUrls.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % ad.imageUrls.length);
    }
  };

  const prevImage = () => {
    if (ad && ad.imageUrls.length > 1) {
      setCurrentImageIndex(
        (prev) => (prev - 1 + ad.imageUrls.length) % ad.imageUrls.length
      );
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-96 bg-gray-200 rounded-lg mb-6"></div>
                <div className="space-y-4">
                  <div className="h-6 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="h-32 bg-gray-200 rounded"></div>
                <div className="h-24 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!ad) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            الإعلان غير موجود
          </h1>
          <p className="text-gray-600 mb-8">
            عذراً، لم نتمكن من العثور على هذا الإعلان
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
          >
            العودة للصفحة الرئيسية
          </Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* مسار التنقل */}
        <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-primary-500">
            الرئيسية
          </Link>
          <span>/</span>
          <Link
            href={`/search?category=${ad.category}`}
            className="hover:text-primary-500"
          >
            {ad.category}
          </Link>
          <span>/</span>
          <span className="text-gray-900">{ad.title}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* المحتوى الرئيسي */}
          <div className="lg:col-span-2">
            {/* معرض الصور */}
            <div className="relative bg-gray-100 rounded-lg overflow-hidden mb-6">
              <div className="aspect-w-16 aspect-h-9 h-96">
                <PlaceholderImage
                  width={800}
                  height={400}
                  text={`صورة ${ad.category}`}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* أزرار التنقل بين الصور */}
              {ad.imageUrls.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70"
                  >
                    <ChevronLeftIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70"
                  >
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </>
              )}

              {/* مؤشر الصور */}
              {ad.imageUrls.length > 1 && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 space-x-reverse">
                  {ad.imageUrls.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-full ${
                        index === currentImageIndex ? "bg-white" : "bg-white/50"
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* تفاصيل الإعلان */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-2xl font-bold text-dark-800 mb-2">
                    {ad.title}
                  </h1>
                  <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                    <div className="flex items-center">
                      <MapPinIcon className="h-4 w-4 ml-1" />
                      {ad.city} - {ad.region}
                    </div>
                    <div className="flex items-center">
                      <ClockIcon className="h-4 w-4 ml-1" />
                      {formatDate(ad.createdAt)}
                    </div>
                    <div className="flex items-center">
                      <EyeIcon className="h-4 w-4 ml-1" />
                      {ad.views} مشاهدة
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={toggleFavorite}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    {isFavorite ? (
                      <HeartSolidIcon className="h-5 w-5 text-red-500" />
                    ) : (
                      <HeartIcon className="h-5 w-5 text-gray-600" />
                    )}
                  </button>
                  <button
                    onClick={handleShare}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <ShareIcon className="h-5 w-5 text-gray-600" />
                  </button>
                </div>
              </div>

              <div className="text-3xl font-bold text-primary-500 mb-6">
                {formatPrice(ad.price)} <span className="text-lg">ل.س</span>
              </div>

              <div className="prose prose-sm max-w-none">
                <h3 className="text-lg font-semibold text-dark-800 mb-3">
                  الوصف
                </h3>
                <div className="text-gray-700 whitespace-pre-line">
                  {ad.description}
                </div>
              </div>

              {/* المواصفات */}
              {ad.specifications &&
                Object.keys(ad.specifications).length > 0 && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h3 className="text-lg font-semibold text-dark-800 mb-3">
                      المواصفات
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {Object.entries(ad.specifications).map(([key, value]) => (
                        <div key={key} className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-600 mb-1">
                            {key === "year" && "سنة الصنع"}
                            {key === "mileage" && "الكيلومترات"}
                            {key === "fuel" && "نوع الوقود"}
                            {key === "color" && "اللون"}
                            {key === "transmission" && "ناقل الحركة"}
                            {key === "engine" && "المحرك"}
                          </div>
                          <div className="font-medium text-dark-800">
                            {typeof value === "number"
                              ? value.toLocaleString("ar-SY")
                              : value}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </div>

          {/* الشريط الجانبي */}
          <div className="space-y-6">
            {/* معلومات البائع */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-dark-800 mb-4">
                معلومات البائع
              </h3>

              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-semibold">
                    {ad.user?.name.charAt(0)}
                  </span>
                </div>
                <div className="mr-3">
                  <div className="font-medium text-dark-800">
                    {ad.user?.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    عضو منذ {formatDate(ad.user?.createdAt || new Date())}
                  </div>
                </div>
              </div>

              {ad.user?.ratingAverage && ad.user.ratingCount > 0 && (
                <div className="flex items-center mb-4">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(ad.user?.ratingAverage || 0)
                            ? "text-yellow-400"
                            : "text-gray-300"
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <span className="mr-2 text-sm text-gray-600">
                    {ad.user.ratingAverage.toFixed(1)} ({ad.user.ratingCount}{" "}
                    تقييم)
                  </span>
                </div>
              )}

              {session?.user.id !== ad.userId && (
                <div className="space-y-3">
                  <button
                    onClick={() => setShowContactModal(true)}
                    className="w-full bg-primary-500 text-white py-3 px-4 rounded-lg hover:bg-primary-600 transition-colors flex items-center justify-center"
                  >
                    <ChatBubbleLeftRightIcon className="h-5 w-5 ml-2" />
                    تواصل مع البائع
                  </button>

                  <button
                    onClick={() => setShowRatingModal(true)}
                    className="w-full border border-primary-500 text-primary-500 py-2 px-4 rounded-lg hover:bg-primary-50 transition-colors flex items-center justify-center"
                  >
                    <StarIcon className="h-5 w-5 ml-2" />
                    تقييم البائع
                  </button>
                </div>
              )}
            </div>

            {/* نصائح الأمان */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2">
                نصائح للأمان
              </h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• تأكد من المنتج قبل الدفع</li>
                <li>• التقي في مكان عام وآمن</li>
                <li>• لا تشارك معلوماتك المصرفية</li>
                <li>• استخدم وسائل دفع آمنة</li>
              </ul>
            </div>
          </div>
        </div>

        {/* الإعلانات المشابهة */}
        {similarAds.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-dark-800 mb-6">
              إعلانات مشابهة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarAds.map((similarAd) => (
                <Link
                  key={similarAd.id}
                  href={`/ads/${similarAd.id}`}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <div className="h-48 bg-gray-200">
                    <PlaceholderImage
                      width={300}
                      height={192}
                      text={`صورة ${similarAd.category}`}
                      className="w-full h-full"
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-dark-800 mb-2 line-clamp-2">
                      {similarAd.title}
                    </h3>
                    <div className="text-lg font-bold text-primary-500">
                      {formatPrice(similarAd.price)}{" "}
                      <span className="text-sm">ل.س</span>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {similarAd.city} - {similarAd.region}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* تقييمات البائع */}
        <div className="mt-12">
          <RatingsList sellerId={ad.userId} refreshTrigger={ratingsRefresh} />
        </div>
      </main>

      <Footer />

      {/* نافذة التواصل */}
      {showContactModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-dark-800 mb-4">
              تواصل مع البائع
            </h3>
            <p className="text-gray-600 mb-4">
              سيتم فتح محادثة جديدة مع البائع. يمكنك إرسال رسالتك والتفاوض على
              السعر.
            </p>
            <div className="flex space-x-3 space-x-reverse">
              <button
                onClick={() => setShowContactModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <Link
                href={`/messages?ad=${ad.id}&user=${ad.userId}`}
                className="flex-1 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 text-center"
              >
                بدء المحادثة
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* نافذة التقييم */}
      {showRatingModal && ad && (
        <RatingModal
          isOpen={showRatingModal}
          onClose={() => setShowRatingModal(false)}
          sellerId={ad.userId}
          sellerName={ad.user?.name || "البائع"}
          onRatingAdded={() => {
            setRatingsRefresh((prev) => prev + 1);
            setShowRatingModal(false);
          }}
        />
      )}
    </div>
  );
}
