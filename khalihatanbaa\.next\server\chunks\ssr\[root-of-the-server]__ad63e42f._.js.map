{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON><PERSON>/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { signIn, getSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'\n\nexport default function LoginPage() {\n  const [formData, setFormData] = useState({\n    emailOrPhone: '',\n    password: ''\n  })\n  const [showPassword, setShowPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  \n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n\n    try {\n      const result = await signIn('credentials', {\n        emailOrPhone: formData.emailOrPhone,\n        password: formData.password,\n        redirect: false\n      })\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة')\n      } else {\n        // تحديث الجلسة والتوجه للصفحة الرئيسية\n        await getSession()\n        router.push('/')\n        router.refresh()\n      }\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }))\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"flex justify-center\">\n            <div className=\"w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center\">\n              <span className=\"text-white font-bold text-2xl\">خ</span>\n            </div>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-bold text-dark-800\">\n            تسجيل الدخول\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-dark-600\">\n            أو{' '}\n            <Link\n              href=\"/auth/register\"\n              className=\"font-medium text-primary-600 hover:text-primary-500\"\n            >\n              إنشاء حساب جديد\n            </Link>\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"emailOrPhone\" className=\"block text-sm font-medium text-dark-700\">\n                البريد الإلكتروني أو رقم الهاتف\n              </label>\n              <input\n                id=\"emailOrPhone\"\n                name=\"emailOrPhone\"\n                type=\"text\"\n                required\n                value={formData.emailOrPhone}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-dark-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\n                placeholder=\"أدخل البريد الإلكتروني أو رقم الهاتف\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-dark-700\">\n                كلمة المرور\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  className=\"appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-dark-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"أدخل كلمة المرور\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <input\n                id=\"remember-me\"\n                name=\"remember-me\"\n                type=\"checkbox\"\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"remember-me\" className=\"mr-2 block text-sm text-dark-900\">\n                تذكرني\n              </label>\n            </div>\n\n            <div className=\"text-sm\">\n              <Link\n                href=\"/auth/forgot-password\"\n                className=\"font-medium text-primary-600 hover:text-primary-500\"\n              >\n                نسيت كلمة المرور؟\n              </Link>\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-dark-600\">\n              ليس لديك حساب؟{' '}\n              <Link\n                href=\"/auth/register\"\n                className=\"font-medium text-primary-600 hover:text-primary-500\"\n              >\n                إنشاء حساب جديد\n              </Link>\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,cAAc,SAAS,YAAY;gBACnC,UAAU,SAAS,QAAQ;gBAC3B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX,OAAO;gBACL,uCAAuC;gBACvC,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;gBACf,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;sCAGpD,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;;gCAAyC;gCACjD;8CACH,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAA0C;;;;;;sDAGlF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,YAAY;4CAC5B,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,eAAe,SAAS;oDAC9B,QAAQ;oDACR,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB,CAAC;8DAE/B,6BACC,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;6EAExB,8OAAC,6MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAO5B,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,WAAU;;;;;;sDAEZ,8OAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAmC;;;;;;;;;;;;8CAK5E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAML,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,yBAAyB;;;;;;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCACpB;kDACf,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}