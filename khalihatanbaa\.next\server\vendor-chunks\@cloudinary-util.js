"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@cloudinary-util";
exports.ids = ["vendor-chunks/@cloudinary-util"];
exports.modules = {

/***/ "(ssr)/./node_modules/@cloudinary-util/url-loader/dist/chunk-L3YIXMGG.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@cloudinary-util/url-loader/dist/chunk-L3YIXMGG.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetOptionsSchema: () => (/* binding */ assetOptionsSchema),\n/* harmony export */   constructCloudinaryUrl: () => (/* binding */ constructCloudinaryUrl),\n/* harmony export */   constructUrlPropsSchema: () => (/* binding */ constructUrlPropsSchema),\n/* harmony export */   effects: () => (/* binding */ effects),\n/* harmony export */   imageOptionsSchema: () => (/* binding */ imageOptionsSchema),\n/* harmony export */   position: () => (/* binding */ position),\n/* harmony export */   primary: () => (/* binding */ primary),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   transformationPlugins: () => (/* binding */ transformationPlugins),\n/* harmony export */   videoOptionsSchema: () => (/* binding */ videoOptionsSchema)\n/* harmony export */ });\n/* harmony import */ var _cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @cloudinary-util/util */ \"(ssr)/./node_modules/@cloudinary-util/url-loader/node_modules/@cloudinary-util/util/dist/index.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _cloudinary_url_gen__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @cloudinary/url-gen */ \"(ssr)/./node_modules/@cloudinary/url-gen/instance/Cloudinary.js\");\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/constants/qualifiers.ts\n\n\n\n// src/constants/parameters.ts\nvar parameters_exports = {};\n__export(parameters_exports, {\n  angle: () => angle,\n  aspectRatio: () => aspectRatio,\n  aspectRatioModesEnum: () => aspectRatioModesEnum,\n  crop: () => crop,\n  cropModesEnum: () => cropModesEnum,\n  extractMode: () => extractMode,\n  extractModesEnum: () => extractModesEnum,\n  flags: () => flags,\n  flagsEnum: () => flagsEnum,\n  format: () => format,\n  gravity: () => gravity,\n  height: () => height,\n  multiple: () => multiple,\n  prompt: () => prompt,\n  width: () => width,\n  x: () => x,\n  y: () => y,\n  zoom: () => zoom\n});\n\nvar cropModesEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n  \"auto\",\n  \"crop\",\n  \"fill\",\n  \"fill_pad\",\n  \"fit\",\n  \"imagga_crop\",\n  \"imagga_scale\",\n  \"lfill\",\n  \"limit\",\n  \"lpad\",\n  \"mfit\",\n  \"mpad\",\n  \"pad\",\n  \"scale\",\n  \"thumb\"\n]);\nvar extractModesEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n  \"content\",\n  \"mask\"\n]);\nvar flagsEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n  \"animated\",\n  \"any_format\",\n  \"apng\",\n  \"attachment\",\n  \"awebp\",\n  \"clip\",\n  \"clip_evenodd\",\n  \"cutter\",\n  \"force_icc\",\n  \"force_strip\",\n  \"getinfo\",\n  \"group4\",\n  \"hlsv3\",\n  \"ignore_aspect_ratio\",\n  \"ignore_mask_channels\",\n  \"immutable_cache\",\n  \"keep_attribution\",\n  \"keep_dar\",\n  \"keep_iptc\",\n  \"layer_apply\",\n  \"lossy\",\n  \"mono\",\n  \"no_overflow\",\n  \"no_stream\",\n  \"png8_fl_png24_fl_png32\",\n  \"preserve_transparency\",\n  \"progressive\",\n  \"rasterize\",\n  \"region_relative\",\n  \"relative\",\n  \"replace_image\",\n  \"sanitize\",\n  \"splice\",\n  \"streaming_attachment\",\n  \"strip_profile\",\n  \"text_disallow_overflow\",\n  \"text_no_trim\",\n  \"tiff8_lzw\",\n  \"tiled\",\n  \"truncate_ts\",\n  \"waveform\"\n]);\nvar angle = {\n  qualifier: \"a\",\n  schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n    JSON.stringify({\n      text: \"Rotates or flips an asset by the specified number of degrees or automatically according to its orientation or available metadata.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#a_angle\"\n    })\n  )\n};\nvar aspectRatioModesEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n  \"vflip\",\n  \"hflip\",\n  \"ignore\",\n  \"auto_right\",\n  \"auto_left\"\n]);\nvar aspectRatioSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n  aspectRatioModesEnum,\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.intersection(zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}))\n  // Quirk to allow enum + string\n]);\nvar aspectRatio = {\n  qualifier: \"ar\",\n  schema: aspectRatioSchema.describe(\n    JSON.stringify({\n      text: \"Crops or resizes the asset to a new aspect ratio.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#ar_aspect_ratio\"\n    })\n  )\n};\nvar cropSchema = cropModesEnum;\nvar crop = {\n  qualifier: \"c\",\n  schema: cropSchema.describe(\n    JSON.stringify({\n      text: \"Mode to use when cropping an asset.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#c_crop_resize\"\n    })\n  )\n};\nvar extractModeSchema = extractModesEnum;\nvar extractMode = {\n  schema: extractModeSchema.default(\"content\").describe(\n    JSON.stringify({\n      text: \"Whether to keep the content of the extracted area, or to replace it with a mask.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_extract\"\n    })\n  )\n};\nvar flags = {\n  qualifier: \"fl\",\n  schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([flagsEnum, zod__WEBPACK_IMPORTED_MODULE_0__.z.array(flagsEnum)]).describe(\n    JSON.stringify({\n      text: \"Alters the regular behavior of another transformation or the overall delivery behavior.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#fl_flag\"\n    })\n  )\n};\nvar format = {\n  qualifier: \"f\",\n  // @TODO: enum\n  schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"Converts (if necessary) and delivers an asset in the specified format regardless of the file extension used in the delivery URL.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#f_format\"\n    })\n  )\n};\nvar gravitySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    \"auto\",\n    \"auto_content_aware\",\n    \"center\",\n    \"custom\",\n    \"east\",\n    \"face\",\n    \"face_center\",\n    \"multi_face\",\n    \"north\",\n    \"north_east\",\n    \"north_west\",\n    \"south\",\n    \"south_east\",\n    \"south_west\",\n    \"west\"\n  ]),\n  // Quirk to allow enum + string\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.intersection(zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}))\n]);\nvar gravity = {\n  qualifier: \"g\",\n  schema: gravitySchema.describe(\n    JSON.stringify({\n      text: \"Determines which part of an asset to focus on. Note: Default of auto is applied for supported crop modes only.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#g_gravity\"\n    })\n  )\n};\nvar heightSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.number(), zod__WEBPACK_IMPORTED_MODULE_0__.z.string()]);\nvar height = {\n  qualifier: \"h\",\n  schema: heightSchema.describe(\n    JSON.stringify({\n      text: \"A qualifier that determines the height of a transformed asset or an overlay.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#h_height\"\n    })\n  )\n};\nvar multipleSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean();\nvar multiple = {\n  schema: multipleSchema.describe(\n    JSON.stringify({\n      text: \"Should generative AI features detect multiple instances.\"\n    })\n  )\n};\nvar prompt = {\n  schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"Natural language descriptions used for generative AI capabilities.\"\n    })\n  )\n};\nvar widthSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.number(), zod__WEBPACK_IMPORTED_MODULE_0__.z.string()]);\nvar width = {\n  qualifier: \"w\",\n  schema: widthSchema.describe(\n    JSON.stringify({\n      text: \"A qualifier that sets the desired width of an asset using a specified value, or automatically based on the available width.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#w_width\"\n    })\n  )\n};\nvar x = {\n  qualifier: \"x\",\n  schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n    JSON.stringify({\n      text: \"Adjusts the starting location or offset of the x axis.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#x_y_coordinates\"\n    })\n  )\n};\nvar y = {\n  qualifier: \"y\",\n  schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n    JSON.stringify({\n      text: \"Adjusts the starting location or offset of the y axis.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#x_y_coordinates\"\n    })\n  )\n};\nvar zoomSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string();\nvar zoom = {\n  schema: zoomSchema.describe(\n    JSON.stringify({\n      text: \"Controls how close to crop to the detected coordinates when using face-detection, custom-coordinate, or object-specific gravity.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#z_zoom\"\n    })\n  )\n};\n\n// src/constants/qualifiers.ts\nvar convertersColors = [\n  {\n    test: _cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.testColorIsHex,\n    convert: _cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.convertColorHexToRgb\n  }\n];\nvar primary = {\n  aspectRatio,\n  crop,\n  gravity,\n  height,\n  width\n};\nvar position = {\n  angle,\n  gravity,\n  x,\n  y\n};\nvar text = {\n  alignment: {\n    qualifier: false,\n    order: 6\n  },\n  antialias: {\n    qualifier: \"antialias\"\n  },\n  border: {\n    qualifier: \"bo\",\n    location: \"primary\"\n  },\n  color: {\n    qualifier: \"co\",\n    location: \"primary\",\n    converters: convertersColors\n  },\n  fontFamily: {\n    qualifier: false,\n    order: 1\n  },\n  fontSize: {\n    qualifier: false,\n    order: 2\n  },\n  fontStyle: {\n    qualifier: false,\n    order: 4\n  },\n  fontWeight: {\n    qualifier: false,\n    order: 3\n  },\n  hinting: {\n    qualifier: \"hinting\"\n  },\n  letterSpacing: {\n    qualifier: \"letter_spacing\"\n  },\n  lineSpacing: {\n    qualifier: \"line_spacing\"\n  },\n  stroke: {\n    qualifier: \"self\",\n    order: 7\n  },\n  textDecoration: {\n    qualifier: false,\n    order: 5\n  }\n};\nvar effects = {\n  angle,\n  art: {\n    prefix: \"e\",\n    qualifier: \"art\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Applies the selected artistic filter.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_art\"\n      })\n    )\n  },\n  autoBrightness: {\n    prefix: \"e\",\n    qualifier: \"auto_brightness\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically adjusts the image brightness and blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_auto_brightness\"\n      })\n    )\n  },\n  autoColor: {\n    prefix: \"e\",\n    qualifier: \"auto_color\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically adjusts the image color balance and blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_auto_color\"\n      })\n    )\n  },\n  autoContrast: {\n    prefix: \"e\",\n    qualifier: \"auto_contrast\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically adjusts the image contrast and blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_auto_contrast\"\n      })\n    )\n  },\n  assistColorblind: {\n    prefix: \"e\",\n    qualifier: \"assist_colorblind\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies stripes or color adjustment to help people with common color blind conditions to differentiate between colors that are similar for them.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_assist_colorblind\"\n      })\n    )\n  },\n  background: {\n    qualifier: \"b\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Applies a background to empty or transparent areas.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#b_background\"\n      })\n    )\n  },\n  blackwhite: {\n    prefix: \"e\",\n    qualifier: \"blackwhite\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Converts an image to black and white.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blackwhite\"\n      })\n    )\n  },\n  blur: {\n    prefix: \"e\",\n    qualifier: \"blur\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a blurring filter to an asset.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blur\"\n      })\n    )\n  },\n  blurFaces: {\n    prefix: \"e\",\n    qualifier: \"blur_faces\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Blurs all detected faces in an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blur_faces\"\n      })\n    )\n  },\n  blurRegion: {\n    prefix: \"e\",\n    qualifier: \"blur_region\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a blurring filter to the region of an image specified by x, y, width and height, or an area of text. If no region is specified, the whole image is blurred.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blur_region\"\n      })\n    )\n  },\n  border: {\n    qualifier: \"bo\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Adds a solid border around an image or video.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#bo_border\"\n      })\n    )\n  },\n  brightness: {\n    prefix: \"e\",\n    qualifier: \"brightness\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the image or video brightness.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_brightness\"\n      })\n    )\n  },\n  brightnessHSB: {\n    prefix: \"e\",\n    qualifier: \"brightness_hsb\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts image brightness modulation in HSB to prevent artifacts in some images.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_brightness_hsb\"\n      })\n    )\n  },\n  cartoonify: {\n    prefix: \"e\",\n    qualifier: \"cartoonify\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a cartoon effect to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_cartoonify\"\n      })\n    )\n  },\n  color: {\n    qualifier: \"co\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"A qualifier that specifies the color to use with the corresponding transformation.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#co_color\"\n      })\n    ),\n    converters: convertersColors\n  },\n  colorize: {\n    prefix: \"e\",\n    qualifier: \"colorize\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Colorizes an image. By default, gray is used for colorization. You can specify a different color using the color qualifier.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_colorize\"\n      })\n    )\n  },\n  contrast: {\n    prefix: \"e\",\n    qualifier: \"contrast\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image or video contrast.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_contrast\"\n      })\n    )\n  },\n  displace: {\n    prefix: \"e\",\n    qualifier: \"distort\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Displaces the pixels in an image according to the color channels of the pixels in another specified image (a gradient map specified with the overlay parameter).\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_displace\"\n      })\n    )\n  },\n  distort: {\n    prefix: \"e\",\n    qualifier: \"distort\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Distorts an image to a new shape by either adjusting its corners or by warping it into an arc.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_distort\"\n      })\n    )\n  },\n  fillLight: {\n    prefix: \"e\",\n    qualifier: \"fill_light\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the fill light and optionally blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_fill_light\"\n      })\n    )\n  },\n  gamma: {\n    prefix: \"e\",\n    qualifier: \"gamma\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the image or video gamma level.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_gamma\"\n      })\n    )\n  },\n  gradientFade: {\n    prefix: \"e\",\n    qualifier: \"gradient_fade\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a gradient fade effect from the edge of an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_gradient_fade\"\n      })\n    )\n  },\n  grayscale: {\n    prefix: \"e\",\n    qualifier: \"grayscale\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n      JSON.stringify({\n        text: \"Converts an image to grayscale (multiple shades of gray).\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_grayscale\"\n      })\n    )\n  },\n  hue: {\n    prefix: \"e\",\n    qualifier: \"hue\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image's hue.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_hue\"\n      })\n    )\n  },\n  improve: {\n    prefix: \"e\",\n    qualifier: \"improve\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image's colors, contrast and brightness to improve its appearance.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_improve\"\n      })\n    )\n  },\n  loop: {\n    prefix: \"e\",\n    qualifier: \"loop\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number(), zod__WEBPACK_IMPORTED_MODULE_0__.z.string()]).describe(\n      JSON.stringify({\n        text: \"Loops a video or animated image the specified number of times.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_loop\"\n      })\n    )\n  },\n  multiply: {\n    prefix: \"e\",\n    qualifier: \"multiply\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n      JSON.stringify({\n        text: \"A qualifier that blends image layers using the multiply blend mode\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_multiply\"\n      })\n    )\n  },\n  negate: {\n    prefix: \"e\",\n    qualifier: \"negate\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"https://cloudinary.com/documentation/transformation_reference#e_negate\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_negate\"\n      })\n    )\n  },\n  noise: {\n    prefix: \"e\",\n    qualifier: \"noise\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n      JSON.stringify({\n        text: \"https://cloudinary.com/documentation/transformation_reference#e_noise\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_noise\"\n      })\n    )\n  },\n  oilPaint: {\n    prefix: \"e\",\n    qualifier: \"oil_paint\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"https://cloudinary.com/documentation/transformation_reference#e_oil_paint\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_oil_paint\"\n      })\n    )\n  },\n  opacity: {\n    qualifier: \"o\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the opacity of an asset and makes it semi-transparent.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#o_opacity\"\n      })\n    )\n  },\n  outline: {\n    prefix: \"e\",\n    qualifier: \"outline\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adds an outline effect to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_outline\"\n      })\n    )\n  },\n  pixelate: {\n    prefix: \"e\",\n    qualifier: \"pixelate\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a pixelation effect.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_pixelate\"\n      })\n    )\n  },\n  pixelateFaces: {\n    prefix: \"e\",\n    qualifier: \"pixelate_faces\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Pixelates all detected faces in an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_pixelate_faces\"\n      })\n    )\n  },\n  pixelateRegion: {\n    prefix: \"e\",\n    qualifier: \"pixelate_region\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Pixelates the region of an image specified by x, y, width and height, or an area of text.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_pixelate_region\"\n      })\n    )\n  },\n  radius: {\n    qualifier: \"r\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n      JSON.stringify({\n        text: \"Rounds the corners of an image or video.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#r_round_corners\"\n      })\n    )\n  },\n  redeye: {\n    prefix: \"e\",\n    qualifier: \"redeye\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically removes red eyes in an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_redeye\"\n      })\n    )\n  },\n  replaceColor: {\n    prefix: \"e\",\n    qualifier: \"replace_color\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Maps an input color and those similar to the input color to corresponding shades of a specified output color.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_replace_color\"\n      })\n    )\n  },\n  saturation: {\n    prefix: \"e\",\n    qualifier: \"saturation\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image or video saturation level.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_saturation\"\n      })\n    )\n  },\n  screen: {\n    prefix: \"e\",\n    qualifier: \"screen\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n      JSON.stringify({\n        text: \"A qualifier that blends image layers using the screen blend mode.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_screen\"\n      })\n    )\n  },\n  sepia: {\n    prefix: \"e\",\n    qualifier: \"sepia\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Changes the color scheme of an image to sepia.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_sepia\"\n      })\n    )\n  },\n  shadow: {\n    prefix: \"e\",\n    qualifier: \"shadow\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adds a gray shadow to the bottom right of an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_shadow\"\n      })\n    )\n  },\n  sharpen: {\n    prefix: \"e\",\n    qualifier: \"sharpen\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a sharpening filter.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_sharpen\"\n      })\n    )\n  },\n  shear: {\n    prefix: \"e\",\n    qualifier: \"shear\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n      JSON.stringify({\n        text: \"Skews an image according to the two specified values in degrees.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_shear\"\n      })\n    )\n  },\n  simulateColorblind: {\n    prefix: \"e\",\n    qualifier: \"simulate_colorblind\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Simulates the way an image would appear to someone with the specified color blind condition.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_simulate_colorblind\"\n      })\n    )\n  },\n  tint: {\n    prefix: \"e\",\n    qualifier: \"tint\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Blends an image with one or more tint colors at a specified intensity.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_tint\"\n      })\n    )\n  },\n  trim: {\n    prefix: \"e\",\n    qualifier: \"trim\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Detects and removes image edges whose color is similar to the corner pixels.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_trim\"\n      })\n    )\n  },\n  unsharpMask: {\n    prefix: \"e\",\n    qualifier: \"unsharp_mask\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies an unsharp mask filter to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_unsharp_mask\"\n      })\n    )\n  },\n  vectorize: {\n    prefix: \"e\",\n    qualifier: \"vectorize\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Vectorizes an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_vectorize\"\n      })\n    )\n  },\n  vibrance: {\n    prefix: \"e\",\n    qualifier: \"vibrance\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a vibrance filter to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_vibrance\"\n      })\n    )\n  },\n  vignette: {\n    prefix: \"e\",\n    qualifier: \"vignette\",\n    schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a vignette effect to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_vignette\"\n      })\n    )\n  }\n};\n\n// src/types/asset.ts\n\n\n// src/plugins/cropping.ts\n\n\n// src/lib/transformations.ts\nfunction constructTransformation({\n  prefix,\n  qualifier,\n  value,\n  converters\n}) {\n  let transformation = \"\";\n  if (prefix) {\n    transformation = `${prefix}_`;\n  }\n  let transformationValue = value;\n  converters?.forEach(({ test, convert }) => {\n    if (!test(transformationValue)) return;\n    transformationValue = convert(transformationValue);\n  });\n  if (transformationValue === true || transformationValue === \"true\") {\n    return `${transformation}${qualifier}`;\n  }\n  if (typeof transformationValue === \"string\" || typeof transformationValue === \"number\") {\n    if (prefix) {\n      return `${transformation}${qualifier}:${transformationValue}`;\n    } else {\n      return `${qualifier}_${transformationValue}`;\n    }\n  }\n}\nfunction promptArrayToString(promptArray) {\n  return `(${promptArray.join(\";\")})`;\n}\nfunction normalizeNumberParameter(param) {\n  if (typeof param !== \"string\") return param;\n  return parseInt(param);\n}\n\n// src/plugins/cropping.ts\nvar cropsAspectRatio = [\"auto\", \"crop\", \"fill\", \"lfill\", \"fill_pad\", \"thumb\"];\nvar cropsGravityAuto = [\"auto\", \"crop\", \"fill\", \"lfill\", \"fill_pad\", \"thumb\"];\nvar cropsWithZoom = [\"crop\", \"thumb\"];\nvar DEFAULT_CROP = \"limit\";\nvar cropOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  aspectRatio: aspectRatio.schema.optional(),\n  type: crop.schema,\n  gravity: gravity.schema.optional(),\n  height: height.schema.optional(),\n  width: width.schema.optional(),\n  x: x.schema.optional(),\n  y: y.schema.optional(),\n  zoom: zoom.schema.optional(),\n  source: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional()\n});\nvar croppingProps = {\n  aspectRatio: aspectRatio.schema.optional(),\n  crop: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    crop.schema,\n    cropOptionsSchema,\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.array(cropOptionsSchema)\n  ]).default(DEFAULT_CROP).optional(),\n  gravity: gravity.schema.optional(),\n  zoom: zoom.schema.optional()\n};\nvar croppingPlugin = {\n  props: croppingProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    let crops = [];\n    if (typeof options.crop === \"string\" || typeof options.crop === \"undefined\") {\n      crops.push({\n        aspectRatio: options.aspectRatio,\n        height: options.height,\n        gravity: options.gravity,\n        type: options.crop || DEFAULT_CROP,\n        width: options.width,\n        zoom: options.zoom\n      });\n    } else if (typeof options.crop === \"object\" && !Array.isArray(options.crop)) {\n      crops.push(options.crop);\n    } else if (Array.isArray(options.crop)) {\n      crops = options.crop;\n    }\n    if (crops.length === 1 && crops[0].source === true) {\n      crops.push({\n        aspectRatio: options.aspectRatio,\n        width: options.width,\n        height: options.height,\n        gravity: options.gravity,\n        type: DEFAULT_CROP,\n        zoom: options.zoom\n      });\n    }\n    const finalTransformations = [];\n    const sourceTransformations = [];\n    for (const crop2 of crops) {\n      const cropDimensions = {\n        width: crop2.width,\n        height: crop2.height\n      };\n      if (typeof cropDimensions.width === \"undefined\" && typeof crop2.aspectRatio === \"undefined\") {\n        cropDimensions.width = options.width;\n        if (typeof cropDimensions.height === \"undefined\") {\n          cropDimensions.height = options.height;\n        }\n      }\n      const transformations = collectTransformations({\n        aspectRatio: crop2.aspectRatio,\n        gravity: crop2.gravity,\n        type: crop2.type || DEFAULT_CROP,\n        x: crop2.x,\n        y: crop2.y,\n        zoom: crop2.zoom,\n        ...cropDimensions\n      });\n      if (transformations.length > 0) {\n        if (crop2.source === true) {\n          sourceTransformations.push(transformations);\n        } else {\n          finalTransformations.push(transformations);\n        }\n      }\n    }\n    sourceTransformations.forEach((transformation) => {\n      if (transformation.length > 0) {\n        cldAsset.addTransformation(transformation.join(\",\"));\n      }\n    });\n    const results = {\n      options: {}\n    };\n    if (results.options && finalTransformations.length > 0) {\n      results.options.resize = finalTransformations.map((transformation) => transformation.join(\",\")).join(\"/\");\n    }\n    return results;\n  }\n};\nfunction collectTransformations(collectOptions) {\n  const { aspectRatio: aspectRatio2, type: crop2, x: x2, y: y2, zoom: zoom2 } = collectOptions;\n  let gravity2 = collectOptions.gravity;\n  const height2 = normalizeNumberParameter(collectOptions.height);\n  const width2 = normalizeNumberParameter(collectOptions.width);\n  const transformations = [];\n  const hasDefinedDimensions = height2 || width2;\n  const hasValidAspectRatio = aspectRatio2 && cropsAspectRatio.includes(crop2);\n  const hasXCoordinate = typeof x2 === \"number\" || typeof x2 === \"string\";\n  const hasYCoordinate = typeof y2 === \"number\" || typeof y2 === \"string\";\n  const hasDefinedCoordinates = hasXCoordinate || hasYCoordinate;\n  if (crop2 && (hasDefinedDimensions || hasValidAspectRatio || hasDefinedCoordinates)) {\n    transformations.push(`c_${crop2}`);\n  }\n  if (hasValidAspectRatio) {\n    transformations.push(`ar_${aspectRatio2}`);\n  }\n  if (width2) {\n    transformations.push(`w_${width2}`);\n  }\n  if (![\"limit\"].includes(crop2) && typeof height2 === \"number\") {\n    transformations.push(`h_${height2}`);\n  }\n  if (hasXCoordinate) {\n    transformations.push(`x_${x2}`);\n  }\n  if (hasYCoordinate) {\n    transformations.push(`y_${y2}`);\n  }\n  if (!gravity2 && cropsGravityAuto.includes(crop2) && !hasDefinedCoordinates) {\n    gravity2 = \"auto\";\n  }\n  if (gravity2) {\n    if (gravity2 === \"auto\" && !cropsGravityAuto.includes(crop2)) {\n      console.warn(\n        `Auto gravity can only be used with crop modes: ${cropsGravityAuto.join(\n          \", \"\n        )}. Not applying gravity.`\n      );\n    } else {\n      transformations.push(`g_${gravity2}`);\n    }\n  }\n  if (zoom2) {\n    if (zoom2 === \"auto\" && !cropsWithZoom.includes(crop2)) {\n      console.warn(\n        `Zoom can only be used with crop modes: ${cropsWithZoom.join(\n          \", \"\n        )}. Not applying zoom.`\n      );\n    } else {\n      transformations.push(`z_${zoom2}`);\n    }\n  }\n  return transformations;\n}\n\n// src/plugins/effects.ts\n\nvar effectProps = {\n  angle: effects.angle.schema.optional(),\n  art: effects.art.schema.optional(),\n  autoBrightness: effects.autoBrightness.schema.optional(),\n  autoColor: effects.autoColor.schema.optional(),\n  autoContrast: effects.autoContrast.schema.optional(),\n  assistColorblind: effects.assistColorblind.schema.optional(),\n  background: effects.background.schema.optional(),\n  blackwhite: effects.blackwhite.schema.optional(),\n  blur: effects.blur.schema.optional(),\n  blurFaces: effects.blurFaces.schema.optional(),\n  blurRegion: effects.blurRegion.schema.optional(),\n  border: effects.border.schema.optional(),\n  brightness: effects.brightness.schema.optional(),\n  brightnessHSB: effects.brightnessHSB.schema.optional(),\n  cartoonify: effects.cartoonify.schema.optional(),\n  color: effects.color.schema.optional(),\n  colorize: effects.colorize.schema.optional(),\n  contrast: effects.contrast.schema.optional(),\n  distort: effects.distort.schema.optional(),\n  fillLight: effects.fillLight.schema.optional(),\n  gamma: effects.gamma.schema.optional(),\n  gradientFade: effects.gradientFade.schema.optional(),\n  grayscale: effects.grayscale.schema.optional(),\n  improve: effects.improve.schema.optional(),\n  loop: effects.loop.schema.optional(),\n  multiply: effects.multiply.schema.optional(),\n  negate: effects.negate.schema.optional(),\n  oilPaint: effects.oilPaint.schema.optional(),\n  opacity: effects.opacity.schema.optional(),\n  outline: effects.outline.schema.optional(),\n  pixelate: effects.pixelate.schema.optional(),\n  pixelateFaces: effects.pixelateFaces.schema.optional(),\n  pixelateRegion: effects.pixelateRegion.schema.optional(),\n  radius: effects.radius.schema.optional(),\n  redeye: effects.redeye.schema.optional(),\n  replaceColor: effects.replaceColor.schema.optional(),\n  saturation: effects.saturation.schema.optional(),\n  screen: effects.screen.schema.optional(),\n  sepia: effects.sepia.schema.optional(),\n  shadow: effects.shadow.schema.optional(),\n  sharpen: effects.sharpen.schema.optional(),\n  shear: effects.shear.schema.optional(),\n  simulateColorblind: effects.simulateColorblind.schema.optional(),\n  tint: effects.tint.schema.optional(),\n  trim: effects.trim.schema.optional(),\n  unsharpMask: effects.unsharpMask.schema.optional(),\n  vectorize: effects.vectorize.schema.optional(),\n  vibrance: effects.vibrance.schema.optional(),\n  vignette: effects.vignette.schema.optional()\n};\nvar effectsProps = {\n  effects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object(effectProps)).describe(\n    JSON.stringify({\n      text: \"Array of objects specifying transformations to be applied to asset.\"\n    })\n  ).optional(),\n  ...effectProps\n};\nvar effectsPlugin = {\n  props: effectsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const transformationStrings = constructTransformationString({\n      effects,\n      options\n    });\n    transformationStrings.filter((t) => !!t).forEach((transformation) => cldAsset.effect(transformation));\n    if (Array.isArray(options?.effects)) {\n      options?.effects.forEach((effectsSet) => {\n        const transformationString = constructTransformationString({\n          effects,\n          options: effectsSet\n        }).filter((t) => !!t).join(\",\");\n        cldAsset.effect(transformationString);\n      });\n    }\n    function constructTransformationString({\n      effects: effects2,\n      options: options2\n    }) {\n      return Object.keys(effects2).map(\n        (key) => {\n          const { prefix, qualifier, converters } = effects2[key];\n          return constructTransformation({\n            qualifier,\n            prefix,\n            value: options2?.[key],\n            converters\n          });\n        }\n      );\n    }\n    return {};\n  }\n};\n\n// src/plugins/flags.ts\nvar { flagsEnum: flagsEnum2 } = parameters_exports;\nvar flagsProps = {\n  flags: flags.schema.optional()\n};\nvar flagsPlugin = {\n  props: flagsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { flags: flags2 = [] } = options;\n    if (Array.isArray(flags2) && flags2.length > 0) {\n      flags2.forEach((flag) => {\n        const { success } = flagsEnum2.safeParse(flag);\n        if (!success) {\n          if (true) {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        cldAsset.addFlag(flag);\n      });\n    } else if (typeof flags2 === \"object\") {\n      Object.entries(flags2).forEach(([qualifier, value]) => {\n        const { success } = flagsEnum2.safeParse(qualifier);\n        if (!success) {\n          if (true) {\n            console.warn(`Invalid flag ${qualifier}, not applying.`);\n          }\n          return;\n        }\n        cldAsset.addTransformation(`fl_${qualifier}:${value}`);\n      });\n    }\n    return {};\n  }\n};\n\n// src/plugins/named-transformations.ts\n\nvar NamedTransformationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string();\nvar namedTransformationsProps = {\n  namedTransformations: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([NamedTransformationSchema, zod__WEBPACK_IMPORTED_MODULE_0__.z.array(NamedTransformationSchema)]).describe(\n    JSON.stringify({\n      text: \"Named transformations to apply to asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#named_transformations\"\n    })\n  ).optional(),\n  /**\n   * @deprecated use {@link `namedTransformations`} instead\n   */\n  transformations: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([NamedTransformationSchema, zod__WEBPACK_IMPORTED_MODULE_0__.z.array(NamedTransformationSchema)]).describe(\n    JSON.stringify({\n      text: \"Deprecated: use namedTransformations instead\",\n      url: \"https://cloudinary.com/documentation/image_transformations#named_transformations\"\n    })\n  ).optional()\n};\nvar namedTransformationsPlugin = {\n  props: namedTransformationsProps,\n  strict: true,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { transformations, namedTransformations } = options;\n    if (transformations && process.env.NODE_ENVIRONMENT === \"development\") {\n      console.warn(\n        \"The transformations prop is deprecated. Please use namedTransformations instead.\"\n      );\n    }\n    let _namedTransformations = namedTransformations || transformations || [];\n    if (!Array.isArray(_namedTransformations)) {\n      _namedTransformations = [_namedTransformations];\n    }\n    _namedTransformations.forEach((transformation) => {\n      cldAsset.addTransformation(`t_${transformation}`);\n    });\n    return {};\n  }\n};\n\n// src/plugins/overlays.ts\n\n\nvar overlayTextSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  alignment: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  antialias: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  border: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  color: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  fontFamily: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  fontSize: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n  fontStyle: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).optional(),\n  fontWeight: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  hinting: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).optional(),\n  letterSpacing: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).optional(),\n  lineSpacing: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).optional(),\n  stroke: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  text: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n  // Required if using object format\n});\nvar overlayPositionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  angle: angle.schema.optional(),\n  gravity: gravity.schema.optional(),\n  x: x.schema.optional(),\n  y: y.schema.optional()\n});\nvar overlaySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  appliedEffects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({})).optional(),\n  appliedFlags: flags.schema.optional(),\n  effects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({})).optional(),\n  crop: crop.schema.optional(),\n  flags: flags.schema.optional(),\n  height: height.schema.optional(),\n  position: overlayPositionSchema.optional(),\n  publicId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  text: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), overlayTextSchema]).optional(),\n  url: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  width: width.schema.optional()\n});\nvar DEFAULT_TEXT_OPTIONS = {\n  color: \"black\",\n  fontFamily: \"Arial\",\n  fontSize: 200,\n  fontWeight: \"bold\"\n};\nvar overlaysProps = {\n  overlay: overlaySchema.describe(\n    JSON.stringify({\n      text: \"Image or text layer that is applied on top of the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional(),\n  overlays: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(overlaySchema).describe(\n    JSON.stringify({\n      text: \"Image or text layers that are applied on top of the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional(),\n  text: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"Text to be overlaid on asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#transformation_url_structure\"\n    })\n  ).optional()\n};\nvar overlaysPlugin = {\n  props: overlaysProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { text: text2, overlays = [] } = options;\n    const type = \"overlay\";\n    const typeQualifier = \"l\";\n    if (Array.isArray(overlays)) {\n      overlays.forEach(applyOverlay);\n    }\n    if (typeof text2 === \"string\") {\n      applyOverlay({\n        text: Object.assign({}, DEFAULT_TEXT_OPTIONS, {\n          text: text2\n        })\n      });\n    } else if (typeof text2 === \"object\") {\n      applyOverlay({\n        text: Object.assign({}, DEFAULT_TEXT_OPTIONS, text2)\n      });\n    }\n    function applyOverlay({\n      publicId,\n      url,\n      position: position2,\n      text: text3,\n      effects: layerEffects = [],\n      appliedEffects = [],\n      flags: layerFlags = [],\n      appliedFlags = [],\n      ...options2\n    }) {\n      const hasPublicId = typeof publicId === \"string\";\n      const hasUrl = typeof url === \"string\";\n      const hasText = typeof text3 === \"object\" || typeof text3 === \"string\";\n      const hasPosition = typeof position2 === \"object\";\n      if (!hasPublicId && !hasUrl && !hasText) {\n        console.warn(`An ${type} is missing Public ID, URL, or Text`);\n        return;\n      }\n      let layerTransformation;\n      if (hasText) {\n        layerTransformation = `${typeQualifier}_text`;\n      } else if (hasPublicId) {\n        layerTransformation = `${typeQualifier}_${publicId.replace(\n          /\\//g,\n          \":\"\n        )}`;\n      } else if (hasUrl) {\n        layerTransformation = `${typeQualifier}_fetch:${(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.encodeBase64)(url)}`;\n      }\n      const primary2 = [];\n      const applied = [];\n      Object.keys(options2).forEach((key) => {\n        if (!(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(primary, key)) return;\n        const { qualifier, converters } = primary[key];\n        const transformation = constructTransformation({\n          qualifier,\n          value: options2[key],\n          converters\n        });\n        if (transformation) {\n          primary2.push(transformation);\n        }\n      });\n      layerEffects.forEach((effect) => {\n        Object.keys(effect).forEach((key) => {\n          const effectQualifier = primary[key] || effects[key] || position[key];\n          if (!effectQualifier) return;\n          const { qualifier, prefix, converters } = effectQualifier;\n          const transformation = constructTransformation({\n            qualifier,\n            prefix,\n            value: effect[key],\n            converters\n          });\n          if (transformation) {\n            primary2.push(transformation);\n          }\n        });\n      });\n      appliedEffects.forEach((effect) => {\n        Object.keys(effect).forEach((key) => {\n          const effectQualifier = primary[key] || effects[key] || position[key];\n          if (!effectQualifier) return;\n          const { qualifier, prefix, converters } = effectQualifier;\n          const transformation = constructTransformation({\n            qualifier,\n            prefix,\n            value: effect[key],\n            converters\n          });\n          if (transformation) {\n            applied.push(transformation);\n          }\n        });\n      });\n      const activeLayerFlags = Array.isArray(layerFlags) ? layerFlags : [layerFlags];\n      activeLayerFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (true) {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        primary2.push(`fl_${flag}`);\n      });\n      const activeAppliedFlags = Array.isArray(appliedFlags) ? appliedFlags : [appliedFlags];\n      activeAppliedFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (true) {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        applied.push(`fl_${flag}`);\n      });\n      if (hasText) {\n        if (typeof text3 === \"string\") {\n          text3 = {\n            ...DEFAULT_TEXT_OPTIONS,\n            text: text3\n          };\n        }\n        const textTransformations = [];\n        if (typeof text3 === \"object\") {\n          const textOptions = Object.keys(text3).filter((key) => (0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(text, key)).map((key) => {\n            const value = text3 && (0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(text3, key) && text3[key];\n            return {\n              ...text[key],\n              key,\n              value,\n              order: text[key].order || 99\n            };\n          });\n          const sortedTextOptions = (0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.sortByKey)(textOptions, \"order\");\n          for (const textOption of sortedTextOptions) {\n            const { key, value, qualifier, location, converters } = textOption;\n            let textValue = value;\n            converters?.forEach(({ test, convert }) => {\n              if (!test(value)) return;\n              textValue = convert(value);\n            });\n            if (location === \"primary\") {\n              primary2.push(`${qualifier}_${textValue}`);\n            } else if (qualifier === \"self\") {\n              textTransformations.push(key);\n            } else if (qualifier) {\n              textTransformations.push(`${qualifier}_${textValue}`);\n            } else {\n              textTransformations.push(textValue);\n            }\n          }\n        }\n        const specialCharacters = {\n          \".\": \"%2E\",\n          \",\": \"%2C\",\n          \"/\": \"%2F\"\n        };\n        let layerText = text3?.text || \"\";\n        if (typeof layerText === \"string\") {\n          Object.keys(specialCharacters)?.forEach((character) => {\n            layerText = layerText?.replace(\n              character,\n              specialCharacters[character]\n            );\n          });\n        }\n        layerTransformation = `${layerTransformation}:${textTransformations.join(\n          \"_\"\n        )}:${layerText}`;\n      }\n      if (hasPosition) {\n        Object.keys(position2).forEach((key) => {\n          if (!(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(position, key) || !(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(position2, key))\n            return;\n          const { qualifier, converters } = position[key];\n          const transformation = constructTransformation({\n            qualifier,\n            value: position2[key],\n            converters\n          });\n          if (transformation) {\n            applied.push(transformation);\n          }\n        });\n      }\n      if (primary2.length > 0) {\n        layerTransformation = `${layerTransformation},${primary2.join(\",\")}`;\n      }\n      layerTransformation = `${layerTransformation}/fl_layer_apply,fl_no_overflow`;\n      if (applied.length > 0) {\n        layerTransformation = `${layerTransformation},${applied.join(\",\")}`;\n      }\n      cldAsset.addTransformation(layerTransformation);\n    }\n    return {};\n  }\n};\n\n// src/plugins/preserve-transformations.ts\n\n\nvar preserveTransformationsProps = {\n  preserveTransformations: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n    JSON.stringify({\n      text: \"Preserves transformations from a Cloudinary URL when using using a Cloudinary URL as the asset source (src).\"\n    })\n  ).optional()\n};\nvar preserveTransformationsPlugin = {\n  props: preserveTransformationsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { preserveTransformations = false } = options;\n    if (preserveTransformations) {\n      try {\n        const transformations = (0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.getTransformations)(options.src).map((t) => t.join(\",\"));\n        transformations.flat().forEach((transformation) => {\n          cldAsset.addTransformation(transformation);\n        });\n      } catch (e) {\n        console.warn(`Failed to preserve transformations: ${e.message}`);\n      }\n    }\n    return {};\n  }\n};\n\n// src/plugins/raw-transformations.ts\n\nvar RawTransformationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string();\nvar rawTransformationsProps = {\n  rawTransformations: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([RawTransformationSchema, zod__WEBPACK_IMPORTED_MODULE_0__.z.array(RawTransformationSchema)]).describe(\n    JSON.stringify({\n      text: \"Array of transformation parameters using the Cloudinary URL API to apply to an asset.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference\"\n    })\n  ).optional()\n};\nvar rawTransformationsPlugin = {\n  props: rawTransformationsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    let { rawTransformations = [] } = options;\n    if (!Array.isArray(rawTransformations)) {\n      rawTransformations = [rawTransformations];\n    }\n    rawTransformations.forEach((transformation) => {\n      cldAsset.addTransformation(transformation);\n    });\n    return {};\n  }\n};\n\n// src/plugins/remove-background.ts\n\nvar removeBackgroundProps = {\n  removeBackground: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n    JSON.stringify({\n      text: \"Removes the background of an image using the Cloudinary AI Background Removal Add-On (Required).\",\n      url: \"https://cloudinary.com/documentation/cloudinary_ai_background_removal_addon\"\n    })\n  ).optional()\n};\nvar removeBackgroundPlugin = {\n  props: removeBackgroundProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { removeBackground = false } = options;\n    if (removeBackground) {\n      cldAsset.effect(\"e_background_removal\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/sanitize.ts\n\nvar sanitizeProps = {\n  sanitize: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n    JSON.stringify({\n      text: \"Runs a sanitizer on SVG images.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#fl_sanitize\"\n    })\n  ).optional()\n};\nvar sanitizePlugin = {\n  props: sanitizeProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { sanitize = true } = options;\n    const shouldApplySanitizer = sanitize && (options.format === \"svg\" || cldAsset.publicID.endsWith(\".svg\"));\n    if (shouldApplySanitizer) {\n      cldAsset.effect(\"fl_sanitize\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/seo.ts\n\nvar seoProps = {\n  seoSuffix: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"Configures the URL to include an SEO-friendly suffix in the URL\",\n      url: \"https://cloudinary.com/documentation/advanced_url_delivery_options#seo_friendly_media_asset_urls\"\n    })\n  ).optional()\n};\nvar seoPlugin = {\n  props: seoProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { seoSuffix } = options;\n    if (typeof seoSuffix === \"string\") {\n      if (options.deliveryType === \"fetch\") {\n        console.warn(\n          \"SEO suffix is not supported with a delivery type of fetch\"\n        );\n      } else {\n        cldAsset.setSuffix(seoSuffix);\n      }\n    }\n    return {};\n  }\n};\n\n// src/plugins/underlays.ts\n\n\nvar underlayPositionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  angle: angle.schema.optional(),\n  gravity: gravity.schema.optional(),\n  x: x.schema.optional(),\n  y: y.schema.optional()\n});\nvar underlaySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  appliedEffects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({})).optional(),\n  appliedFlags: flags.schema.optional(),\n  effects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({})).optional(),\n  crop: crop.schema.optional(),\n  flags: flags.schema.optional(),\n  height: height.schema.optional(),\n  position: underlayPositionSchema.optional(),\n  publicId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  type: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  url: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  width: width.schema.optional()\n});\nvar underlaysProps = {\n  underlay: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"Public ID of image that is applied under the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional(),\n  underlays: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(underlaySchema).describe(\n    JSON.stringify({\n      text: \"Image layers that are applied under the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional()\n};\nvar underlaysPlugin = {\n  props: underlaysProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { underlay, underlays = [] } = options;\n    const typeQualifier = \"u\";\n    if (Array.isArray(underlays)) {\n      underlays.forEach(applyUnderlay);\n    }\n    if (typeof underlay === \"string\") {\n      const underlayOptions = {\n        publicId: underlay,\n        crop: \"fill\",\n        width: \"1.0\",\n        height: \"1.0\",\n        flags: [\"relative\"]\n      };\n      applyUnderlay(underlayOptions);\n    }\n    function applyUnderlay({\n      publicId,\n      type,\n      position: position2,\n      effects: layerEffects = [],\n      flags: layerFlags = [],\n      appliedFlags = [],\n      ...options2\n    }) {\n      const hasPublicId = typeof publicId === \"string\";\n      const hasPosition = typeof position2 === \"object\";\n      if (!hasPublicId) {\n        console.warn(`An ${type} is missing a Public ID`);\n        return;\n      }\n      let layerTransformation = `${typeQualifier}_${publicId.replace(\n        /\\//g,\n        \":\"\n      )}`;\n      const primary2 = [];\n      const applied = [];\n      Object.keys(options2).forEach((key) => {\n        if (!(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(primary, key)) return;\n        const { qualifier } = primary[key];\n        primary2.push(`${qualifier}_${options2[key]}`);\n      });\n      layerEffects.forEach((effect) => {\n        Object.keys(effect).forEach((key) => {\n          if (!(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(primary, key)) return;\n          const { qualifier } = primary[key];\n          primary2.push(`${qualifier}_${effect[key]}`);\n        });\n      });\n      if (hasPosition) {\n        Object.keys(position2).forEach(\n          (key) => {\n            if (!(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(position, key)) return;\n            const { qualifier } = position[key];\n            applied.push(`${qualifier}_${position2[key]}`);\n          }\n        );\n      }\n      const activeLayerFlags = Array.isArray(layerFlags) ? layerFlags : [layerFlags];\n      activeLayerFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (true) {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        primary2.push(`fl_${flag}`);\n      });\n      const activeAppliedFlags = Array.isArray(appliedFlags) ? appliedFlags : [appliedFlags];\n      activeAppliedFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (true) {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        applied.push(`fl_${flag}`);\n      });\n      layerTransformation = `${layerTransformation},${primary2.join(\",\")}`;\n      layerTransformation = `${layerTransformation}/fl_layer_apply,fl_no_overflow`;\n      if (applied.length > 0) {\n        layerTransformation = `${layerTransformation},${applied.join(\",\")}`;\n      }\n      cldAsset.addTransformation(layerTransformation);\n    }\n    return {};\n  }\n};\n\n// src/plugins/version.ts\n\nvar versionProps = {\n  version: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.number(), zod__WEBPACK_IMPORTED_MODULE_0__.z.string()]).describe(\n    JSON.stringify({\n      text: \"Custom version number to apply to asset URL.\",\n      url: \"https://cloudinary.com/documentation/advanced_url_delivery_options#asset_versions\"\n    })\n  ).optional()\n};\nvar versionPlugin = {\n  props: versionProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { version } = options;\n    if (typeof version === \"string\" || typeof version === \"number\") {\n      cldAsset.setVersion(`${version}`.replace(\"v\", \"\"));\n    }\n    return {};\n  }\n};\n\n// src/types/asset.ts\nvar assetOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  assetType: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"image\").describe(\n    JSON.stringify({\n      text: \"The type of asset to deliver.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#transformation_url_structure\"\n    })\n  ).optional(),\n  deliveryType: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"upload\").describe(\n    JSON.stringify({\n      text: \"Delivery method of the asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#delivery_types\"\n    })\n  ).optional(),\n  dpr: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n    JSON.stringify({\n      text: \"Delivery method of the asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#delivery_types\"\n    })\n  ).optional(),\n  format: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"auto\").describe(\n    JSON.stringify({\n      text: \"Converts (if necessary) and delivers an asset in the specified format.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#f_format\"\n    })\n  ).optional(),\n  height: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n    JSON.stringify({\n      text: \"Height of the given asset.\"\n    })\n  ).optional(),\n  quality: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number(), zod__WEBPACK_IMPORTED_MODULE_0__.z.string()]).default(\"auto\").describe(\n    JSON.stringify({\n      text: \"Quality of the delivered asset\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#q_quality\"\n    })\n  ).optional(),\n  src: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"Cloudinary Public ID or versioned Cloudinary URL (/v1234/)\"\n    })\n  ),\n  strictTransformations: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n    JSON.stringify({\n      text: \"Gives you the ability to have more control over what transformations are permitted to be used from your Cloudinary account.\",\n      url: \"https://cloudinary.com/documentation/control_access_to_media#strict_transformations\"\n    })\n  ).optional(),\n  width: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number()]).describe(\n    JSON.stringify({\n      text: \"Width of the given asset.\"\n    })\n  ).optional(),\n  // Spreading plugins instead of extend or merge to avoid excessive schema warning\n  // https://github.com/microsoft/TypeScript/issues/34933#issuecomment-**********\n  ...croppingProps,\n  ...effectsProps,\n  ...flagsProps,\n  ...namedTransformationsProps,\n  ...overlaysProps,\n  ...preserveTransformationsProps,\n  ...rawTransformationsProps,\n  ...removeBackgroundProps,\n  ...sanitizeProps,\n  ...seoProps,\n  ...underlaysProps,\n  ...versionProps\n});\n\n// src/types/image.ts\n\n\n// src/plugins/default-image.ts\n\n\nvar defaultImageProps = {\n  defaultImage: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"Configures the default image to use in case the given public ID is not available. Must include file extension.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#d_default_image\"\n    })\n  ).optional()\n};\nvar defaultImagePlugin = {\n  props: defaultImageProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { defaultImage } = options;\n    if (typeof defaultImage === \"string\") {\n      if (!(0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.getFormat)(defaultImage)) {\n        console.warn(\n          `The defaultImage prop may be missing a format and must include it along with the public ID. (Ex: myimage.jpg)`\n        );\n      }\n      const defaultImageId = defaultImage.replace(/\\//g, \":\");\n      cldAsset.addTransformation(`d_${defaultImageId}`);\n    }\n    return {};\n  }\n};\n\n// src/plugins/enhance.ts\n\nvar enhanceProps = {\n  enhance: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n    JSON.stringify({\n      text: \"Uses AI to analyze an image and make adjustments to enhance the appeal of the image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_enhance\"\n    })\n  ).optional()\n};\nvar enhancePlugin = {\n  props: enhanceProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { enhance = false } = options;\n    if (enhance) {\n      cldAsset.effect(\"e_enhance\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/extract.ts\n\nvar extractProps = {\n  extract: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    prompt.schema.optional(),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.array(prompt.schema).optional(),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      invert: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().default(false).optional(),\n      mode: extractMode.schema.optional(),\n      multiple: multiple.schema.default(false).optional(),\n      prompt: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([prompt.schema, zod__WEBPACK_IMPORTED_MODULE_0__.z.array(prompt.schema)]).optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Extracts an area or multiple areas of an image, described in natural language.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_extract\"\n    })\n  ).optional()\n};\nvar extractPlugin = {\n  props: extractProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { extract } = options;\n    if (!extract || typeof extract === \"undefined\") return {};\n    const properties = [];\n    if (typeof extract === \"string\") {\n      properties.push(`prompt_${extract}`);\n    } else if (Array.isArray(extract)) {\n      properties.push(`prompt_${formatPrompts(extract)}`);\n    } else if (typeof extract === \"object\" && !Array.isArray(extract)) {\n      const prompt2 = formatPrompts(extract.prompt);\n      if (prompt2) {\n        properties.push(`prompt_${prompt2}`);\n      }\n      if (extract.invert === true) {\n        properties.push(\"invert_true\");\n      }\n      if (typeof extract.mode === \"string\") {\n        properties.push(`mode_${extract.mode}`);\n      }\n      if (extract.multiple === true) {\n        properties.push(\"multiple_true\");\n      }\n    }\n    if (properties.length > 0) {\n      const transformation = `e_extract:${properties.join(\";\")}`;\n      cldAsset.addTransformation(transformation);\n    }\n    return {};\n  }\n};\nfunction formatPrompts(prompt2) {\n  if (typeof prompt2 === \"string\") return prompt2;\n  if (Array.isArray(prompt2)) {\n    return `(${prompt2.filter((prompt3) => typeof prompt3 === \"string\").join(\";\")})`;\n  }\n  return void 0;\n}\n\n// src/plugins/fill-background.ts\n\nvar defaultCrop = \"pad\";\nvar fillBackgroundProps = {\n  fillBackground: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean(),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      crop: crop.schema.optional(),\n      gravity: gravity.schema.optional(),\n      prompt: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Uses Generative Fill to extended padded image with AI\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#b_gen_fill\"\n    })\n  ).optional()\n};\nvar fillBackgroundPlugin = {\n  props: fillBackgroundProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { fillBackground } = options;\n    if (typeof fillBackground === \"undefined\") return {};\n    const width2 = normalizeNumberParameter(options.width);\n    const height2 = normalizeNumberParameter(options.height);\n    const hasDefinedDimensions = typeof height2 === \"number\" && typeof width2 === \"number\";\n    let aspectRatio2 = options.aspectRatio;\n    if (!aspectRatio2 && hasDefinedDimensions) {\n      aspectRatio2 = `${width2}:${height2}`;\n    }\n    if (!aspectRatio2) {\n      if (true) {\n        console.warn(\n          `Could not determine aspect ratio based on available options to use fillBackground. Please specify width and height or an aspect ratio.`\n        );\n      }\n      return {};\n    }\n    if (fillBackground === true) {\n      const properties = [\n        \"b_gen_fill\",\n        `ar_${aspectRatio2}`,\n        `c_${defaultCrop}`\n      ];\n      cldAsset.addTransformation(properties.join(\",\"));\n    } else if (typeof fillBackground === \"object\") {\n      const { crop: crop2 = defaultCrop, gravity: gravity2, prompt: prompt2 } = fillBackground;\n      const properties = [`ar_${aspectRatio2}`, `c_${crop2}`];\n      if (typeof prompt2 === \"string\") {\n        properties.unshift(`b_gen_fill:${prompt2}`);\n      } else {\n        properties.unshift(`b_gen_fill`);\n      }\n      if (typeof gravity2 === \"string\") {\n        properties.push(`g_${gravity2}`);\n      }\n      cldAsset.addTransformation(properties.join(\",\"));\n    }\n    return {};\n  }\n};\n\n// src/plugins/recolor.ts\n\nvar imageOptionsRecolorPromptSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())\n]);\nvar imageOptionsRecolorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  prompt: imageOptionsRecolorPromptSchema.optional(),\n  to: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n  multiple: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional()\n});\nvar recolorProps = {\n  recolor: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([imageOptionsRecolorPromptSchema, imageOptionsRecolorSchema]).describe(\n    JSON.stringify({\n      text: \"Uses generative AI to recolor parts of your image, maintaining the relative shading.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_recolor\"\n    })\n  ).optional()\n};\nvar recolorPlugin = {\n  props: recolorProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { recolor } = options;\n    const recolorOptions = {\n      prompt: void 0,\n      \"to-color\": void 0,\n      multiple: void 0\n    };\n    if (Array.isArray(recolor)) {\n      if (Array.isArray(recolor[0])) {\n        recolorOptions.prompt = promptArrayToString(recolor[0]);\n      } else {\n        recolorOptions.prompt = recolor[0];\n      }\n      if (typeof recolor[1] === \"string\") {\n        recolorOptions[\"to-color\"] = recolor[1];\n      }\n    } else if (typeof recolor === \"object\") {\n      if (typeof recolor.prompt === \"string\") {\n        recolorOptions.prompt = recolor.prompt;\n      } else if (Array.isArray(recolor.prompt)) {\n        recolorOptions.prompt = promptArrayToString(recolor.prompt);\n      }\n      if (typeof recolor.to === \"string\") {\n        recolorOptions[\"to-color\"] = recolor.to;\n      }\n      if (recolor.multiple === true) {\n        recolorOptions.multiple = `true`;\n      }\n    }\n    const transformation = Object.entries(recolorOptions).filter(([, value]) => !!value).map(([key, value]) => `${key}_${value}`).join(\";\");\n    if (transformation) {\n      cldAsset.addTransformation(`e_gen_recolor:${transformation}`);\n    }\n    return {};\n  }\n};\n\n// src/plugins/remove.ts\n\nvar imageOptionsRemovePromptSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())\n]);\nvar imageOptionsRemoveSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  prompt: imageOptionsRemovePromptSchema.optional(),\n  region: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.number()), zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.number()))]).optional(),\n  multiple: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n  removeShadow: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional()\n});\nvar removeProps = {\n  remove: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([imageOptionsRemovePromptSchema, imageOptionsRemoveSchema]).describe(\n    JSON.stringify({\n      text: \"Applies zooming and/or panning to an image, resulting in a video or animated image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_zoompan\"\n    })\n  ).optional()\n};\nvar removePlugin = {\n  props: removeProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { remove } = options;\n    const removeOptions = {\n      prompt: void 0,\n      region: void 0,\n      multiple: void 0,\n      \"remove-shadow\": void 0\n    };\n    if (typeof remove === \"string\") {\n      removeOptions.prompt = remove;\n    } else if (Array.isArray(remove)) {\n      removeOptions.prompt = promptArrayToString(remove);\n    } else if (typeof remove === \"object\") {\n      const hasPrompt = typeof remove.prompt === \"string\" || Array.isArray(remove.prompt);\n      const hasRegion = Array.isArray(remove.region);\n      if (hasPrompt && hasRegion) {\n        throw new Error(\n          \"Invalid remove options: you can not have both a prompt and a region. More info: https://cloudinary.com/documentation/transformation_reference#e_gen_remove\"\n        );\n      }\n      if (typeof remove.prompt === \"string\") {\n        removeOptions.prompt = remove.prompt;\n      } else if (Array.isArray(remove.prompt)) {\n        removeOptions.prompt = promptArrayToString(remove.prompt);\n      }\n      if (Array.isArray(remove.region)) {\n        removeOptions.region = regionArrayToString(remove.region);\n      }\n      if (remove.multiple === true) {\n        removeOptions.multiple = `true`;\n      }\n      if (remove.removeShadow === true) {\n        removeOptions[\"remove-shadow\"] = `true`;\n      }\n    }\n    const transformation = Object.entries(removeOptions).filter(([, value]) => !!value).map(([key, value]) => `${key}_${value}`).join(\";\");\n    if (transformation) {\n      cldAsset.addTransformation(`e_gen_remove:${transformation}`);\n    }\n    return {};\n  }\n};\nfunction regionArrayToString(regionArray) {\n  const indexes = {\n    0: \"x\",\n    1: \"y\",\n    2: \"w\",\n    3: \"h\"\n  };\n  const regionString = regionArray.map((region, index) => {\n    if (Array.isArray(region)) {\n      return regionArrayToString(region);\n    }\n    const key = indexes[index];\n    return `${key}_${region}`;\n  }).join(\";\");\n  return `(${regionString})`;\n}\n\n// src/plugins/replace-background.ts\n\nvar replaceBackgroundProps = {\n  replaceBackground: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean(),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      seed: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n      prompt: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Replaces the background of an image with an AI-generated background.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_background_replace\"\n    })\n  ).optional()\n};\nvar replaceBackgroundPlugin = {\n  props: replaceBackgroundProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { replaceBackground } = options;\n    if (!replaceBackground || typeof replaceBackground === \"undefined\") return {};\n    const properties = [];\n    if (typeof replaceBackground === \"object\") {\n      if (typeof replaceBackground.prompt !== \"undefined\") {\n        properties.push(`prompt_${replaceBackground.prompt}`);\n      }\n      if (typeof replaceBackground.seed === \"number\") {\n        properties.push(`seed_${replaceBackground.seed}`);\n      }\n    } else if (typeof replaceBackground === \"string\") {\n      properties.push(`prompt_${replaceBackground}`);\n    }\n    let transformation = \"e_gen_background_replace\";\n    if (properties.length > 0) {\n      transformation = `${transformation}:${properties.join(\";\")}`;\n    }\n    cldAsset.addTransformation(transformation);\n    return {};\n  }\n};\n\n// src/plugins/replace.ts\n\nvar replaceProps = {\n  replace: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      to: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n      from: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n      preserveGeometry: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Uses generative AI to replace parts of your image with something else.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_replace\"\n    })\n  ).optional()\n};\nvar replacePlugin = {\n  props: replaceProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { replace = null } = options;\n    if (replace) {\n      let from, to, preserveGeometry = false;\n      if (Array.isArray(replace)) {\n        from = replace[0];\n        to = replace[1];\n        preserveGeometry = replace[2] || false;\n      } else {\n        from = replace.from;\n        to = replace.to;\n        preserveGeometry = replace.preserveGeometry || false;\n      }\n      const properties = [`e_gen_replace:from_${from}`, `to_${to}`];\n      if (preserveGeometry) {\n        properties.push(`preserve-geometry_${preserveGeometry}`);\n      }\n      cldAsset.effect(properties.join(\";\"));\n    }\n    return {};\n  }\n};\n\n// src/plugins/restore.ts\n\nvar restoreProps = {\n  restore: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(\n    JSON.stringify({\n      text: \"Uses generative AI to restore details in poor quality images or images that may have become degraded through repeated processing and compression.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_restore\"\n    })\n  ).optional()\n};\nvar restorePlugin = {\n  props: restoreProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { restore = false } = options;\n    if (restore) {\n      cldAsset.effect(\"e_gen_restore\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/zoompan.ts\n\nvar zoompanProps = {\n  zoompan: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean(),\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      loop: effects.loop.schema.optional(),\n      options: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Applies zooming and/or panning to an image, resulting in a video or animated image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_zoompan\"\n    })\n  ).optional()\n};\nvar zoompanPlugin = {\n  props: zoompanProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { zoompan = false } = options;\n    const overrides = {\n      format: void 0\n    };\n    if (zoompan === true) {\n      cldAsset.effect(\"e_zoompan\");\n    } else if (typeof zoompan === \"string\") {\n      if (zoompan === \"loop\") {\n        cldAsset.effect(\"e_zoompan\");\n        cldAsset.effect(\"e_loop\");\n      } else {\n        cldAsset.effect(`e_zoompan:${zoompan}`);\n      }\n    } else if (typeof zoompan === \"object\") {\n      let zoompanEffect = \"e_zoompan\";\n      if (typeof zoompan.options === \"string\") {\n        zoompanEffect = `${zoompanEffect}:${zoompan.options}`;\n      }\n      cldAsset.effect(zoompanEffect);\n      let loopEffect;\n      if (zoompan.loop === true) {\n        loopEffect = \"e_loop\";\n      } else if (typeof zoompan.loop === \"string\" || typeof zoompan.loop === \"number\") {\n        loopEffect = `e_loop:${zoompan.loop}`;\n      }\n      if (loopEffect) {\n        cldAsset.effect(loopEffect);\n      }\n    }\n    if (zoompan !== false) {\n      overrides.format = \"auto:animated\";\n    }\n    return {\n      options: overrides\n    };\n  }\n};\n\n// src/types/image.ts\nvar imageOptionsSchema = assetOptionsSchema.merge(\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Spreading plugins instead of extend or merge to avoid excessive schema warning\n    // https://github.com/microsoft/TypeScript/issues/34933#issuecomment-**********\n    ...defaultImageProps,\n    ...enhanceProps,\n    ...extractProps,\n    ...fillBackgroundProps,\n    ...recolorProps,\n    ...removeProps,\n    ...replaceProps,\n    ...replaceBackgroundProps,\n    ...restoreProps,\n    ...zoompanProps\n  })\n);\n\n// src/types/video.ts\n\n\n// src/plugins/abr.ts\n\nvar abrProps = {\n  streamingProfile: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(\n    JSON.stringify({\n      text: \"The streaming profile to apply when delivering a video using adaptive bitrate streaming.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#sp_streaming_profile\"\n    })\n  ).optional()\n};\nvar abrPlugin = {\n  props: abrProps,\n  assetTypes: [\"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { streamingProfile } = options;\n    if (typeof streamingProfile === \"string\") {\n      cldAsset.addTransformation(`sp_${streamingProfile}`);\n    }\n    return {};\n  }\n};\n\n// src/types/video.ts\nvar videoOptionsSchema = assetOptionsSchema.merge(\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Spreading plugins instead of extend or merge to avoid excessive schema warning\n    // https://github.com/microsoft/TypeScript/issues/34933#issuecomment-**********\n    ...abrProps\n  })\n);\n\n// src/lib/cloudinary.ts\n\n\n\n\n// src/types/analytics.ts\n\nvar analyticsOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.any();\n\n// src/types/config.ts\n\nvar configOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.any();\n\n// src/lib/cloudinary.ts\nvar transformationPlugins = [\n  // Some features *must* be the first transformation applied\n  // thus their plugins *must* come first in the chain\n  enhancePlugin,\n  extractPlugin,\n  recolorPlugin,\n  removeBackgroundPlugin,\n  removePlugin,\n  replacePlugin,\n  replaceBackgroundPlugin,\n  restorePlugin,\n  // Cropping needs to be before any other general transformations\n  // as it provides the option of 2-step resizing where someone\n  // can resize the \"base\" canvas as well as the final resize\n  // mechanism commonly used for responsive resizing\n  croppingPlugin,\n  // Raw transformations should always come before\n  // other arguments to avoid conflicting with\n  // added options via the component\n  preserveTransformationsPlugin,\n  rawTransformationsPlugin,\n  abrPlugin,\n  defaultImagePlugin,\n  effectsPlugin,\n  fillBackgroundPlugin,\n  flagsPlugin,\n  overlaysPlugin,\n  sanitizePlugin,\n  namedTransformationsPlugin,\n  seoPlugin,\n  underlaysPlugin,\n  versionPlugin,\n  zoompanPlugin\n];\nvar constructUrlOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([imageOptionsSchema, videoOptionsSchema]).describe(\n  JSON.stringify({\n    text: \"Asset options (Image or Video) that define delivery URL including public ID and transformations.\",\n    path: \"/url-loader/assetoptions\"\n  })\n);\nvar constructUrlPropsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  analytics: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([analyticsOptionsSchema, zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()]).describe(\n    JSON.stringify({\n      text: \"Tech, dependency, and feature identifiers for tracking SDK usage related to Cloudinary.\",\n      path: \"/url-loader/analyticsoptions\"\n    })\n  ).optional(),\n  config: configOptionsSchema.describe(\n    JSON.stringify({\n      text: \"Configuration parameters for environment and Cloudinary account.\",\n      url: \"https://cloudinary.com/documentation/cloudinary_sdks#configuration_parameters\",\n      path: \"/url-loader/analyticsoptions\"\n    })\n  ).optional(),\n  options: constructUrlOptionsSchema\n});\nfunction constructCloudinaryUrl({\n  options,\n  config = {},\n  analytics\n}) {\n  if (analytics === false) {\n    if (typeof config?.url === \"undefined\") {\n      config.url = {};\n    }\n    config.url.analytics = false;\n  }\n  const cld = new _cloudinary_url_gen__WEBPACK_IMPORTED_MODULE_2__.Cloudinary(config);\n  if (typeof options?.src !== \"string\") {\n    throw Error(\n      `Failed to construct Cloudinary URL: Missing source (src) in options.`\n    );\n  }\n  if (!options?.assetType) {\n    options.assetType = \"image\";\n  }\n  const propsCheck = [];\n  transformationPlugins.forEach(({ props }) => {\n    const pluginProps = Object.keys(props);\n    pluginProps.forEach((prop) => {\n      if (propsCheck.includes(prop)) {\n        throw new Error(`Option ${prop} already exists!`);\n      }\n      propsCheck.push(prop);\n    });\n  });\n  const parsedOptions = {};\n  let publicId;\n  if (typeof options.src === \"string\" && /^https?:\\/\\//.test(options.src)) {\n    try {\n      const parts = (0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.parseUrl)(options.src);\n      publicId = parts?.publicId;\n      parsedOptions.seoSuffix = parts?.seoSuffix;\n      parsedOptions.version = parts?.version;\n    } catch (e) {\n    }\n  }\n  if (!publicId) {\n    publicId = options.src;\n  }\n  Object.keys(parsedOptions).forEach(\n    (key) => {\n      if ((0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_1__.objectHasKey)(options, key)) return;\n      options[key] = parsedOptions[key];\n    }\n  );\n  options.version ?? (options.version = 1);\n  let cldAsset = void 0;\n  if ([\"image\", \"images\"].includes(options.assetType)) {\n    cldAsset = cld.image(publicId);\n  } else if ([\"video\", \"videos\"].includes(options.assetType)) {\n    cldAsset = cld.video(publicId);\n  }\n  if (typeof cldAsset === \"undefined\") {\n    throw new Error(\"Invalid asset type.\");\n  }\n  const pluginEffects = {};\n  transformationPlugins.forEach(\n    ({ plugin, assetTypes, props, strict }) => {\n      const supportedAssetType = options?.assetType !== void 0 && assetTypes.includes(options.assetType);\n      const pluginProps = Object.keys(props);\n      const optionsKeys = Object.keys(options);\n      const attemptedUse = pluginProps.map((prop) => optionsKeys.includes(prop)).filter((isUsed) => !!isUsed).length > 0;\n      if (!supportedAssetType) {\n        if (attemptedUse) {\n          console.warn(\n            `One of the following props [${pluginProps.join(\n              \", \"\n            )}] was used with an unsupported asset type [${options?.assetType}]`\n          );\n        }\n        return;\n      }\n      if (options.strictTransformations && !strict) {\n        if (attemptedUse) {\n          console.warn(\n            `One of the following props [${pluginProps.join(\n              \", \"\n            )}] was used that is not supported with Strict Transformations.`\n          );\n        }\n        return;\n      }\n      const results = plugin({\n        cldAsset,\n        options\n      });\n      const { options: pluginOptions } = results || { options: void 0 };\n      Object.assign(pluginEffects, pluginOptions);\n    }\n  );\n  if (typeof pluginEffects.resize === \"string\") {\n    cldAsset.addTransformation(pluginEffects.resize);\n  }\n  cldAsset.setDeliveryType(options?.deliveryType || \"upload\");\n  if (!options.strictTransformations) {\n    if (options?.dpr) {\n      let dpr = options.dpr;\n      if (typeof dpr === \"number\") {\n        dpr = dpr.toFixed(1);\n      }\n      cldAsset.addTransformation(`dpr_${dpr}`);\n    }\n    const defaultFormat = options?.format === \"default\";\n    const rawContainsFormat = searchAssetRawTransformations(\"f_\", cldAsset, {\n      matchType: \"startsWith\"\n    });\n    const rawContainsFormatAndExplicit = rawContainsFormat && typeof options?.format !== \"undefined\";\n    if (pluginEffects?.format || !defaultFormat && (!rawContainsFormat || rawContainsFormatAndExplicit)) {\n      cldAsset.format(options?.format || pluginEffects?.format || \"auto\");\n    }\n    const defaultQuality = options?.quality === \"default\";\n    const rawContainsQuality = searchAssetRawTransformations(\"q_\", cldAsset, {\n      matchType: \"startsWith\"\n    });\n    const rawContainsQualityAndExplicit = rawContainsQuality && typeof options?.quality !== \"undefined\";\n    if (!defaultQuality && (!rawContainsQuality || rawContainsQualityAndExplicit)) {\n      cldAsset.quality(options?.quality || \"auto\");\n    }\n  }\n  return cldAsset.toURL({\n    trackedAnalytics: analytics\n  });\n}\nfunction searchAssetRawTransformations(query, asset, options) {\n  if (typeof asset.transformation === \"undefined\") return;\n  const { matchType = \"includes\" } = options || {};\n  const transformations = asset.transformation.actions.flatMap(\n    (transformation) => {\n      return transformation.toString().split(\"/\").flatMap((seg) => seg.split(\",\"));\n    }\n  );\n  const matches = transformations.filter((transformation) => {\n    if (matchType === \"startsWith\") {\n      return transformation.startsWith(query);\n    } else {\n      return transformation.includes(query);\n    }\n  });\n  return matches.length > 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary-util/url-loader/dist/chunk-L3YIXMGG.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary-util/url-loader/dist/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/@cloudinary-util/url-loader/dist/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UPLOAD_WIDGET_EVENTS: () => (/* binding */ UPLOAD_WIDGET_EVENTS),\n/* harmony export */   constructCloudinaryUrl: () => (/* reexport safe */ _chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.constructCloudinaryUrl),\n/* harmony export */   effects: () => (/* reexport safe */ _chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.effects),\n/* harmony export */   generateSignatureCallback: () => (/* binding */ generateSignatureCallback),\n/* harmony export */   generateUploadWidgetResultCallback: () => (/* binding */ generateUploadWidgetResultCallback),\n/* harmony export */   getUploadWidgetOptions: () => (/* binding */ getUploadWidgetOptions),\n/* harmony export */   getVideoPlayerOptions: () => (/* binding */ getVideoPlayerOptions),\n/* harmony export */   position: () => (/* reexport safe */ _chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.position),\n/* harmony export */   primary: () => (/* reexport safe */ _chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.primary),\n/* harmony export */   text: () => (/* reexport safe */ _chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.text),\n/* harmony export */   transformationPlugins: () => (/* reexport safe */ _chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.transformationPlugins)\n/* harmony export */ });\n/* harmony import */ var _chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-L3YIXMGG.js */ \"(ssr)/./node_modules/@cloudinary-util/url-loader/dist/chunk-L3YIXMGG.js\");\n/* harmony import */ var _cloudinary_util_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @cloudinary-util/util */ \"(ssr)/./node_modules/@cloudinary-util/url-loader/node_modules/@cloudinary-util/util/dist/index.js\");\n\n\n// src/lib/upload-widget.ts\nfunction getUploadWidgetOptions({ uploadSignature, ...options }, config) {\n  const signed = typeof uploadSignature === \"function\";\n  const { cloudName, apiKey } = config?.cloud || {};\n  if (!cloudName) {\n    throw new Error(\"A Cloudinary Cloud name is required, please make sure your environment variable is set and configured in your environment.\");\n  }\n  if (signed && !apiKey) {\n    throw new Error(\"A Cloudinary API Key is required for signed requests, please make sure your environment variable is set and configured in your environment.\");\n  }\n  if (!signed && !options.uploadPreset) {\n    throw new Error(\"A Cloudinary Upload Preset is required for unsigned uploads. Please specify an uploadPreset or configure signed uploads.\");\n  }\n  const uploadOptions = {\n    cloudName,\n    apiKey,\n    ...options\n  };\n  if (signed) {\n    uploadOptions.uploadSignature = uploadSignature;\n  }\n  return uploadOptions;\n}\nvar UPLOAD_WIDGET_EVENTS = {\n  \"abort\": \"onAbort\",\n  \"batch-cancelled\": \"onBatchCancelled\",\n  \"close\": \"onClose\",\n  \"display-changed\": \"onDisplayChanged\",\n  \"publicid\": \"onPublicId\",\n  \"queues-end\": \"onQueuesEnd\",\n  \"queues-start\": \"onQueuesStart\",\n  \"retry\": \"onRetry\",\n  \"show-completed\": \"onShowCompleted\",\n  \"source-changed\": \"onSourceChanged\",\n  \"success\": \"onSuccess\",\n  \"tags\": \"onTags\",\n  \"upload-added\": \"onUploadAdded\"\n};\nfunction generateUploadWidgetResultCallback(options) {\n  return function resultCallback(error, uploadResult) {\n    if (error) {\n      if (typeof options.onError === \"function\") {\n        options.onError(error, uploadResult);\n      }\n    }\n    if (typeof options.onResult === \"function\") {\n      options.onResult(uploadResult);\n    }\n    const widgetEvent = typeof uploadResult?.event === \"string\" && UPLOAD_WIDGET_EVENTS[uploadResult.event];\n    if (typeof widgetEvent === \"string\" && typeof options[widgetEvent] === \"function\") {\n      const callback = options[widgetEvent];\n      callback(uploadResult);\n    }\n  };\n}\n\n// src/lib/upload.ts\nfunction generateSignatureCallback({ signatureEndpoint, fetch: fetcher }) {\n  return function generateSignature(callback, paramsToSign) {\n    if (typeof signatureEndpoint === \"undefined\") {\n      throw Error(\"Failed to generate signature: signatureEndpoint property undefined.\");\n    }\n    if (typeof fetcher === \"undefined\") {\n      throw Error(\"Failed to generate signature: fetch property undefined.\");\n    }\n    fetcher(signatureEndpoint, {\n      method: \"POST\",\n      body: JSON.stringify({\n        paramsToSign\n      }),\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    }).then((response) => response.json()).then((result) => {\n      callback(result.signature);\n    }).catch((error) => {\n      callback(null, error);\n    });\n  };\n}\n\n// src/lib/video-player.ts\n\nfunction getVideoPlayerOptions(options, config) {\n  const {\n    autoplay,\n    controls = true,\n    language,\n    languages,\n    logo = true,\n    loop = false,\n    muted = false,\n    poster,\n    src,\n    transformation,\n    quality = \"auto\",\n    ...otherCldVidPlayerOptions\n  } = options;\n  const { cloudName } = config?.cloud || {};\n  const { secureDistribution, privateCdn } = config?.url || {};\n  if (!cloudName) {\n    throw new Error(\n      \"A Cloudinary Cloud name is required, please make sure your environment variable is set and configured in your environment.\"\n    );\n  }\n  let publicId = src || \"\";\n  if (publicId.startsWith(\"http\")) {\n    try {\n      const parts = (0,_cloudinary_util_util__WEBPACK_IMPORTED_MODULE_0__.parseUrl)(src);\n      if (typeof parts?.publicId === \"string\") {\n        publicId = parts?.publicId;\n      }\n    } catch (e) {\n    }\n  }\n  if (!publicId) {\n    throw new Error(\n      \"Video Player requires a src, please make sure to configure your src as a public ID or Cloudinary URL.\"\n    );\n  }\n  const playerTransformations = Array.isArray(transformation) ? transformation : [transformation];\n  playerTransformations.unshift({\n    quality\n  });\n  let logoOptions = {};\n  if (typeof logo === \"boolean\") {\n    logoOptions.showLogo = logo;\n  } else if (typeof logo === \"object\") {\n    logoOptions = {\n      ...logoOptions,\n      showLogo: true,\n      logoImageUrl: logo.imageUrl,\n      logoOnclickUrl: logo.onClickUrl\n    };\n  }\n  let autoplayValue = false;\n  let autoplayModeValue = void 0;\n  if (typeof autoplay === \"boolean\" || autoplay === \"true\" || autoplay === \"false\") {\n    autoplayValue = autoplay;\n  }\n  if (typeof autoplay === \"string\" && autoplay !== \"true\" && autoplay !== \"false\") {\n    autoplayModeValue = autoplay;\n  }\n  const playerOptions = {\n    cloud_name: cloudName,\n    privateCdn,\n    secureDistribution,\n    autoplayMode: autoplayModeValue,\n    autoplay: autoplayValue,\n    controls,\n    language,\n    languages,\n    loop,\n    muted,\n    publicId,\n    transformation: playerTransformations,\n    ...logoOptions,\n    ...otherCldVidPlayerOptions\n  };\n  if (playerOptions.width && playerOptions.height && !playerOptions.aspectRatio) {\n    playerOptions.aspectRatio = `${playerOptions.width}:${playerOptions.height}`;\n  }\n  if (typeof poster === \"string\") {\n    playerOptions.posterOptions = {\n      publicId: poster\n    };\n  } else if (typeof poster === \"object\") {\n    if (typeof poster.src !== \"string\") {\n      playerOptions.posterOptions = {\n        publicId: (0,_chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.constructCloudinaryUrl)({\n          options: {\n            ...poster,\n            src: publicId,\n            assetType: \"video\",\n            format: \"auto:image\"\n          },\n          config\n        })\n      };\n    } else {\n      playerOptions.posterOptions = {\n        publicId: (0,_chunk_L3YIXMGG_js__WEBPACK_IMPORTED_MODULE_1__.constructCloudinaryUrl)({\n          options: poster,\n          config\n        })\n      };\n    }\n  }\n  return playerOptions;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary-util/url-loader/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary-util/url-loader/node_modules/@cloudinary-util/util/dist/index.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@cloudinary-util/url-loader/node_modules/@cloudinary-util/util/dist/index.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertColorHexToRgb: () => (/* binding */ convertColorHexToRgb),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64),\n/* harmony export */   getFormat: () => (/* binding */ getFormat),\n/* harmony export */   getPublicId: () => (/* binding */ getPublicId),\n/* harmony export */   getTransformations: () => (/* binding */ getTransformations),\n/* harmony export */   objectHasKey: () => (/* binding */ objectHasKey),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   pollForProcessingImage: () => (/* binding */ pollForProcessingImage),\n/* harmony export */   sortByKey: () => (/* binding */ sortByKey),\n/* harmony export */   testColorIsHex: () => (/* binding */ testColorIsHex)\n/* harmony export */ });\n// src/lib/cloudinary.ts\nvar REGEX_VERSION = /\\/v\\d+\\//;\nvar REGEX_FORMAT = /\\.(ai|avif|gif|png|webp|bmp|bw|djvu|dng|ps|ept|eps|eps3|fbx|flif|gif|glb|gltf|heif|heic|ico|indd|jpg|jpe|jpeg|jp2|wdp|jxr|hdp|obj|pdf|ply|png|psd|arw|cr2|svg|tga|tif|tiff|u3ma|usdz|webp|3g2|3gp|avi|flv|m3u8|ts|m2ts|mts|mov|mkv|mp4|mpeg|mpd|mxf|ogv|webm|wmv)$/i;\nvar REGEX_URL = /https?:\\/\\/(?<host>[^/]+)\\/(?<cloudName>[^/]+)?\\/?(?<assetType>image|images|video|videos|raw|files)\\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)?\\/?(?<signature>s--([a-zA-Z0-9_-]{8}|[a-zA-Z0-9_-]{32})--)?\\/?(?<transformations>(?:[^_/]+_[^,/]+,?\\/?)*\\/)*(?<version>v\\d+|\\w{1,2})\\/(?<publicId>[^\\s]+)$/;\nvar ASSET_TYPES_SEO = [\"images\", \"videos\", \"files\"];\nvar CLOUDINARY_DEFAULT_HOST = \"res.cloudinary.com\";\nfunction parseUrl(src) {\n  if (typeof src !== \"string\") {\n    throw new Error(`Failed to parse URL - Invalid src: Is not a string`);\n  }\n  const hasVersion = REGEX_VERSION.test(src);\n  if (!hasVersion) {\n    throw new Error(\n      `Failed to parse URL - Invalid src: Does not include version (Ex: /v1234/)`\n    );\n  }\n  const [baseUrlWithExtension, queryString] = src.split(\"?\");\n  const format = getFormat(baseUrlWithExtension);\n  let baseUrl = baseUrlWithExtension;\n  if (format) {\n    baseUrl = baseUrlWithExtension.replace(new RegExp(`${format}$`), \"\");\n  }\n  const results = baseUrl.match(REGEX_URL);\n  const transformations = results?.groups?.transformations?.split(\"/\").filter((t) => !!t);\n  const parts = {\n    ...results?.groups,\n    format,\n    seoSuffix: void 0,\n    transformations: transformations || [],\n    queryParams: {},\n    version: results?.groups?.version ? parseInt(results.groups.version.replace(\"v\", \"\")) : void 0\n  };\n  if (parts.host === CLOUDINARY_DEFAULT_HOST && !parts.cloudName) {\n    throw new Error(\n      \"Failed to parse URL - Invalid src: Cloudinary URL delivered from res.cloudinary.com must include Cloud Name (ex: res.cloudinary.com/<Cloud Name>/image/...)\"\n    );\n  }\n  if (queryString) {\n    parts.queryParams = queryString.split(\"&\").reduce((prev, curr) => {\n      const [key, value] = curr.split(\"=\");\n      prev[key] = value;\n      return prev;\n    }, {});\n  }\n  if (parts.assetType && ASSET_TYPES_SEO.includes(parts.assetType)) {\n    const publicIdParts = parts.publicId?.split(\"/\") || [];\n    parts.seoSuffix = publicIdParts.pop();\n    parts.publicId = publicIdParts.join(\"/\");\n  }\n  if (parts.publicId) {\n    parts.publicId = decodeURIComponent(parts.publicId);\n  }\n  return parts;\n}\nfunction getPublicId(src) {\n  const { publicId } = parseUrl(src) || {};\n  return publicId;\n}\nfunction getTransformations(src) {\n  const { transformations = [] } = parseUrl(src) || {};\n  return transformations.map((t) => t.split(\",\"));\n}\nfunction getFormat(src) {\n  const matches = src.match(REGEX_FORMAT);\n  if (matches === null) return;\n  return matches[0];\n}\nasync function pollForProcessingImage(options) {\n  try {\n    const response = await fetch(options.src);\n    if (response.status === 423) {\n      await new Promise((resolve) => setTimeout(resolve, 500));\n      return await pollForProcessingImage(options);\n    }\n    return response.ok;\n  } catch {\n    return false;\n  }\n}\n\n// src/lib/colors.ts\nfunction testColorIsHex(value) {\n  if (typeof value !== \"string\") return false;\n  return !!value.startsWith(\"#\");\n}\nfunction convertColorHexToRgb(value) {\n  return `rgb:${value.replace(\"#\", \"\")}`;\n}\n\n// src/lib/util.ts\nfunction encodeBase64(value) {\n  if (typeof btoa === \"function\") {\n    return btoa(value);\n  }\n  if (typeof Buffer !== \"undefined\") {\n    return Buffer.from(value).toString(\"base64\");\n  }\n}\nfunction objectHasKey(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction sortByKey(array = [], key, type = \"asc\") {\n  function compare(a, b) {\n    let keyA = a[key];\n    let keyB = b[key];\n    if (typeof keyA === \"string\") {\n      keyA = keyA.toLowerCase();\n    }\n    if (typeof keyB === \"string\") {\n      keyB = keyB.toLowerCase();\n    }\n    if (keyA < keyB) {\n      return -1;\n    }\n    if (keyA > keyB) {\n      return 1;\n    }\n    return 0;\n  }\n  let newArray = [...array];\n  if (typeof key !== \"string\") return newArray;\n  newArray = newArray.sort(compare);\n  if (type === \"desc\") {\n    return newArray.reverse();\n  }\n  return newArray;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary-util/url-loader/node_modules/@cloudinary-util/util/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@cloudinary-util/util/dist/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@cloudinary-util/util/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertColorHexToRgb: () => (/* binding */ convertColorHexToRgb),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64),\n/* harmony export */   getFormat: () => (/* binding */ getFormat),\n/* harmony export */   getPublicId: () => (/* binding */ getPublicId),\n/* harmony export */   getTransformations: () => (/* binding */ getTransformations),\n/* harmony export */   objectHasKey: () => (/* binding */ objectHasKey),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   pollForProcessingImage: () => (/* binding */ pollForProcessingImage),\n/* harmony export */   sortByKey: () => (/* binding */ sortByKey),\n/* harmony export */   testColorIsHex: () => (/* binding */ testColorIsHex)\n/* harmony export */ });\n// src/lib/cloudinary.ts\nvar REGEX_VERSION = /\\/v\\d+\\//;\nvar REGEX_FORMAT = /\\.(ai|avif|gif|png|webp|bmp|bw|djvu|dng|ps|ept|eps|eps3|fbx|flif|gif|glb|gltf|heif|heic|ico|indd|jpg|jpe|jpeg|jp2|wdp|jxr|hdp|obj|pdf|ply|png|psd|arw|cr2|svg|tga|tif|tiff|u3ma|usdz|webp|3g2|3gp|avi|flv|m3u8|ts|m2ts|mts|mov|mkv|mp4|mpeg|mpd|mxf|ogv|webm|wmv)$/i;\nvar REGEX_URL = /https?:\\/\\/(?<host>[^/]+)\\/(?<cloudName>[^/]+)?\\/?(?<assetType>image|images|video|videos|raw|files)\\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)?\\/?(?<signature>s--([a-zA-Z0-9_-]{8}|[a-zA-Z0-9_-]{32})--)?\\/?(?<transformations>(?:[^_/]+_[^,/]+,?\\/?)*\\/)*(?<version>v\\d+|\\w{1,2})\\/(?<publicId>[^\\s]+)$/;\nvar ASSET_TYPES_SEO = [\"images\", \"videos\", \"files\"];\nvar CLOUDINARY_DEFAULT_HOST = \"res.cloudinary.com\";\nfunction parseUrl(src) {\n  if (typeof src !== \"string\") {\n    throw new Error(`Failed to parse URL - Invalid src: Is not a string`);\n  }\n  const hasVersion = REGEX_VERSION.test(src);\n  if (!hasVersion) {\n    throw new Error(\n      `Failed to parse URL - Invalid src: Does not include version (Ex: /v1234/)`\n    );\n  }\n  const [baseUrlWithExtension, queryString] = src.split(\"?\");\n  const format = getFormat(baseUrlWithExtension);\n  let baseUrl = baseUrlWithExtension;\n  if (format) {\n    baseUrl = baseUrlWithExtension.replace(new RegExp(`${format}$`), \"\");\n  }\n  const results = baseUrl.match(REGEX_URL);\n  const transformations = results?.groups?.transformations?.split(\"/\").filter((t) => !!t);\n  const parts = {\n    ...results?.groups,\n    format,\n    seoSuffix: void 0,\n    transformations: transformations || [],\n    queryParams: {},\n    version: results?.groups?.version ? parseInt(results.groups.version.replace(\"v\", \"\")) : void 0\n  };\n  if (parts.host === CLOUDINARY_DEFAULT_HOST && !parts.cloudName) {\n    throw new Error(\n      \"Failed to parse URL - Invalid src: Cloudinary URL delivered from res.cloudinary.com must include Cloud Name (ex: res.cloudinary.com/<Cloud Name>/image/...)\"\n    );\n  }\n  if (queryString) {\n    parts.queryParams = queryString.split(\"&\").reduce((prev, curr) => {\n      const [key, value] = curr.split(\"=\");\n      prev[key] = value;\n      return prev;\n    }, {});\n  }\n  if (parts.assetType && ASSET_TYPES_SEO.includes(parts.assetType)) {\n    const publicIdParts = parts.publicId?.split(\"/\") || [];\n    parts.seoSuffix = publicIdParts.pop();\n    parts.publicId = publicIdParts.join(\"/\");\n  }\n  if (parts.publicId) {\n    parts.publicId = decodeURIComponent(parts.publicId);\n  }\n  return parts;\n}\nfunction getPublicId(src) {\n  const { publicId } = parseUrl(src) || {};\n  return publicId;\n}\nfunction getTransformations(src) {\n  const { transformations = [] } = parseUrl(src) || {};\n  return transformations.map((t) => t.split(\",\"));\n}\nfunction getFormat(src) {\n  const matches = src.match(REGEX_FORMAT);\n  if (matches === null) return;\n  return matches[0];\n}\nasync function pollForProcessingImage(options) {\n  try {\n    const response = await fetch(options.src);\n    if (response.status === 423) {\n      await new Promise((resolve) => setTimeout(resolve, 500));\n      return await pollForProcessingImage(options);\n    }\n    if (!response.ok) {\n      return {\n        success: false,\n        status: response.status,\n        error: response.headers.get(\"x-cld-error\") || \"Unknown error\"\n      };\n    }\n    return {\n      success: true,\n      status: response.status\n    };\n  } catch (error) {\n    return {\n      success: false,\n      status: 500,\n      error: error.message || \"Network error\"\n    };\n  }\n}\n\n// src/lib/colors.ts\nfunction testColorIsHex(value) {\n  if (typeof value !== \"string\") return false;\n  return !!value.startsWith(\"#\");\n}\nfunction convertColorHexToRgb(value) {\n  return `rgb:${value.replace(\"#\", \"\")}`;\n}\n\n// src/lib/util.ts\nfunction encodeBase64(value) {\n  if (typeof btoa === \"function\") {\n    return btoa(value);\n  }\n  if (typeof Buffer !== \"undefined\") {\n    return Buffer.from(value).toString(\"base64\");\n  }\n}\nfunction objectHasKey(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction sortByKey(array = [], key, type = \"asc\") {\n  function compare(a, b) {\n    let keyA = a[key];\n    let keyB = b[key];\n    if (typeof keyA === \"string\") {\n      keyA = keyA.toLowerCase();\n    }\n    if (typeof keyB === \"string\") {\n      keyB = keyB.toLowerCase();\n    }\n    if (keyA < keyB) return -1;\n    if (keyA > keyB) return 1;\n    return 0;\n  }\n  let newArray = [...array];\n  if (typeof key !== \"string\") return newArray;\n  newArray = newArray.sort(compare);\n  if (type === \"desc\") {\n    return newArray.reverse();\n  }\n  return newArray;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@cloudinary-util/util/dist/index.js\n");

/***/ })

};
;