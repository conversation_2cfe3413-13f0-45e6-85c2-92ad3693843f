import { Flag, animated, anyFormat, animatedPng, animatedWebP, clipEvenOdd, lossy, preserveTransparency, png8, png24, png32, progressive, rasterize, sanitize, stripProfile, tiff8Lzw, attachment, forceIcc, forceStrip, getInfo, immutableCache, keepAttribution, keepIptc, custom, streamingAttachment, hlsv3, keepDar, noStream, mono, layerApply, relative, regionRelative, splice, truncateTS, waveform, ignoreInitialAspectRatio, clip, tiled, noOverflow, ignoreMaskChannels } from "@cloudinary/transformation-builder-sdk/qualifiers/flag";
export { Flag, animated, anyFormat, animatedPng, animatedWebP, clipEvenOdd, lossy, preserveTransparency, png8, png24, png32, progressive, rasterize, sanitize, stripProfile, tiff8Lzw, attachment, forceIcc, forceStrip, getInfo, immutableCache, keepAttribution, keepIptc, custom, streamingAttachment, hlsv3, keepDar, noStream, mono, layerApply, relative, regionRelative, splice, truncateTS, waveform, ignoreInitialAspectRatio, clip, tiled, noOverflow, ignoreMaskChannels };
