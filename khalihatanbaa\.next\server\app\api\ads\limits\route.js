/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/limits/route";
exports.ids = ["app/api/ads/limits/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Flimits%2Froute&page=%2Fapi%2Fads%2Flimits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Flimits%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Flimits%2Froute&page=%2Fapi%2Fads%2Flimits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Flimits%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ads_limits_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ads/limits/route.ts */ \"(rsc)/./src/app/api/ads/limits/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/limits/route\",\n        pathname: \"/api/ads/limits\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/limits/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\api\\\\ads\\\\limits\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ads_limits_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Flimits%2Froute&page=%2Fapi%2Fads%2Flimits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Flimits%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ads/limits/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/ads/limits/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n// جلب حدود الإعلانات للمستخدم الحالي\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"يجب تسجيل الدخول أولاً\"\n            }, {\n                status: 401\n            });\n        }\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: session.user.id\n            },\n            select: {\n                freeAdsCount: true,\n                freeAdsExpiresAt: true,\n                paidAdsCount: true,\n                paidAdsExpiresAt: true,\n                _count: {\n                    select: {\n                        ads: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"المستخدم غير موجود\"\n            }, {\n                status: 404\n            });\n        }\n        // حساب الإحصائيات\n        const totalActiveAds = user._count.ads;\n        // حساب إجمالي المشاهدات من الإعلانات\n        const adStats = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.ad.aggregate({\n            where: {\n                userId: session.user.id,\n                isActive: true\n            },\n            _sum: {\n                views: true\n            }\n        });\n        const totalViews = adStats._sum.views || 0;\n        const totalContacts = 0; // يمكن حسابه من الرسائل لاحقاً\n        // حساب الإعلانات المتاحة\n        const now = new Date();\n        // التحقق من انتهاء صلاحية الإعلانات المجانية\n        const freeAdsUsed = totalActiveAds; // عدد الإعلانات النشطة\n        const freeAdsAvailable = user.freeAdsExpiresAt && user.freeAdsExpiresAt < now ? 0 : Math.max(0, user.freeAdsCount - freeAdsUsed);\n        // التحقق من انتهاء صلاحية الإعلانات المدفوعة\n        const paidAdsUsed = 0; // لا يوجد نظام دفع حالياً\n        const paidAdsAvailable = user.paidAdsExpiresAt && user.paidAdsExpiresAt < now ? 0 : Math.max(0, user.paidAdsCount - paidAdsUsed);\n        // الإعلانات المميزة (متاحة دائماً للاختبار)\n        const promotedAdsAvailable = 10; // عدد ثابت للاختبار\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                freeAds: {\n                    total: user.freeAdsCount,\n                    used: freeAdsUsed,\n                    available: freeAdsAvailable,\n                    expiresAt: user.freeAdsExpiresAt\n                },\n                paidAds: {\n                    total: user.paidAdsCount,\n                    used: paidAdsUsed,\n                    available: paidAdsAvailable,\n                    expiresAt: user.paidAdsExpiresAt\n                },\n                promotedAds: {\n                    total: 10,\n                    used: 0,\n                    available: promotedAdsAvailable,\n                    expiresAt: null\n                },\n                totalActiveAds: totalActiveAds,\n                totalViews: totalViews,\n                totalContacts: totalContacts,\n                canCreateFreeAd: freeAdsAvailable > 0,\n                canCreatePaidAd: paidAdsAvailable > 0,\n                canCreatePromotedAd: promotedAdsAvailable > 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching ad limits:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"حدث خطأ في جلب حدود الإعلانات\"\n        }, {\n            status: 500\n        });\n    }\n}\n// تحديث حدود الإعلانات (للإدارة)\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"يجب تسجيل الدخول أولاً\"\n            }, {\n                status: 401\n            });\n        }\n        // التحقق من صلاحيات الإدارة\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: session.user.id\n            },\n            select: {\n                role: true\n            }\n        });\n        if (user?.role !== \"admin\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"غير مسموح لك بهذا الإجراء\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { userId, freeAdsCount, paidAdsCount, promotedAdsCount, freeAdsDuration, paidAdsDuration, promotedAdsDuration } = body;\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"معرف المستخدم مطلوب\"\n            }, {\n                status: 400\n            });\n        }\n        const now = new Date();\n        const updateData = {};\n        if (freeAdsCount !== undefined) {\n            updateData.freeAdsCount = freeAdsCount;\n            if (freeAdsDuration) {\n                updateData.freeAdsExpiresAt = new Date(now.getTime() + freeAdsDuration * 24 * 60 * 60 * 1000);\n            }\n        }\n        if (paidAdsCount !== undefined) {\n            updateData.paidAdsCount = paidAdsCount;\n            if (paidAdsDuration) {\n                updateData.paidAdsExpiresAt = new Date(now.getTime() + paidAdsDuration * 24 * 60 * 60 * 1000);\n            }\n        }\n        if (promotedAdsCount !== undefined) {\n            updateData.promotedAdsCount = promotedAdsCount;\n            if (promotedAdsDuration) {\n                updateData.promotedAdsExpiresAt = new Date(now.getTime() + promotedAdsDuration * 24 * 60 * 60 * 1000);\n            }\n        }\n        const updatedUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: updateData,\n            select: {\n                id: true,\n                name: true,\n                email: true,\n                freeAdsCount: true,\n                freeAdsUsed: true,\n                freeAdsExpiresAt: true,\n                paidAdsCount: true,\n                paidAdsUsed: true,\n                paidAdsExpiresAt: true,\n                promotedAdsCount: true,\n                promotedAdsUsed: true,\n                promotedAdsExpiresAt: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedUser,\n            message: \"تم تحديث حدود الإعلانات بنجاح\"\n        });\n    } catch (error) {\n        console.error(\"Error updating ad limits:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"حدث خطأ في تحديث حدود الإعلانات\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ads/limits/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                emailOrPhone: {\n                    label: \"البريد الإلكتروني أو رقم الهاتف\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"كلمة المرور\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.emailOrPhone || !credentials?.password) {\n                    return null;\n                }\n                // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findFirst({\n                    where: {\n                        OR: [\n                            {\n                                email: credentials.emailOrPhone\n                            },\n                            {\n                                phone: credentials.emailOrPhone\n                            }\n                        ],\n                        isActive: true\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                // التحقق من كلمة المرور\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(credentials.password, user.passwordHash);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    name: user.name,\n                    email: user.email,\n                    phone: user.phone,\n                    role: user.role,\n                    avatar: user.avatar || undefined\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.phone = user.phone;\n                token.avatar = user.avatar;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.phone = token.phone;\n                session.user.avatar = token.avatar;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNrRTtBQUNwQztBQUNJO0FBRTNCLE1BQU1HLGNBQStCO0lBQzFDQyxXQUFXO1FBQ1RKLDJFQUFtQkEsQ0FBQztZQUNsQkssTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxjQUFjO29CQUNaQyxPQUFPO29CQUNQQyxNQUFNO2dCQUNSO2dCQUNBQyxVQUFVO29CQUFFRixPQUFPO29CQUFlQyxNQUFNO2dCQUFXO1lBQ3JEO1lBQ0EsTUFBTUUsV0FBVUwsV0FBVztnQkFDekIsSUFBSSxDQUFDQSxhQUFhQyxnQkFBZ0IsQ0FBQ0QsYUFBYUksVUFBVTtvQkFDeEQsT0FBTztnQkFDVDtnQkFFQSxxREFBcUQ7Z0JBQ3JELE1BQU1FLE9BQU8sTUFBTVYsMkNBQU1BLENBQUNVLElBQUksQ0FBQ0MsU0FBUyxDQUFDO29CQUN2Q0MsT0FBTzt3QkFDTEMsSUFBSTs0QkFDRjtnQ0FBRUMsT0FBT1YsWUFBWUMsWUFBWTs0QkFBQzs0QkFDbEM7Z0NBQUVVLE9BQU9YLFlBQVlDLFlBQVk7NEJBQUM7eUJBQ25DO3dCQUNEVyxVQUFVO29CQUNaO2dCQUNGO2dCQUVBLElBQUksQ0FBQ04sTUFBTTtvQkFDVCxPQUFPO2dCQUNUO2dCQUVBLHdCQUF3QjtnQkFDeEIsTUFBTU8sa0JBQWtCLE1BQU1sQix3REFBYyxDQUMxQ0ssWUFBWUksUUFBUSxFQUNwQkUsS0FBS1MsWUFBWTtnQkFHbkIsSUFBSSxDQUFDRixpQkFBaUI7b0JBQ3BCLE9BQU87Z0JBQ1Q7Z0JBRUEsT0FBTztvQkFDTEcsSUFBSVYsS0FBS1UsRUFBRTtvQkFDWGpCLE1BQU1PLEtBQUtQLElBQUk7b0JBQ2ZXLE9BQU9KLEtBQUtJLEtBQUs7b0JBQ2pCQyxPQUFPTCxLQUFLSyxLQUFLO29CQUNqQk0sTUFBTVgsS0FBS1csSUFBSTtvQkFDZkMsUUFBUVosS0FBS1ksTUFBTSxJQUFJQztnQkFDekI7WUFDRjtRQUNGO0tBQ0Q7SUFDREMsU0FBUztRQUNQQyxVQUFVO0lBQ1o7SUFDQUMsV0FBVztRQUNULE1BQU1DLEtBQUksRUFBRUMsS0FBSyxFQUFFbEIsSUFBSSxFQUFFO1lBQ3ZCLElBQUlBLE1BQU07Z0JBQ1JrQixNQUFNUCxJQUFJLEdBQUdYLEtBQUtXLElBQUk7Z0JBQ3RCTyxNQUFNYixLQUFLLEdBQUdMLEtBQUtLLEtBQUs7Z0JBQ3hCYSxNQUFNTixNQUFNLEdBQUdaLEtBQUtZLE1BQU07WUFDNUI7WUFDQSxPQUFPTTtRQUNUO1FBQ0EsTUFBTUosU0FBUSxFQUFFQSxPQUFPLEVBQUVJLEtBQUssRUFBRTtZQUM5QixJQUFJQSxPQUFPO2dCQUNUSixRQUFRZCxJQUFJLENBQUNVLEVBQUUsR0FBR1EsTUFBTUMsR0FBRztnQkFDM0JMLFFBQVFkLElBQUksQ0FBQ1csSUFBSSxHQUFHTyxNQUFNUCxJQUFJO2dCQUM5QkcsUUFBUWQsSUFBSSxDQUFDSyxLQUFLLEdBQUdhLE1BQU1iLEtBQUs7Z0JBQ2hDUyxRQUFRZCxJQUFJLENBQUNZLE1BQU0sR0FBR00sTUFBTU4sTUFBTTtZQUNwQztZQUNBLE9BQU9FO1FBQ1Q7SUFDRjtJQUNBTSxPQUFPO1FBQ0xDLFFBQVE7SUFFVjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcc3JjXFxsaWJcXGF1dGgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dEF1dGhPcHRpb25zIH0gZnJvbSBcIm5leHQtYXV0aFwiO1xuaW1wb3J0IENyZWRlbnRpYWxzUHJvdmlkZXIgZnJvbSBcIm5leHQtYXV0aC9wcm92aWRlcnMvY3JlZGVudGlhbHNcIjtcbmltcG9ydCBiY3J5cHQgZnJvbSBcImJjcnlwdGpzXCI7XG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tIFwiLi9wcmlzbWFcIjtcblxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XG4gIHByb3ZpZGVyczogW1xuICAgIENyZWRlbnRpYWxzUHJvdmlkZXIoe1xuICAgICAgbmFtZTogXCJjcmVkZW50aWFsc1wiLFxuICAgICAgY3JlZGVudGlhbHM6IHtcbiAgICAgICAgZW1haWxPclBob25lOiB7XG4gICAgICAgICAgbGFiZWw6IFwi2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKINij2Ygg2LHZgtmFINin2YTZh9in2KrZgVwiLFxuICAgICAgICAgIHR5cGU6IFwidGV4dFwiLFxuICAgICAgICB9LFxuICAgICAgICBwYXNzd29yZDogeyBsYWJlbDogXCLZg9mE2YXYqSDYp9mE2YXYsdmI2LFcIiwgdHlwZTogXCJwYXNzd29yZFwiIH0sXG4gICAgICB9LFxuICAgICAgYXN5bmMgYXV0aG9yaXplKGNyZWRlbnRpYWxzKSB7XG4gICAgICAgIGlmICghY3JlZGVudGlhbHM/LmVtYWlsT3JQaG9uZSB8fCAhY3JlZGVudGlhbHM/LnBhc3N3b3JkKSB7XG4gICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDYp9mE2KjYrdirINi52YYg2KfZhNmF2LPYqtiu2K/ZhSDYqNin2YTYqNix2YrYryDYp9mE2KXZhNmD2KrYsdmI2YbZiiDYo9mIINix2YLZhSDYp9mE2YfYp9iq2YFcbiAgICAgICAgY29uc3QgdXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRGaXJzdCh7XG4gICAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICAgIE9SOiBbXG4gICAgICAgICAgICAgIHsgZW1haWw6IGNyZWRlbnRpYWxzLmVtYWlsT3JQaG9uZSB9LFxuICAgICAgICAgICAgICB7IHBob25lOiBjcmVkZW50aWFscy5lbWFpbE9yUGhvbmUgfSxcbiAgICAgICAgICAgIF0sXG4gICAgICAgICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINmD2YTZhdipINin2YTZhdix2YjYsVxuICAgICAgICBjb25zdCBpc1Bhc3N3b3JkVmFsaWQgPSBhd2FpdCBiY3J5cHQuY29tcGFyZShcbiAgICAgICAgICBjcmVkZW50aWFscy5wYXNzd29yZCxcbiAgICAgICAgICB1c2VyLnBhc3N3b3JkSGFzaFxuICAgICAgICApO1xuXG4gICAgICAgIGlmICghaXNQYXNzd29yZFZhbGlkKSB7XG4gICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICAgIG5hbWU6IHVzZXIubmFtZSxcbiAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgICBwaG9uZTogdXNlci5waG9uZSxcbiAgICAgICAgICByb2xlOiB1c2VyLnJvbGUsXG4gICAgICAgICAgYXZhdGFyOiB1c2VyLmF2YXRhciB8fCB1bmRlZmluZWQsXG4gICAgICAgIH07XG4gICAgICB9LFxuICAgIH0pLFxuICBdLFxuICBzZXNzaW9uOiB7XG4gICAgc3RyYXRlZ3k6IFwiand0XCIsXG4gIH0sXG4gIGNhbGxiYWNrczoge1xuICAgIGFzeW5jIGp3dCh7IHRva2VuLCB1c2VyIH0pIHtcbiAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgIHRva2VuLnJvbGUgPSB1c2VyLnJvbGU7XG4gICAgICAgIHRva2VuLnBob25lID0gdXNlci5waG9uZTtcbiAgICAgICAgdG9rZW4uYXZhdGFyID0gdXNlci5hdmF0YXI7XG4gICAgICB9XG4gICAgICByZXR1cm4gdG9rZW47XG4gICAgfSxcbiAgICBhc3luYyBzZXNzaW9uKHsgc2Vzc2lvbiwgdG9rZW4gfSkge1xuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIHNlc3Npb24udXNlci5pZCA9IHRva2VuLnN1YiE7XG4gICAgICAgIHNlc3Npb24udXNlci5yb2xlID0gdG9rZW4ucm9sZSBhcyBzdHJpbmc7XG4gICAgICAgIHNlc3Npb24udXNlci5waG9uZSA9IHRva2VuLnBob25lIGFzIHN0cmluZztcbiAgICAgICAgc2Vzc2lvbi51c2VyLmF2YXRhciA9IHRva2VuLmF2YXRhciBhcyBzdHJpbmc7XG4gICAgICB9XG4gICAgICByZXR1cm4gc2Vzc2lvbjtcbiAgICB9LFxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogXCIvYXV0aC9sb2dpblwiLFxuICAgIC8vIHNpZ25VcDogXCIvYXV0aC9yZWdpc3RlclwiLCAvLyDYutmK2LEg2YXYr9i52YjZhSDZgdmKIE5leHRBdXRoXG4gIH0sXG59O1xuIl0sIm5hbWVzIjpbIkNyZWRlbnRpYWxzUHJvdmlkZXIiLCJiY3J5cHQiLCJwcmlzbWEiLCJhdXRoT3B0aW9ucyIsInByb3ZpZGVycyIsIm5hbWUiLCJjcmVkZW50aWFscyIsImVtYWlsT3JQaG9uZSIsImxhYmVsIiwidHlwZSIsInBhc3N3b3JkIiwiYXV0aG9yaXplIiwidXNlciIsImZpbmRGaXJzdCIsIndoZXJlIiwiT1IiLCJlbWFpbCIsInBob25lIiwiaXNBY3RpdmUiLCJpc1Bhc3N3b3JkVmFsaWQiLCJjb21wYXJlIiwicGFzc3dvcmRIYXNoIiwiaWQiLCJyb2xlIiwiYXZhdGFyIiwidW5kZWZpbmVkIiwic2Vzc2lvbiIsInN0cmF0ZWd5IiwiY2FsbGJhY2tzIiwiand0IiwidG9rZW4iLCJzdWIiLCJwYWdlcyIsInNpZ25JbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO0tBQVE7QUFDaEIsR0FBRTtBQUVKLElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknXSxcbiAgfSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Flimits%2Froute&page=%2Fapi%2Fads%2Flimits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Flimits%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();