import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// جلب حدود الإعلانات للمستخدم الحالي
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        freeAdsCount: true,
        freeAdsExpiresAt: true,
        paidAdsCount: true,
        paidAdsExpiresAt: true,
        _count: {
          select: {
            ads: {
              where: { isActive: true },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    // حساب الإحصائيات
    const totalActiveAds = user._count.ads;

    // حساب إجمالي المشاهدات من الإعلانات
    const adStats = await prisma.ad.aggregate({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      _sum: {
        views: true,
      },
    });

    const totalViews = adStats._sum.views || 0;
    const totalContacts = 0; // يمكن حسابه من الرسائل لاحقاً

    // حساب الإعلانات المتاحة
    const now = new Date();

    // التحقق من انتهاء صلاحية الإعلانات المجانية
    const freeAdsUsed = totalActiveAds; // عدد الإعلانات النشطة
    const freeAdsAvailable =
      user.freeAdsExpiresAt && user.freeAdsExpiresAt < now
        ? 0
        : Math.max(0, user.freeAdsCount - freeAdsUsed);

    // التحقق من انتهاء صلاحية الإعلانات المدفوعة
    const paidAdsUsed = 0; // لا يوجد نظام دفع حالياً
    const paidAdsAvailable =
      user.paidAdsExpiresAt && user.paidAdsExpiresAt < now
        ? 0
        : Math.max(0, user.paidAdsCount - paidAdsUsed);

    return NextResponse.json({
      success: true,
      data: {
        freeAds: {
          total: user.freeAdsCount,
          used: freeAdsUsed,
          available: freeAdsAvailable,
          expiresAt: user.freeAdsExpiresAt,
        },
        paidAds: {
          total: user.paidAdsCount,
          used: paidAdsUsed,
          available: paidAdsAvailable,
          expiresAt: user.paidAdsExpiresAt,
        },
        totalActiveAds: totalActiveAds,
        totalViews: totalViews,
        totalContacts: totalContacts,
        canCreateFreeAd: freeAdsAvailable > 0,
        canCreatePaidAd: paidAdsAvailable > 0,
      },
    });
  } catch (error) {
    console.error("Error fetching ad limits:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في جلب حدود الإعلانات" },
      { status: 500 }
    );
  }
}

// تحديث حدود الإعلانات (للإدارة)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    // التحقق من صلاحيات الإدارة
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "admin") {
      return NextResponse.json(
        { success: false, error: "غير مسموح لك بهذا الإجراء" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      userId,
      freeAdsCount,
      paidAdsCount,
      promotedAdsCount,
      freeAdsDuration,
      paidAdsDuration,
      promotedAdsDuration,
    } = body;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: "معرف المستخدم مطلوب" },
        { status: 400 }
      );
    }

    const now = new Date();
    const updateData: any = {};

    if (freeAdsCount !== undefined) {
      updateData.freeAdsCount = freeAdsCount;
      if (freeAdsDuration) {
        updateData.freeAdsExpiresAt = new Date(
          now.getTime() + freeAdsDuration * 24 * 60 * 60 * 1000
        );
      }
    }

    if (paidAdsCount !== undefined) {
      updateData.paidAdsCount = paidAdsCount;
      if (paidAdsDuration) {
        updateData.paidAdsExpiresAt = new Date(
          now.getTime() + paidAdsDuration * 24 * 60 * 60 * 1000
        );
      }
    }

    if (promotedAdsCount !== undefined) {
      updateData.promotedAdsCount = promotedAdsCount;
      if (promotedAdsDuration) {
        updateData.promotedAdsExpiresAt = new Date(
          now.getTime() + promotedAdsDuration * 24 * 60 * 60 * 1000
        );
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        freeAdsCount: true,
        freeAdsUsed: true,
        freeAdsExpiresAt: true,
        paidAdsCount: true,
        paidAdsUsed: true,
        paidAdsExpiresAt: true,
        promotedAdsCount: true,
        promotedAdsUsed: true,
        promotedAdsExpiresAt: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: "تم تحديث حدود الإعلانات بنجاح",
    });
  } catch (error) {
    console.error("Error updating ad limits:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في تحديث حدود الإعلانات" },
      { status: 500 }
    );
  }
}
