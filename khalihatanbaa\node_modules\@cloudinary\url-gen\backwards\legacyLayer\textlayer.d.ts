import Layer from './layer.js';
declare class TextLayer extends Layer {
    /**
     * @constructor TextLayer
     * @param {Object} options - layer parameters
     */
    constructor(options?: {});
    resourceType(resourceType: string): void;
    type(type: string): void;
    format(format: any): void;
    fontFamily(fontFamily: string): this;
    fontSize(fontSize: string | number): this;
    fontWeight(fontWeight: string): this;
    fontStyle(fontStyle: string): this;
    textDecoration(textDecoration: string): this;
    textAlign(textAlign: string): this;
    stroke(stroke: string): this;
    letterSpacing(letterSpacing: string): this;
    lineSpacing(lineSpacing: string): this;
    fontHinting(fontHinting: string): this;
    fontAntialiasing(fontAntialiasing: string): this;
    text(text: string): this;
    /**
     * generate the string representation of the layer
     * @function TextLayer#toString
     * @return {String}
     */
    toString(): string;
    textStyleIdentifier(): string;
}
export default TextLayer;
