/**
 * @description Contains methods to specify the type of artistic filter </br>
 * Learn more: {@link https://cloudinary.com/documentation/effects_and_artistic_enhancements#artistic_filter_effects|Artistic Filter}
 * @namespace ArtisticFilter
 * @memberOf Qualifiers
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function alDente(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function athena(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function audrey(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function aurora(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function daguerre(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function eucalyptus(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function fes(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function frost(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function hairspray(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function hokusai(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function incognito(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function linen(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function peacock(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function primavera(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function quartz(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function redRock(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function refresh(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function sizzle(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function sonnet(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function ukulele(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ArtisticFilter
 */
declare function zorro(): string;
declare const ArtisticFilter: {
    alDente: typeof alDente;
    athena: typeof athena;
    audrey: typeof audrey;
    aurora: typeof aurora;
    daguerre: typeof daguerre;
    eucalyptus: typeof eucalyptus;
    hairspray: typeof hairspray;
    hokusai: typeof hokusai;
    peacock: typeof peacock;
    primavera: typeof primavera;
    quartz: typeof quartz;
    incognito: typeof incognito;
    redRock: typeof redRock;
    sizzle: typeof sizzle;
    fes: typeof fes;
    linen: typeof linen;
    refresh: typeof refresh;
    sonnet: typeof sonnet;
    ukulele: typeof ukulele;
    frost: typeof frost;
    zorro: typeof zorro;
};
export { ArtisticFilter, alDente, athena, audrey, aurora, daguerre, eucalyptus, hairspray, hokusai, peacock, primavera, quartz, incognito, redRock, sizzle, fes, linen, refresh, sonnet, ukulele, frost, zorro };
