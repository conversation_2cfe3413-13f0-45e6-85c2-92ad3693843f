{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-dark-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* معلومات الموقع */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4\">\n              <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">خ</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">خَلّيها تنْباع</h3>\n                <p className=\"text-sm text-gray-400\">منصة البيع والشراء</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. \n              نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">فيسبوك</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">تلغرام</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">واتساب</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* روابط سريعة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  من نحن\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  كيف يعمل الموقع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety-tips\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  نصائح الأمان\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  باقات الإعلانات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  اتصل بنا\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* الدعم والمساعدة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">الدعم والمساعدة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  مركز المساعدة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  شروط الاستخدام\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الإبلاغ عن مشكلة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* معلومات الاتصال */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center md:text-right\">\n              <h5 className=\"font-semibold mb-2\">البريد الإلكتروني</h5>\n              <p className=\"text-gray-300\"><EMAIL></p>\n            </div>\n            <div className=\"text-center\">\n              <h5 className=\"font-semibold mb-2\">رقم الهاتف</h5>\n              <p className=\"text-gray-300\" dir=\"ltr\">+963 11 123 4567</p>\n            </div>\n            <div className=\"text-center md:text-left\">\n              <h5 className=\"font-semibold mb-2\">العنوان</h5>\n              <p className=\"text-gray-300\">دمشق، سوريا</p>\n            </div>\n          </div>\n        </div>\n\n        {/* حقوق النشر */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\n          </p>\n          <p className=\"text-gray-500 text-sm mt-2\">\n            تم التطوير بـ ❤️ في سوريا\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAmD;;;;;;;;;;;sDAI1F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;wCAAgB,KAAI;kDAAM;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ui/PlaceholderImage.tsx"], "sourcesContent": ["interface PlaceholderImageProps {\n  width?: number\n  height?: number\n  text?: string\n  className?: string\n}\n\nexport function PlaceholderImage({ \n  width = 300, \n  height = 200, \n  text = 'صورة', \n  className = '' \n}: PlaceholderImageProps) {\n  return (\n    <div \n      className={`bg-gray-200 flex items-center justify-center text-gray-500 ${className}`}\n      style={{ width, height }}\n    >\n      <div className=\"text-center\">\n        <svg \n          className=\"mx-auto h-12 w-12 text-gray-400 mb-2\" \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\" \n            strokeWidth={2} \n            d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" \n          />\n        </svg>\n        <p className=\"text-sm\">{text}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,SAAS,iBAAiB,EAC/B,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,OAAO,MAAM,EACb,YAAY,EAAE,EACQ;IACtB,qBACE,8OAAC;QACC,WAAW,CAAC,2DAA2D,EAAE,WAAW;QACpF,OAAO;YAAE;YAAO;QAAO;kBAEvB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;8BAGN,8OAAC;oBAAE,WAAU;8BAAW;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ui/ImageUpload.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef } from \"react\";\nimport { CldUploadWidget } from \"next-cloudinary\";\nimport {\n  PhotoIcon,\n  XMarkIcon,\n  PlusIcon,\n  ArrowUpTrayIcon,\n} from \"@heroicons/react/24/outline\";\n\ninterface ImageUploadProps {\n  value: string[];\n  onChange: (urls: string[]) => void;\n  maxImages?: number;\n  disabled?: boolean;\n}\n\nexport function ImageUpload({\n  value = [],\n  onChange,\n  maxImages = 5,\n  disabled = false,\n}: ImageUploadProps) {\n  const [uploading, setUploading] = useState(false);\n\n  const handleUpload = (result: any) => {\n    if (result.event === \"success\") {\n      const newUrls = [...value, result.info.secure_url];\n      onChange(newUrls);\n      setUploading(false);\n    }\n  };\n\n  const removeImage = (index: number) => {\n    const newUrls = value.filter((_, i) => i !== index);\n    onChange(newUrls);\n  };\n\n  const canAddMore = value.length < maxImages;\n\n  return (\n    <div className=\"space-y-4\">\n      {/* عرض الصور المرفوعة */}\n      {value.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n          {value.map((url, index) => (\n            <div key={index} className=\"relative group\">\n              <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n                <img\n                  src={url}\n                  alt={`صورة ${index + 1}`}\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n\n              {/* زر الحذف */}\n              <button\n                type=\"button\"\n                onClick={() => removeImage(index)}\n                disabled={disabled}\n                className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity disabled:opacity-50\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n\n              {/* رقم الصورة */}\n              <div className=\"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\n                {index + 1}\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* زر إضافة صور */}\n      {canAddMore && (\n        <CldUploadWidget\n          uploadPreset=\"khalihatanbaa_preset\"\n          options={{\n            maxFiles: 1,\n            resourceType: \"image\",\n            clientAllowedFormats: [\"jpg\", \"jpeg\", \"png\", \"webp\"],\n            maxFileSize: 5000000, // 5MB\n            folder: \"khalihatanbaa/ads\",\n            transformation: [\n              { width: 800, height: 600, crop: \"limit\" },\n              { quality: \"auto\" },\n              { format: \"auto\" },\n            ],\n          }}\n          onUpload={handleUpload}\n          onOpen={() => setUploading(true)}\n          onClose={() => setUploading(false)}\n        >\n          {({ open }) => (\n            <button\n              type=\"button\"\n              onClick={() => open()}\n              disabled={disabled || uploading}\n              className=\"w-full border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <div className=\"flex flex-col items-center\">\n                {uploading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-3\"></div>\n                    <p className=\"text-sm text-gray-600\">جاري رفع الصورة...</p>\n                  </>\n                ) : (\n                  <>\n                    <PhotoIcon className=\"h-12 w-12 text-gray-400 mb-3\" />\n                    <p className=\"text-sm font-medium text-gray-900 mb-1\">\n                      اضغط لإضافة صورة\n                    </p>\n                    <p className=\"text-xs text-gray-500\">\n                      PNG, JPG, WEBP حتى 5MB\n                    </p>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {value.length} من {maxImages} صور\n                    </p>\n                  </>\n                )}\n              </div>\n            </button>\n          )}\n        </CldUploadWidget>\n      )}\n\n      {/* رسالة عند الوصول للحد الأقصى */}\n      {!canAddMore && (\n        <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n          <p className=\"text-sm text-gray-600\">\n            تم الوصول للحد الأقصى من الصور ({maxImages} صور)\n          </p>\n        </div>\n      )}\n\n      {/* نصائح */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n          نصائح للحصول على أفضل النتائج:\n        </h4>\n        <ul className=\"text-xs text-blue-700 space-y-1\">\n          <li>• استخدم صور واضحة وعالية الجودة</li>\n          <li>• اعرض المنتج من زوايا مختلفة</li>\n          <li>• تأكد من الإضاءة الجيدة</li>\n          <li>• الصورة الأولى ستكون الصورة الرئيسية</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n\n// مكون مبسط لعرض الصور فقط\nexport function ImageGallery({ images }: { images: string[] }) {\n  const [currentImage, setCurrentImage] = useState(0);\n\n  if (!images || images.length === 0) {\n    return (\n      <div className=\"aspect-square bg-gray-100 rounded-lg flex items-center justify-center\">\n        <PhotoIcon className=\"h-16 w-16 text-gray-400\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* الصورة الرئيسية */}\n      <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n        <img\n          src={images[currentImage]}\n          alt={`صورة ${currentImage + 1}`}\n          className=\"w-full h-full object-cover\"\n        />\n      </div>\n\n      {/* الصور المصغرة */}\n      {images.length > 1 && (\n        <div className=\"flex space-x-2 space-x-reverse overflow-x-auto\">\n          {images.map((image, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentImage(index)}\n              className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${\n                currentImage === index\n                  ? \"border-primary-500\"\n                  : \"border-gray-200 hover:border-gray-300\"\n              }`}\n            >\n              <img\n                src={image}\n                alt={`صورة ${index + 1}`}\n                className=\"w-full h-full object-cover\"\n              />\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* مؤشر الصور */}\n      {images.length > 1 && (\n        <div className=\"text-center\">\n          <span className=\"text-sm text-gray-500\">\n            {currentImage + 1} من {images.length}\n          </span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAkBO,SAAS,YAAY,EAC1B,QAAQ,EAAE,EACV,QAAQ,EACR,YAAY,CAAC,EACb,WAAW,KAAK,EACC;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,KAAK,KAAK,WAAW;YAC9B,MAAM,UAAU;mBAAI;gBAAO,OAAO,IAAI,CAAC,UAAU;aAAC;YAClD,SAAS;YACT,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,UAAU,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC7C,SAAS;IACX;IAEA,MAAM,aAAa,MAAM,MAAM,GAAG;IAElC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,KAAK,sBACf,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK;oCACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;oCACxB,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,YAAY;gCAC3B,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAIvB,8OAAC;gCAAI,WAAU;0CACZ,QAAQ;;;;;;;uBArBH;;;;;;;;;;YA6Bf,4BACC,8OAAC,oJAAA,CAAA,kBAAe;gBACd,cAAa;gBACb,SAAS;oBACP,UAAU;oBACV,cAAc;oBACd,sBAAsB;wBAAC;wBAAO;wBAAQ;wBAAO;qBAAO;oBACpD,aAAa;oBACb,QAAQ;oBACR,gBAAgB;wBACd;4BAAE,OAAO;4BAAK,QAAQ;4BAAK,MAAM;wBAAQ;wBACzC;4BAAE,SAAS;wBAAO;wBAClB;4BAAE,QAAQ;wBAAO;qBAClB;gBACH;gBACA,UAAU;gBACV,QAAQ,IAAM,aAAa;gBAC3B,SAAS,IAAM,aAAa;0BAE3B,CAAC,EAAE,IAAI,EAAE,iBACR,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM;wBACf,UAAU,YAAY;wBACtB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,0BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;6DAGvC;;kDACE,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAGrC,8OAAC;wCAAE,WAAU;;4CACV,MAAM,MAAM;4CAAC;4CAAK;4CAAU;;;;;;;;;;;;;;;;;;;;;;;;YAW5C,CAAC,4BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAwB;wBACF;wBAAU;;;;;;;;;;;;0BAMjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;AAGO,SAAS,aAAa,EAAE,MAAM,EAAwB;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;QAClC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;IAG3B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAK,MAAM,CAAC,aAAa;oBACzB,KAAK,CAAC,KAAK,EAAE,eAAe,GAAG;oBAC/B,WAAU;;;;;;;;;;;YAKb,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,4DAA4D,EACtE,iBAAiB,QACb,uBACA,yCACJ;kCAEF,cAAA,8OAAC;4BACC,KAAK;4BACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;4BACxB,WAAU;;;;;;uBAXP;;;;;;;;;;YAmBZ,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;wBACb,eAAe;wBAAE;wBAAK,OAAO,MAAM;;;;;;;;;;;;;;;;;;AAMhD", "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ratings/RatingModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { XMarkIcon, StarIcon } from '@heroicons/react/24/outline'\nimport { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'\n\ninterface RatingModalProps {\n  isOpen: boolean\n  onClose: () => void\n  sellerId: string\n  sellerName: string\n  onRatingAdded: () => void\n}\n\nexport function RatingModal({ isOpen, onClose, sellerId, sellerName, onRatingAdded }: RatingModalProps) {\n  const { data: session } = useSession()\n  const [rating, setRating] = useState(0)\n  const [hoverRating, setHoverRating] = useState(0)\n  const [comment, setComment] = useState('')\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [error, setError] = useState('')\n\n  if (!isOpen) return null\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!session?.user?.id) {\n      setError('يجب تسجيل الدخول أولاً')\n      return\n    }\n\n    if (rating === 0) {\n      setError('يرجى اختيار تقييم')\n      return\n    }\n\n    setIsSubmitting(true)\n    setError('')\n\n    try {\n      const response = await fetch('/api/ratings', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          sellerId,\n          rating,\n          comment: comment.trim() || undefined\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        onRatingAdded()\n        onClose()\n        // إعادة تعيين النموذج\n        setRating(0)\n        setComment('')\n        setError('')\n      } else {\n        setError(data.error || 'حدث خطأ في إضافة التقييم')\n      }\n    } catch (error) {\n      console.error('Error submitting rating:', error)\n      setError('حدث خطأ في إضافة التقييم')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose()\n      setRating(0)\n      setComment('')\n      setError('')\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-dark-800\">تقييم البائع</h3>\n          <button\n            onClick={handleClose}\n            disabled={isSubmitting}\n            className=\"p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50\"\n          >\n            <XMarkIcon className=\"h-5 w-5 text-gray-500\" />\n          </button>\n        </div>\n\n        <div className=\"mb-4\">\n          <p className=\"text-gray-600 mb-2\">كيف كانت تجربتك مع البائع؟</p>\n          <p className=\"font-medium text-dark-800\">{sellerName}</p>\n        </div>\n\n        <form onSubmit={handleSubmit}>\n          {/* نجوم التقييم */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n              التقييم *\n            </label>\n            <div className=\"flex items-center space-x-1 space-x-reverse\">\n              {[1, 2, 3, 4, 5].map((star) => (\n                <button\n                  key={star}\n                  type=\"button\"\n                  onClick={() => setRating(star)}\n                  onMouseEnter={() => setHoverRating(star)}\n                  onMouseLeave={() => setHoverRating(0)}\n                  className=\"p-1 transition-colors\"\n                  disabled={isSubmitting}\n                >\n                  {star <= (hoverRating || rating) ? (\n                    <StarSolidIcon className=\"h-8 w-8 text-yellow-400\" />\n                  ) : (\n                    <StarIcon className=\"h-8 w-8 text-gray-300\" />\n                  )}\n                </button>\n              ))}\n            </div>\n            <div className=\"mt-1 text-sm text-gray-500\">\n              {rating === 0 && 'اختر تقييمك'}\n              {rating === 1 && 'سيء جداً'}\n              {rating === 2 && 'سيء'}\n              {rating === 3 && 'متوسط'}\n              {rating === 4 && 'جيد'}\n              {rating === 5 && 'ممتاز'}\n            </div>\n          </div>\n\n          {/* تعليق */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n              تعليق (اختياري)\n            </label>\n            <textarea\n              value={comment}\n              onChange={(e) => setComment(e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              placeholder=\"شارك تجربتك مع البائع...\"\n              maxLength={500}\n              disabled={isSubmitting}\n            />\n            <div className=\"mt-1 text-xs text-gray-500 text-left\">\n              {comment.length}/500\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 bg-red-50 border border-red-200 text-red-600 px-3 py-2 rounded-lg text-sm\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"flex space-x-3 space-x-reverse\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              disabled={isSubmitting}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\"\n            >\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              disabled={rating === 0 || isSubmitting}\n              className=\"flex-1 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? 'جاري الإرسال...' : 'إرسال التقييم'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAeO,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAoB;IACpG,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,SAAS;YACT;QACF;QAEA,IAAI,WAAW,GAAG;YAChB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA,SAAS,QAAQ,IAAI,MAAM;gBAC7B;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB;gBACA;gBACA,sBAAsB;gBACtB,UAAU;gBACV,WAAW;gBACX,SAAS;YACX,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA,UAAU;YACV,WAAW;YACX,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAG5C,8OAAC;oBAAK,UAAU;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,UAAU;4CACzB,cAAc,IAAM,eAAe;4CACnC,cAAc,IAAM,eAAe;4CACnC,WAAU;4CACV,UAAU;sDAET,QAAQ,CAAC,eAAe,MAAM,kBAC7B,8OAAC,6MAAA,CAAA,WAAa;gDAAC,WAAU;;;;;qEAEzB,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;2CAXjB;;;;;;;;;;8CAgBX,8OAAC;oCAAI,WAAU;;wCACZ,WAAW,KAAK;wCAChB,WAAW,KAAK;wCAChB,WAAW,KAAK;wCAChB,WAAW,KAAK;wCAChB,WAAW,KAAK;wCAChB,WAAW,KAAK;;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,MAAM;oCACN,WAAU;oCACV,aAAY;oCACZ,WAAW;oCACX,UAAU;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,MAAM;wCAAC;;;;;;;;;;;;;wBAInB,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU,WAAW,KAAK;oCAC1B,WAAU;8CAET,eAAe,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD", "debugId": null}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ratings/RatingsList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { StarIcon, UserIcon } from '@heroicons/react/24/outline'\nimport { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'\n\ninterface Rating {\n  id: string\n  rating: number\n  comment?: string\n  createdAt: string\n  giver: {\n    id: string\n    name: string\n    avatar?: string\n  }\n}\n\ninterface RatingSummary {\n  total: number\n  average: number\n  distribution: {\n    star: number\n    count: number\n  }[]\n}\n\ninterface RatingsListProps {\n  sellerId: string\n  refreshTrigger?: number\n}\n\nexport function RatingsList({ sellerId, refreshTrigger }: RatingsListProps) {\n  const [ratings, setRatings] = useState<Rating[]>([])\n  const [summary, setSummary] = useState<RatingSummary | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [page, setPage] = useState(1)\n  const [hasMore, setHasMore] = useState(true)\n\n  useEffect(() => {\n    fetchRatings(1, true)\n  }, [sellerId, refreshTrigger])\n\n  const fetchRatings = async (pageNum: number = 1, reset: boolean = false) => {\n    try {\n      const response = await fetch(`/api/ratings?sellerId=${sellerId}&page=${pageNum}&limit=10`)\n      const data = await response.json()\n\n      if (data.success) {\n        if (reset) {\n          setRatings(data.data.ratings)\n        } else {\n          setRatings(prev => [...prev, ...data.data.ratings])\n        }\n        \n        setSummary(data.data.summary)\n        setHasMore(data.data.pagination.page < data.data.pagination.totalPages)\n        setPage(pageNum)\n      }\n    } catch (error) {\n      console.error('Error fetching ratings:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadMore = () => {\n    if (!loading && hasMore) {\n      setLoading(true)\n      fetchRatings(page + 1, false)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString)\n    return date.toLocaleDateString('ar-SY', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {\n    const sizeClass = size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'\n    \n    return (\n      <div className=\"flex items-center\">\n        {[1, 2, 3, 4, 5].map((star) => (\n          <div key={star}>\n            {star <= rating ? (\n              <StarSolidIcon className={`${sizeClass} text-yellow-400`} />\n            ) : (\n              <StarIcon className={`${sizeClass} text-gray-300`} />\n            )}\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  if (loading && ratings.length === 0) {\n    return (\n      <div className=\"animate-pulse space-y-4\">\n        <div className=\"h-20 bg-gray-200 rounded-lg\"></div>\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"h-16 bg-gray-200 rounded-lg\"></div>\n        ))}\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* ملخص التقييمات */}\n      {summary && summary.total > 0 && (\n        <div className=\"bg-gray-50 rounded-lg p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* متوسط التقييم */}\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-dark-800 mb-2\">\n                {summary.average.toFixed(1)}\n              </div>\n              <div className=\"mb-2\">\n                {renderStars(Math.round(summary.average), 'md')}\n              </div>\n              <div className=\"text-sm text-gray-600\">\n                بناءً على {summary.total} تقييم\n              </div>\n            </div>\n\n            {/* توزيع التقييمات */}\n            <div className=\"space-y-2\">\n              {summary.distribution.reverse().map((dist) => (\n                <div key={dist.star} className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span className=\"text-sm text-gray-600 w-8\">\n                    {dist.star} ⭐\n                  </span>\n                  <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                    <div\n                      className=\"bg-yellow-400 h-2 rounded-full\"\n                      style={{\n                        width: `${summary.total > 0 ? (dist.count / summary.total) * 100 : 0}%`\n                      }}\n                    ></div>\n                  </div>\n                  <span className=\"text-sm text-gray-600 w-8\">\n                    {dist.count}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* قائمة التقييمات */}\n      <div className=\"space-y-4\">\n        <h3 className=\"text-lg font-semibold text-dark-800\">\n          التقييمات ({summary?.total || 0})\n        </h3>\n\n        {ratings.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            <StarIcon className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n            <p>لا توجد تقييمات حتى الآن</p>\n            <p className=\"text-sm mt-1\">كن أول من يقيم هذا البائع</p>\n          </div>\n        ) : (\n          <>\n            {ratings.map((rating) => (\n              <div key={rating.id} className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex items-start space-x-3 space-x-reverse\">\n                  <div className=\"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                    {rating.giver.avatar ? (\n                      <img\n                        src={rating.giver.avatar}\n                        alt={rating.giver.name}\n                        className=\"w-full h-full rounded-full object-cover\"\n                      />\n                    ) : (\n                      <UserIcon className=\"h-5 w-5 text-primary-600\" />\n                    )}\n                  </div>\n\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <div>\n                        <p className=\"font-medium text-dark-800\">\n                          {rating.giver.name}\n                        </p>\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          {renderStars(rating.rating)}\n                          <span className=\"text-sm text-gray-600\">\n                            {formatDate(rating.createdAt)}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {rating.comment && (\n                      <p className=\"text-gray-700 text-sm leading-relaxed\">\n                        {rating.comment}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n\n            {/* زر تحميل المزيد */}\n            {hasMore && (\n              <div className=\"text-center\">\n                <button\n                  onClick={loadMore}\n                  disabled={loading}\n                  className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\"\n                >\n                  {loading ? 'جاري التحميل...' : 'تحميل المزيد'}\n                </button>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAgCO,SAAS,YAAY,EAAE,QAAQ,EAAE,cAAc,EAAoB;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,GAAG;IAClB,GAAG;QAAC;QAAU;KAAe;IAE7B,MAAM,eAAe,OAAO,UAAkB,CAAC,EAAE,QAAiB,KAAK;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,EAAE,QAAQ,SAAS,CAAC;YACzF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,OAAO;oBACT,WAAW,KAAK,IAAI,CAAC,OAAO;gBAC9B,OAAO;oBACL,WAAW,CAAA,OAAQ;+BAAI;+BAAS,KAAK,IAAI,CAAC,OAAO;yBAAC;gBACpD;gBAEA,WAAW,KAAK,IAAI,CAAC,OAAO;gBAC5B,WAAW,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU;gBACtE,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,WAAW,SAAS;YACvB,WAAW;YACX,aAAa,OAAO,GAAG;QACzB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc,CAAC,QAAgB,OAAoB,IAAI;QAC3D,MAAM,YAAY,SAAS,OAAO,YAAY;QAE9C,qBACE,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC;8BACE,QAAQ,uBACP,8OAAC,6MAAA,CAAA,WAAa;wBAAC,WAAW,GAAG,UAAU,gBAAgB,CAAC;;;;;6CAExD,8OAAC,+MAAA,CAAA,WAAQ;wBAAC,WAAW,GAAG,UAAU,cAAc,CAAC;;;;;;mBAJ3C;;;;;;;;;;IAUlB;IAEA,IAAI,WAAW,QAAQ,MAAM,KAAK,GAAG;QACnC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;gBACd;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wBAAY,WAAU;uBAAb;;;;;;;;;;;IAIlB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,WAAW,QAAQ,KAAK,GAAG,mBAC1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,OAAO,CAAC,OAAO,CAAC;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;8CACZ,YAAY,KAAK,KAAK,CAAC,QAAQ,OAAO,GAAG;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;wCAAwB;wCAC1B,QAAQ,KAAK;wCAAC;;;;;;;;;;;;;sCAK7B,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,YAAY,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,qBACnC,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAK,WAAU;;gDACb,KAAK,IAAI;gDAAC;;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,GAAG,QAAQ,KAAK,GAAG,IAAI,AAAC,KAAK,KAAK,GAAG,QAAQ,KAAK,GAAI,MAAM,EAAE,CAAC,CAAC;gDACzE;;;;;;;;;;;sDAGJ,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;;mCAbL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0BAuB7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAsC;4BACtC,SAAS,SAAS;4BAAE;;;;;;;oBAGjC,QAAQ,MAAM,KAAK,kBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAG9B;;4BACG,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAAoB,WAAU;8CAC7B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,OAAO,KAAK,CAAC,MAAM,iBAClB,8OAAC;oDACC,KAAK,OAAO,KAAK,CAAC,MAAM;oDACxB,KAAK,OAAO,KAAK,CAAC,IAAI;oDACtB,WAAU;;;;;yEAGZ,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAIxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,OAAO,KAAK,CAAC,IAAI;;;;;;8EAEpB,8OAAC;oEAAI,WAAU;;wEACZ,YAAY,OAAO,MAAM;sFAC1B,8OAAC;4EAAK,WAAU;sFACb,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;oDAMnC,OAAO,OAAO,kBACb,8OAAC;wDAAE,WAAU;kEACV,OAAO,OAAO;;;;;;;;;;;;;;;;;;mCA/Bf,OAAO,EAAE;;;;;4BAwCpB,yBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/ads/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useParams } from \"next/navigation\";\nimport { useSession } from \"next-auth/react\";\nimport Link from \"next/link\";\nimport { Header } from \"@/components/layout/Header\";\nimport { Footer } from \"@/components/layout/Footer\";\nimport { PlaceholderImage } from \"@/components/ui/PlaceholderImage\";\nimport { ImageGallery } from \"@/components/ui/ImageUpload\";\nimport { RatingModal } from \"@/components/ratings/RatingModal\";\nimport { RatingsList } from \"@/components/ratings/RatingsList\";\nimport { Ad } from \"@/types\";\nimport {\n  HeartIcon,\n  ShareIcon,\n  MapPinIcon,\n  ClockIcon,\n  EyeIcon,\n  ChatBubbleLeftRightIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n  StarIcon,\n} from \"@heroicons/react/24/outline\";\nimport {\n  HeartIcon as HeartSolidIcon,\n  StarIcon as StarSolidIcon,\n} from \"@heroicons/react/24/solid\";\n\n// بيانات تجريبية للإعلان\nconst sampleAd: Ad = {\n  id: \"1\",\n  title: \"تويوتا كامري 2018 فل كامل\",\n  description: `سيارة تويوتا كامري موديل 2018 بحالة ممتازة جداً\n  \nالمواصفات:\n- فل كامل (جلد، فتحة سقف، شاشة، كاميرا خلفية)\n- صيانة دورية في الوكالة\n- لون أبيض لؤلؤي\n- عدد الكيلومترات: 85,000 كم\n- جميع الأوراق سليمة\n- فحص كامل متاح\n\nالسيارة بحالة ممتازة ولا تحتاج أي مصاريف. جاهزة للاستخدام فوراً.\n\nللجادين فقط، يرجى التواصل عبر الرسائل أولاً.`,\n  price: 45000000,\n  category: \"سيارات\",\n  subCategory: \"تويوتا\",\n  condition: \"مستعمل\",\n  city: \"دمشق\",\n  region: \"المزة\",\n  addressDetail: \"قريب من جامع المزة\",\n  imageUrls: [\"/placeholder-car.jpg\"],\n  views: 125,\n  isActive: true,\n  isFreeAd: false,\n  isPromoted: true,\n  createdAt: new Date(\"2024-01-15\"),\n  updatedAt: new Date(\"2024-01-15\"),\n  userId: \"user1\",\n  specifications: {\n    year: 2018,\n    mileage: 85000,\n    fuel: \"بنزين\",\n    color: \"أبيض لؤلؤي\",\n    transmission: \"أوتوماتيك\",\n    engine: \"2.5 لتر\",\n  },\n  user: {\n    id: \"user1\",\n    name: \"أحمد محمد\",\n    email: \"<EMAIL>\",\n    phone: \"+963991234567\",\n    role: \"user\",\n    avatar: \"\",\n    isActive: true,\n    freeAdsCount: 2,\n    freeAdsExpiresAt: new Date(),\n    paidAdsCount: 0,\n    paidAdsExpiresAt: new Date(),\n    ratingAverage: 4.5,\n    ratingCount: 12,\n    createdAt: new Date(\"2023-06-01\"),\n    updatedAt: new Date(\"2024-01-15\"),\n  },\n};\n\n// إعلانات مشابهة تجريبية\nconst similarAds: Ad[] = [\n  {\n    id: \"2\",\n    title: \"تويوتا كامري 2019\",\n    description: \"سيارة بحالة جيدة\",\n    price: 48000000,\n    category: \"سيارات\",\n    subCategory: \"تويوتا\",\n    condition: \"مستعمل\",\n    city: \"دمشق\",\n    region: \"الشعلان\",\n    imageUrls: [],\n    views: 89,\n    isActive: true,\n    isFreeAd: false,\n    isPromoted: false,\n    createdAt: new Date(\"2024-01-14\"),\n    updatedAt: new Date(\"2024-01-14\"),\n    userId: \"user2\",\n  },\n];\n\nexport default function AdDetailsPage() {\n  const params = useParams();\n  const { data: session } = useSession();\n  const [ad, setAd] = useState<Ad | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [isFavorite, setIsFavorite] = useState(false);\n  const [showContactModal, setShowContactModal] = useState(false);\n  const [showRatingModal, setShowRatingModal] = useState(false);\n  const [ratingsRefresh, setRatingsRefresh] = useState(0);\n  const [hasContactedSeller, setHasContactedSeller] = useState(false);\n\n  useEffect(() => {\n    // محاكاة جلب بيانات الإعلان\n    setTimeout(() => {\n      setAd(sampleAd);\n      setLoading(false);\n    }, 1000);\n\n    // جلب حالة المفضلة إذا كان المستخدم مسجل دخول\n    if (session?.user?.id) {\n      checkFavoriteStatus();\n    }\n  }, [params.id, session]);\n\n  // التحقق من التواصل السابق مع البائع\n  useEffect(() => {\n    const checkContactHistory = async () => {\n      if (!session?.user?.id || !ad?.userId) return;\n\n      try {\n        const response = await fetch(\n          `/api/messages/check-contact?sellerId=${ad.userId}`\n        );\n        const data = await response.json();\n\n        if (data.success) {\n          setHasContactedSeller(data.hasContacted);\n        }\n      } catch (error) {\n        console.error(\"Error checking contact history:\", error);\n      }\n    };\n\n    if (ad && session?.user?.id) {\n      checkContactHistory();\n    }\n  }, [ad, session]);\n\n  const checkFavoriteStatus = async () => {\n    try {\n      const response = await fetch(\"/api/favorites\");\n      const data = await response.json();\n\n      if (data.success) {\n        const isFav = data.data.some((fav: any) => fav.ad.id === params.id);\n        setIsFavorite(isFav);\n      }\n    } catch (error) {\n      console.error(\"Error checking favorite status:\", error);\n    }\n  };\n\n  const toggleFavorite = async () => {\n    if (!session?.user?.id) {\n      alert(\"يجب تسجيل الدخول أولاً\");\n      return;\n    }\n\n    try {\n      if (isFavorite) {\n        // إزالة من المفضلة\n        const response = await fetch(`/api/favorites?adId=${params.id}`, {\n          method: \"DELETE\",\n        });\n        const data = await response.json();\n\n        if (data.success) {\n          setIsFavorite(false);\n        } else {\n          alert(data.error || \"حدث خطأ في إزالة الإعلان من المفضلة\");\n        }\n      } else {\n        // إضافة إلى المفضلة\n        const response = await fetch(\"/api/favorites\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify({ adId: params.id }),\n        });\n        const data = await response.json();\n\n        if (data.success) {\n          setIsFavorite(true);\n        } else {\n          alert(data.error || \"حدث خطأ في إضافة الإعلان إلى المفضلة\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error toggling favorite:\", error);\n      alert(\"حدث خطأ في تحديث المفضلة\");\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat(\"ar-SY\").format(price);\n  };\n\n  const formatDate = (date: Date) => {\n    return new Intl.DateTimeFormat(\"ar-SY\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    }).format(date);\n  };\n\n  const handleShare = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: ad?.title,\n        text: ad?.description,\n        url: window.location.href,\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      alert(\"تم نسخ الرابط!\");\n    }\n  };\n\n  const nextImage = () => {\n    if (ad && ad.imageUrls.length > 1) {\n      setCurrentImageIndex((prev) => (prev + 1) % ad.imageUrls.length);\n    }\n  };\n\n  const prevImage = () => {\n    if (ad && ad.imageUrls.length > 1) {\n      setCurrentImageIndex(\n        (prev) => (prev - 1 + ad.imageUrls.length) % ad.imageUrls.length\n      );\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/3 mb-6\"></div>\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n              <div className=\"lg:col-span-2\">\n                <div className=\"h-96 bg-gray-200 rounded-lg mb-6\"></div>\n                <div className=\"space-y-4\">\n                  <div className=\"h-6 bg-gray-200 rounded\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n              <div className=\"space-y-4\">\n                <div className=\"h-32 bg-gray-200 rounded\"></div>\n                <div className=\"h-24 bg-gray-200 rounded\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  if (!ad) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            الإعلان غير موجود\n          </h1>\n          <p className=\"text-gray-600 mb-8\">\n            عذراً، لم نتمكن من العثور على هذا الإعلان\n          </p>\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600\"\n          >\n            العودة للصفحة الرئيسية\n          </Link>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* مسار التنقل */}\n        <nav className=\"flex items-center space-x-2 space-x-reverse text-sm text-gray-500 mb-6\">\n          <Link href=\"/\" className=\"hover:text-primary-500\">\n            الرئيسية\n          </Link>\n          <span>/</span>\n          <Link\n            href={`/search?category=${ad.category}`}\n            className=\"hover:text-primary-500\"\n          >\n            {ad.category}\n          </Link>\n          <span>/</span>\n          <span className=\"text-gray-900\">{ad.title}</span>\n        </nav>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* المحتوى الرئيسي */}\n          <div className=\"lg:col-span-2\">\n            {/* معرض الصور */}\n            <div className=\"mb-6\">\n              {ad.imageUrls && ad.imageUrls.length > 0 ? (\n                <ImageGallery images={ad.imageUrls} />\n              ) : (\n                <div className=\"relative bg-gray-100 rounded-lg overflow-hidden\">\n                  <div className=\"aspect-w-16 aspect-h-9 h-96\">\n                    <PlaceholderImage\n                      width={800}\n                      height={400}\n                      text={`صورة ${ad.category}`}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* تفاصيل الإعلان */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div>\n                  <h1 className=\"text-2xl font-bold text-dark-800 mb-2\">\n                    {ad.title}\n                  </h1>\n                  <div className=\"flex items-center space-x-4 space-x-reverse text-sm text-gray-500\">\n                    <div className=\"flex items-center\">\n                      <MapPinIcon className=\"h-4 w-4 ml-1\" />\n                      {ad.city} - {ad.region}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <ClockIcon className=\"h-4 w-4 ml-1\" />\n                      {formatDate(ad.createdAt)}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <EyeIcon className=\"h-4 w-4 ml-1\" />\n                      {ad.views} مشاهدة\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <button\n                    onClick={toggleFavorite}\n                    className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\n                  >\n                    {isFavorite ? (\n                      <HeartSolidIcon className=\"h-5 w-5 text-red-500\" />\n                    ) : (\n                      <HeartIcon className=\"h-5 w-5 text-gray-600\" />\n                    )}\n                  </button>\n                  <button\n                    onClick={handleShare}\n                    className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\n                  >\n                    <ShareIcon className=\"h-5 w-5 text-gray-600\" />\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"text-3xl font-bold text-primary-500 mb-6\">\n                {formatPrice(ad.price)} <span className=\"text-lg\">ل.س</span>\n              </div>\n\n              <div className=\"prose prose-sm max-w-none\">\n                <h3 className=\"text-lg font-semibold text-dark-800 mb-3\">\n                  الوصف\n                </h3>\n                <div className=\"text-gray-700 whitespace-pre-line\">\n                  {ad.description}\n                </div>\n              </div>\n\n              {/* المواصفات */}\n              {ad.specifications &&\n                Object.keys(ad.specifications).length > 0 && (\n                  <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                    <h3 className=\"text-lg font-semibold text-dark-800 mb-3\">\n                      المواصفات\n                    </h3>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                      {Object.entries(ad.specifications).map(([key, value]) => (\n                        <div key={key} className=\"bg-gray-50 p-3 rounded-lg\">\n                          <div className=\"text-sm text-gray-600 mb-1\">\n                            {key === \"year\" && \"سنة الصنع\"}\n                            {key === \"mileage\" && \"الكيلومترات\"}\n                            {key === \"fuel\" && \"نوع الوقود\"}\n                            {key === \"color\" && \"اللون\"}\n                            {key === \"transmission\" && \"ناقل الحركة\"}\n                            {key === \"engine\" && \"المحرك\"}\n                          </div>\n                          <div className=\"font-medium text-dark-800\">\n                            {typeof value === \"number\"\n                              ? value.toLocaleString(\"ar-SY\")\n                              : value}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n            </div>\n          </div>\n\n          {/* الشريط الجانبي */}\n          <div className=\"space-y-6\">\n            {/* معلومات البائع */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-dark-800 mb-4\">\n                معلومات البائع\n              </h3>\n\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\n                  <span className=\"text-primary-600 font-semibold\">\n                    {ad.user?.name.charAt(0)}\n                  </span>\n                </div>\n                <div className=\"mr-3\">\n                  <div className=\"font-medium text-dark-800\">\n                    {ad.user?.name}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">\n                    عضو منذ {formatDate(ad.user?.createdAt || new Date())}\n                  </div>\n                </div>\n              </div>\n\n              {ad.user?.ratingAverage && ad.user.ratingCount > 0 && (\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"flex items-center\">\n                    {[...Array(5)].map((_, i) => (\n                      <svg\n                        key={i}\n                        className={`h-4 w-4 ${\n                          i < Math.floor(ad.user?.ratingAverage || 0)\n                            ? \"text-yellow-400\"\n                            : \"text-gray-300\"\n                        }`}\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                      </svg>\n                    ))}\n                  </div>\n                  <span className=\"mr-2 text-sm text-gray-600\">\n                    {ad.user.ratingAverage.toFixed(1)} ({ad.user.ratingCount}{\" \"}\n                    تقييم)\n                  </span>\n                </div>\n              )}\n\n              {session?.user.id !== ad.userId && (\n                <div className=\"space-y-3\">\n                  <button\n                    onClick={() => setShowContactModal(true)}\n                    className=\"w-full bg-primary-500 text-white py-3 px-4 rounded-lg hover:bg-primary-600 transition-colors flex items-center justify-center\"\n                  >\n                    <ChatBubbleLeftRightIcon className=\"h-5 w-5 ml-2\" />\n                    تواصل مع البائع\n                  </button>\n\n                  {/* زر التقييم - يظهر فقط إذا كان هناك تواصل سابق */}\n                  {hasContactedSeller && (\n                    <button\n                      onClick={() => setShowRatingModal(true)}\n                      className=\"w-full border border-primary-500 text-primary-500 py-2 px-4 rounded-lg hover:bg-primary-50 transition-colors flex items-center justify-center\"\n                    >\n                      <StarIcon className=\"h-5 w-5 ml-2\" />\n                      تقييم البائع\n                    </button>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* نصائح الأمان */}\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <h4 className=\"font-semibold text-yellow-800 mb-2\">\n                نصائح للأمان\n              </h4>\n              <ul className=\"text-sm text-yellow-700 space-y-1\">\n                <li>• تأكد من المنتج قبل الدفع</li>\n                <li>• التقي في مكان عام وآمن</li>\n                <li>• لا تشارك معلوماتك المصرفية</li>\n                <li>• استخدم وسائل دفع آمنة</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* الإعلانات المشابهة */}\n        {similarAds.length > 0 && (\n          <div className=\"mt-12\">\n            <h2 className=\"text-2xl font-bold text-dark-800 mb-6\">\n              إعلانات مشابهة\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {similarAds.map((similarAd) => (\n                <Link\n                  key={similarAd.id}\n                  href={`/ads/${similarAd.id}`}\n                  className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow\"\n                >\n                  <div className=\"h-48 bg-gray-200\">\n                    {similarAd.imageUrls && similarAd.imageUrls.length > 0 ? (\n                      <img\n                        src={similarAd.imageUrls[0]}\n                        alt={similarAd.title}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    ) : (\n                      <PlaceholderImage\n                        width={300}\n                        height={192}\n                        text={`صورة ${similarAd.category}`}\n                        className=\"w-full h-full\"\n                      />\n                    )}\n                  </div>\n                  <div className=\"p-4\">\n                    <h3 className=\"font-semibold text-dark-800 mb-2 line-clamp-2\">\n                      {similarAd.title}\n                    </h3>\n                    <div className=\"text-lg font-bold text-primary-500\">\n                      {formatPrice(similarAd.price)}{\" \"}\n                      <span className=\"text-sm\">ل.س</span>\n                    </div>\n                    <div className=\"text-sm text-gray-500 mt-1\">\n                      {similarAd.city} - {similarAd.region}\n                    </div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* تقييمات البائع */}\n        <div className=\"mt-12\">\n          <RatingsList sellerId={ad.userId} refreshTrigger={ratingsRefresh} />\n        </div>\n      </main>\n\n      <Footer />\n\n      {/* نافذة التواصل */}\n      {showContactModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n            <h3 className=\"text-lg font-semibold text-dark-800 mb-4\">\n              تواصل مع البائع\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              سيتم فتح محادثة جديدة مع البائع. يمكنك إرسال رسالتك والتفاوض على\n              السعر.\n            </p>\n            <div className=\"flex space-x-3 space-x-reverse\">\n              <button\n                onClick={() => setShowContactModal(false)}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\"\n              >\n                إلغاء\n              </button>\n              <Link\n                href={`/messages?ad=${ad.id}&user=${ad.userId}`}\n                className=\"flex-1 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 text-center\"\n              >\n                بدء المحادثة\n              </Link>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* نافذة التقييم */}\n      {showRatingModal && ad && (\n        <RatingModal\n          isOpen={showRatingModal}\n          onClose={() => setShowRatingModal(false)}\n          sellerId={ad.userId}\n          sellerName={ad.user?.name || \"البائع\"}\n          onRatingAdded={() => {\n            setRatingsRefresh((prev) => prev + 1);\n            setShowRatingModal(false);\n          }}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAxBA;;;;;;;;;;;;;;AA6BA,yBAAyB;AACzB,MAAM,WAAe;IACnB,IAAI;IACJ,OAAO;IACP,aAAa,CAAC;;;;;;;;;;;;4CAY4B,CAAC;IAC3C,OAAO;IACP,UAAU;IACV,aAAa;IACb,WAAW;IACX,MAAM;IACN,QAAQ;IACR,eAAe;IACf,WAAW;QAAC;KAAuB;IACnC,OAAO;IACP,UAAU;IACV,UAAU;IACV,YAAY;IACZ,WAAW,IAAI,KAAK;IACpB,WAAW,IAAI,KAAK;IACpB,QAAQ;IACR,gBAAgB;QACd,MAAM;QACN,SAAS;QACT,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;IACV;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,UAAU;QACV,cAAc;QACd,kBAAkB,IAAI;QACtB,cAAc;QACd,kBAAkB,IAAI;QACtB,eAAe;QACf,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;AACF;AAEA,yBAAyB;AACzB,MAAM,aAAmB;IACvB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW,EAAE;QACb,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,IAAI,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,WAAW;YACT,MAAM;YACN,WAAW;QACb,GAAG;QAEH,8CAA8C;QAC9C,IAAI,SAAS,MAAM,IAAI;YACrB;QACF;IACF,GAAG;QAAC,OAAO,EAAE;QAAE;KAAQ;IAEvB,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI,CAAC,SAAS,MAAM,MAAM,CAAC,IAAI,QAAQ;YAEvC,IAAI;gBACF,MAAM,WAAW,MAAM,MACrB,CAAC,qCAAqC,EAAE,GAAG,MAAM,EAAE;gBAErD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,sBAAsB,KAAK,YAAY;gBACzC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;QAEA,IAAI,MAAM,SAAS,MAAM,IAAI;YAC3B;QACF;IACF,GAAG;QAAC;QAAI;KAAQ;IAEhB,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,CAAC,EAAE,KAAK,OAAO,EAAE;gBAClE,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,MAAM;YACN;QACF;QAEA,IAAI;YACF,IAAI,YAAY;gBACd,mBAAmB;gBACnB,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,OAAO,EAAE,EAAE,EAAE;oBAC/D,QAAQ;gBACV;gBACA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc;gBAChB,OAAO;oBACL,MAAM,KAAK,KAAK,IAAI;gBACtB;YACF,OAAO;gBACL,oBAAoB;gBACpB,MAAM,WAAW,MAAM,MAAM,kBAAkB;oBAC7C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE,MAAM,OAAO,EAAE;oBAAC;gBACzC;gBACA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc;gBAChB,OAAO;oBACL,MAAM,KAAK,KAAK,IAAI;gBACtB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO,IAAI;gBACX,MAAM,IAAI;gBACV,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;QACF,OAAO;YACL,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;YAClD,MAAM;QACR;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,GAAG;YACjC,qBAAqB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM;QACjE;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,GAAG;YACjC,qBACE,CAAC,OAAS,CAAC,OAAO,IAAI,GAAG,SAAS,CAAC,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM;QAEpE;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAGnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKvB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,IAAI,CAAC,IAAI;QACP,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;8BAIH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAyB;;;;;;0CAGlD,8OAAC;0CAAK;;;;;;0CACN,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,iBAAiB,EAAE,GAAG,QAAQ,EAAE;gCACvC,WAAU;0CAET,GAAG,QAAQ;;;;;;0CAEd,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAiB,GAAG,KAAK;;;;;;;;;;;;kCAG3C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACZ,GAAG,SAAS,IAAI,GAAG,SAAS,CAAC,MAAM,GAAG,kBACrC,8OAAC,uIAAA,CAAA,eAAY;4CAAC,QAAQ,GAAG,SAAS;;;;;iEAElC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;oDACf,OAAO;oDACP,QAAQ;oDACR,MAAM,CAAC,KAAK,EAAE,GAAG,QAAQ,EAAE;oDAC3B,WAAU;;;;;;;;;;;;;;;;;;;;;kDAQpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,GAAG,KAAK;;;;;;0EAEX,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,mNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;4EACrB,GAAG,IAAI;4EAAC;4EAAI,GAAG,MAAM;;;;;;;kFAExB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;4EACpB,WAAW,GAAG,SAAS;;;;;;;kFAE1B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,6MAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAClB,GAAG,KAAK;4EAAC;;;;;;;;;;;;;;;;;;;kEAKhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS;gEACT,WAAU;0EAET,2BACC,8OAAC,+MAAA,CAAA,YAAc;oEAAC,WAAU;;;;;yFAE1B,8OAAC,iNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAGzB,8OAAC;gEACC,SAAS;gEACT,WAAU;0EAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAK3B,8OAAC;gDAAI,WAAU;;oDACZ,YAAY,GAAG,KAAK;oDAAE;kEAAC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAGpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAGzD,8OAAC;wDAAI,WAAU;kEACZ,GAAG,WAAW;;;;;;;;;;;;4CAKlB,GAAG,cAAc,IAChB,OAAO,IAAI,CAAC,GAAG,cAAc,EAAE,MAAM,GAAG,mBACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAGzD,8OAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,GAAG,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAClD,8OAAC;gEAAc,WAAU;;kFACvB,8OAAC;wEAAI,WAAU;;4EACZ,QAAQ,UAAU;4EAClB,QAAQ,aAAa;4EACrB,QAAQ,UAAU;4EAClB,QAAQ,WAAW;4EACnB,QAAQ,kBAAkB;4EAC1B,QAAQ,YAAY;;;;;;;kFAEvB,8OAAC;wEAAI,WAAU;kFACZ,OAAO,UAAU,WACd,MAAM,cAAc,CAAC,WACrB;;;;;;;+DAZE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAuBxB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAIzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,GAAG,IAAI,EAAE,KAAK,OAAO;;;;;;;;;;;kEAG1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,GAAG,IAAI,EAAE;;;;;;0EAEZ,8OAAC;gEAAI,WAAU;;oEAAwB;oEAC5B,WAAW,GAAG,IAAI,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;;4CAKnD,GAAG,IAAI,EAAE,iBAAiB,GAAG,IAAI,CAAC,WAAW,GAAG,mBAC/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM;yDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gEAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,EAAE,iBAAiB,KACrC,oBACA,iBACJ;gEACF,MAAK;gEACL,SAAQ;0EAER,cAAA,8OAAC;oEAAK,GAAE;;;;;;+DATH;;;;;;;;;;kEAaX,8OAAC;wDAAK,WAAU;;4DACb,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;4DAAG;4DAAG,GAAG,IAAI,CAAC,WAAW;4DAAE;4DAAI;;;;;;;;;;;;;4CAMnE,SAAS,KAAK,OAAO,GAAG,MAAM,kBAC7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,oBAAoB;wDACnC,WAAU;;0EAEV,8OAAC,6OAAA,CAAA,0BAAuB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;oDAKrD,oCACC,8OAAC;wDACC,SAAS,IAAM,mBAAmB;wDAClC,WAAU;;0EAEV,8OAAC,+MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAS/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DAGnD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,WAAW,MAAM,GAAG,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;wCAC5B,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,kBACnD,8OAAC;oDACC,KAAK,UAAU,SAAS,CAAC,EAAE;oDAC3B,KAAK,UAAU,KAAK;oDACpB,WAAU;;;;;yEAGZ,8OAAC,4IAAA,CAAA,mBAAgB;oDACf,OAAO;oDACP,QAAQ;oDACR,MAAM,CAAC,KAAK,EAAE,UAAU,QAAQ,EAAE;oDAClC,WAAU;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,UAAU,KAAK;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;4DACZ,YAAY,UAAU,KAAK;4DAAG;0EAC/B,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;4DACZ,UAAU,IAAI;4DAAC;4DAAI,UAAU,MAAM;;;;;;;;;;;;;;uCA7BnC,UAAU,EAAE;;;;;;;;;;;;;;;;kCAuC3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAA,CAAA,cAAW;4BAAC,UAAU,GAAG,MAAM;4BAAE,gBAAgB;;;;;;;;;;;;;;;;;0BAItD,8OAAC,sIAAA,CAAA,SAAM;;;;;YAGN,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE;oCAC/C,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,mBAAmB,oBAClB,8OAAC,4IAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,UAAU,GAAG,MAAM;gBACnB,YAAY,GAAG,IAAI,EAAE,QAAQ;gBAC7B,eAAe;oBACb,kBAAkB,CAAC,OAAS,OAAO;oBACnC,mBAAmB;gBACrB;;;;;;;;;;;;AAKV", "debugId": null}}]}