# خَلّيها تنْباع - منصة البيع والشراء السورية

منصة إلكترونية عربية لبيع وشراء المنتجات المستعملة في سوريا تحت اسم "خَلّيها تنْباع"، مع دعم كامل للغة العربية (RTL) وتصميم حديث وسهل الاستخدام.

## 🚀 الميزات الرئيسية

### المرحلة الأولى (MVP) - تم التنفيذ ✅

- **واجهة عربية كاملة** مع دعم RTL
- **نظام المصادقة** (تسجيل دخول/إنشاء حساب)
- **الصفحة الرئيسية** مع عرض أحدث الإعلانات
- **نشر الإعلانات** مع معالج خطوات تفاعلي
- **البحث والفلترة** المتقدمة
- **صفحة تفاصيل الإعلان** مع معرض الصور
- **تصنيف الفئات** (عقارات، سيارات، إلكترونيات، إلخ)
- **تصميم متجاوب** يعمل على جميع الأجهزة

### الميزات المخططة للمراحل القادمة 🔄

- **نظام الدردشة الداخلي** للتواصل بين البائع والمشتري
- **نظام الإعلانات المجانية والمدفوعة** (3 إعلانات مجانية لمدة 15 يوم)
- **خوارزمية التوصيات** للإعلانات المشابهة
- **نظام التقييمات والمراجعات**
- **الملف الشخصي والمفضلة**
- **لوحة إدارة** للمشرفين
- **نظام الدفع** (Payeer/حوالة بنكية)
- **رفع الصور إلى Cloudinary**

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 15 مع TypeScript
- **Styling**: Tailwind CSS مع دعم RTL
- **Database**: PostgreSQL مع Prisma ORM
- **Authentication**: NextAuth.js
- **Icons**: Heroicons
- **Fonts**: Tajawal & Cairo (Google Fonts)

## 📦 التثبيت والتشغيل

### المتطلبات الأساسية

- Node.js 18+
- PostgreSQL
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**

```bash
git clone <repository-url>
cd khalihatanbaa
```

2. **تثبيت المكتبات**

```bash
npm install
```

3. **إعداد متغيرات البيئة**

```bash
cp .env.local.example .env.local
```

قم بتحديث الملف `.env.local` بالقيم الصحيحة:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/khalihatanbaa"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Cloudinary (للمراحل القادمة)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
```

4. **إعداد قاعدة البيانات**

```bash
npx prisma generate
npx prisma db push
```

5. **تشغيل المشروع**

```bash
npm run dev
```

المشروع سيكون متاحاً على: `http://localhost:3000`

## 📁 هيكل المشروع

```
khalihatanbaa/
├── src/
│   ├── app/                    # صفحات Next.js (App Router)
│   │   ├── auth/              # صفحات المصادقة
│   │   ├── ads/               # صفحات الإعلانات
│   │   ├── search/            # صفحة البحث
│   │   └── api/               # API Routes
│   ├── components/            # مكونات React
│   │   ├── layout/            # مكونات التخطيط
│   │   ├── home/              # مكونات الصفحة الرئيسية
│   │   └── ui/                # مكونات واجهة المستخدم
│   ├── lib/                   # مكتبات مساعدة
│   └── types/                 # أنواع TypeScript
├── prisma/                    # مخطط قاعدة البيانات
├── public/                    # الملفات العامة
└── tailwind.config.ts         # إعدادات Tailwind CSS
```

## 🎨 نظام الألوان

- **الأحمر الرئيسي**: `#FF4C4C`
- **البنفسجي الثانوي**: `#7C3AED`
- **الكحلي الغامق**: `#1E293B`
- **خلفيات فاتحة**: `#F8FAFC`

## 📱 الفئات المدعومة

- 🏠 **عقارات** (شقة، فيلا، أرض، محل تجاري، مكتب)
- 🚗 **سيارات** (تويوتا، نيسان، هيونداي، كيا، مرسيدس، BMW)
- 📱 **إلكترونيات** (هاتف ذكي، لابتوب، تلفاز، كاميرا)
- 🪑 **أثاث** (غرفة نوم، غرفة جلوس، مطبخ، مكتب)
- 👕 **ملابس** (رجالي، نسائي، أطفال، أحذية)
- ⚽ **رياضة** (كرة قدم، كرة سلة، جيم، دراجات)

## 🌍 المحافظات المدعومة

جميع المحافظات السورية: دمشق، حلب، حمص، حماة، اللاذقية، طرطوس، درعا، السويداء، القنيطرة، دير الزور، الرقة، الحسكة، إدلب، ريف دمشق.

## 🔐 نظام المصادقة

- تسجيل دخول بالبريد الإلكتروني أو رقم الهاتف
- إنشاء حساب جديد مع التحقق من البيانات
- حماية الصفحات التي تتطلب تسجيل دخول
- جلسات آمنة مع NextAuth.js

## 📊 قاعدة البيانات

### الجداول الرئيسية:

- **Users**: معلومات المستخدمين
- **Ads**: الإعلانات
- **Messages**: الرسائل (للمراحل القادمة)
- **Favorites**: المفضلة (للمراحل القادمة)
- **Ratings**: التقييمات (للمراحل القادمة)
- **AdPackages**: باقات الإعلانات (للمراحل القادمة)

## 🚀 خطة التطوير

### المرحلة 2: نظام الرسائل والتفاعل

- [ ] نظام الدردشة الداخلي
- [ ] الإشعارات
- [ ] المفضلة
- [ ] التقييمات

### المرحلة 3: النظام التجاري

- [ ] نظام الإعلانات المجانية/المدفوعة
- [ ] نظام الدفع
- [ ] لوحة إدارة المستخدم
- [ ] إحصائيات الإعلانات

### المرحلة 4: الميزات المتقدمة

- [ ] خوارزمية التوصيات الذكية
- [ ] لوحة إدارة شاملة
- [ ] تطبيق موبايل (React Native)
- [ ] API عامة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **الموقع**: khalihatanbaa.com
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +963 11 123 4567

---

**تم التطوير بـ ❤️ في سوريا**
