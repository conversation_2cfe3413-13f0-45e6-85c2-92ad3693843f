/**
 * @summary qualifier
 * @memberOf Qualifiers.ChromeSubSampling
 */
declare function chroma444(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ChromeSubSampling
 */
declare function chroma420(): number;
/**
 * @description Contains functions to select the chroma subsampling setting.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/image_optimization#toggle_chroma_subsampling|Toggling chroma subsampling}
 * @memberOf Qualifiers
 * @namespace ChromeSubSampling
 * @see To be used in {@link Actions.Delivery|Delivery} action (Quality)
 * @see To be used in {@link Actions.Delivery.DeliveryQualityAction|Quality Action} class
 */
declare const ChromaSubSampling: {
    chroma444: typeof chroma444;
    chroma420: typeof chroma420;
};
export { ChromaSubSampling, chroma420, chroma444 };
