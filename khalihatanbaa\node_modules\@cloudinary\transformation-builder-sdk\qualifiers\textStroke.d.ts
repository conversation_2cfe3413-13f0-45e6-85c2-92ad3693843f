import { SystemColors } from "./color.js";
import { ExpressionQualifier } from "./expression/ExpressionQualifier.js";
/**
 * @description Contains function to set the outline stroke.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/layers#text_layer_options|Adding text overlays to images}
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_layers#text_layer_options|Adding text overlays to videos}
 * @memberOf Qualifiers
 * @namespace TextAlignment
 * @see To be used with {@link Qualifiers.TextStyle|Text Style}
 */
/**
 * @summary qualifier Adding a Border-like qualifier to the same action.
 * @memberOf Qualifiers.Stroke
 * @param {number|string|ExpressionQualifier} width The width in pixels.
 * @param {number|string|SystemColors} color The color of the border.
 */
declare function solid(width: number | string | ExpressionQualifier, color: number | string | SystemColors): string;
declare const Stroke: {
    solid: typeof solid;
};
export { Stroke, solid };
