@import url("https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap");
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #1e293b;

  /* ألوان المنصة */
  --primary-50: #fef2f2;
  --primary-100: #fee2e2;
  --primary-200: #fecaca;
  --primary-300: #fca5a5;
  --primary-400: #f87171;
  --primary-500: #ff4c4c;
  --primary-600: #dc2626;
  --primary-700: #b91c1c;
  --primary-800: #991b1b;
  --primary-900: #7f1d1d;

  --secondary-50: #f5f3ff;
  --secondary-100: #ede9fe;
  --secondary-200: #ddd6fe;
  --secondary-300: #c4b5fd;
  --secondary-400: #a78bfa;
  --secondary-500: #7c3aed;
  --secondary-600: #7c3aed;
  --secondary-700: #6d28d9;
  --secondary-800: #5b21b6;
  --secondary-900: #4c1d95;

  --dark-50: #f8fafc;
  --dark-100: #f1f5f9;
  --dark-200: #e2e8f0;
  --dark-300: #cbd5e1;
  --dark-400: #94a3b8;
  --dark-500: #64748b;
  --dark-600: #475569;
  --dark-700: #334155;
  --dark-800: #1e293b;
  --dark-900: #0f172a;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* ألوان مخصصة */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);

  --color-secondary-50: var(--secondary-50);
  --color-secondary-100: var(--secondary-100);
  --color-secondary-200: var(--secondary-200);
  --color-secondary-300: var(--secondary-300);
  --color-secondary-400: var(--secondary-400);
  --color-secondary-500: var(--secondary-500);
  --color-secondary-600: var(--secondary-600);
  --color-secondary-700: var(--secondary-700);
  --color-secondary-800: var(--secondary-800);
  --color-secondary-900: var(--secondary-900);

  --color-dark-50: var(--dark-50);
  --color-dark-100: var(--dark-100);
  --color-dark-200: var(--dark-200);
  --color-dark-300: var(--dark-300);
  --color-dark-400: var(--dark-400);
  --color-dark-500: var(--dark-500);
  --color-dark-600: var(--dark-600);
  --color-dark-700: var(--dark-700);
  --color-dark-800: var(--dark-800);
  --color-dark-900: var(--dark-900);

  /* خطوط عربية */
  --font-arabic: "Tajawal", "Cairo", sans-serif;
  --font-arabic-heading: "Cairo", "Tajawal", sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;
  }
}

/* إعدادات RTL */
html {
  direction: rtl;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Tajawal", "Cairo", Arial, sans-serif;
  direction: rtl;
  text-align: right;
}

/* تحسينات للنصوص العربية */
* {
  font-feature-settings: "kern" 1;
  text-rendering: optimizeLegibility;
}

/* إعدادات خاصة للعناصر التفاعلية */
button,
input,
textarea,
select {
  font-family: inherit;
}

/* تحسين عرض الأرقام العربية */
.arabic-numbers {
  font-variant-numeric: tabular-nums;
}
