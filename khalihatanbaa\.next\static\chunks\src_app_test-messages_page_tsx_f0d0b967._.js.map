{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/test-messages/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\n\nexport default function TestMessagesPage() {\n  const { data: session, status } = useSession();\n  const [messages, setMessages] = useState([]);\n  const [conversations, setConversations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      fetchData();\n    }\n  }, [status]);\n\n  const fetchData = async () => {\n    try {\n      // جلب المحادثات\n      const conversationsResponse = await fetch(\"/api/messages\");\n      const conversationsData = await conversationsResponse.json();\n      \n      console.log(\"Conversations API Response:\", conversationsData);\n      \n      if (conversationsData.success) {\n        setConversations(conversationsData.data);\n      } else {\n        setError(\"خطأ في جلب المحادثات: \" + conversationsData.error);\n      }\n\n      // جلب الرسائل مع مستخدم محدد\n      if (conversationsData.data && conversationsData.data.length > 0) {\n        const firstConversation = conversationsData.data[0];\n        const messagesResponse = await fetch(`/api/messages?with=${firstConversation.otherUser.id}`);\n        const messagesData = await messagesResponse.json();\n        \n        console.log(\"Messages API Response:\", messagesData);\n        \n        if (messagesData.success) {\n          setMessages(messagesData.data);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching data:\", error);\n      setError(\"خطأ في الاتصال: \" + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (status === \"loading\") {\n    return <div className=\"p-8\">جاري تحميل الجلسة...</div>;\n  }\n\n  if (status === \"unauthenticated\") {\n    return (\n      <div className=\"p-8\">\n        <h1 className=\"text-2xl font-bold mb-4\">اختبار الرسائل</h1>\n        <p>يجب تسجيل الدخول أولاً</p>\n        <a href=\"/auth/login\" className=\"text-blue-500 underline\">\n          تسجيل الدخول\n        </a>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return <div className=\"p-8\">جاري التحميل...</div>;\n  }\n\n  return (\n    <div className=\"p-8 max-w-4xl mx-auto\">\n      <h1 className=\"text-2xl font-bold mb-6\">اختبار الرسائل</h1>\n      \n      <div className=\"mb-4 p-4 bg-blue-50 rounded\">\n        <h3 className=\"font-semibold\">معلومات الجلسة:</h3>\n        <p>المستخدم: {session?.user?.name}</p>\n        <p>البريد الإلكتروني: {session?.user?.email}</p>\n        <p>المعرف: {session?.user?.id}</p>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded text-red-700\">\n          {error}\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* المحادثات */}\n        <div>\n          <h2 className=\"text-xl font-semibold mb-4\">المحادثات ({conversations.length})</h2>\n          <div className=\"space-y-2\">\n            {conversations.length === 0 ? (\n              <p className=\"text-gray-500\">لا توجد محادثات</p>\n            ) : (\n              conversations.map((conversation, index) => (\n                <div key={index} className=\"p-3 border rounded bg-gray-50\">\n                  <p className=\"font-medium\">{conversation.otherUser?.name || \"مستخدم غير معروف\"}</p>\n                  <p className=\"text-sm text-gray-600\">{conversation.content}</p>\n                  {conversation.ad && (\n                    <p className=\"text-xs text-blue-600\">الإعلان: {conversation.ad.title}</p>\n                  )}\n                  <p className=\"text-xs text-gray-400\">\n                    {new Date(conversation.createdAt).toLocaleString(\"ar-SY\")}\n                  </p>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n\n        {/* الرسائل */}\n        <div>\n          <h2 className=\"text-xl font-semibold mb-4\">الرسائل ({messages.length})</h2>\n          <div className=\"space-y-2\">\n            {messages.length === 0 ? (\n              <p className=\"text-gray-500\">لا توجد رسائل</p>\n            ) : (\n              messages.map((message, index) => (\n                <div key={index} className=\"p-3 border rounded bg-gray-50\">\n                  <p className=\"font-medium\">\n                    من: {message.from?.name || \"مستخدم غير معروف\"}\n                  </p>\n                  <p className=\"font-medium\">\n                    إلى: {message.to?.name || \"مستخدم غير معروف\"}\n                  </p>\n                  <p className=\"text-sm\">{message.content}</p>\n                  <p className=\"text-xs text-gray-400\">\n                    {new Date(message.createdAt).toLocaleString(\"ar-SY\")}\n                  </p>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"mt-8\">\n        <button\n          onClick={fetchData}\n          className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n        >\n          إعادة تحميل البيانات\n        </button>\n      </div>\n\n      {/* عرض البيانات الخام */}\n      <div className=\"mt-8\">\n        <h3 className=\"text-lg font-semibold mb-2\">البيانات الخام:</h3>\n        <details className=\"mb-4\">\n          <summary className=\"cursor-pointer font-medium\">المحادثات (JSON)</summary>\n          <pre className=\"mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto\">\n            {JSON.stringify(conversations, null, 2)}\n          </pre>\n        </details>\n        <details>\n          <summary className=\"cursor-pointer font-medium\">الرسائل (JSON)</summary>\n          <pre className=\"mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto\">\n            {JSON.stringify(messages, null, 2)}\n          </pre>\n        </details>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW,iBAAiB;gBAC9B;YACF;QACF;qCAAG;QAAC;KAAO;IAEX,MAAM,YAAY;QAChB,IAAI;YACF,gBAAgB;YAChB,MAAM,wBAAwB,MAAM,MAAM;YAC1C,MAAM,oBAAoB,MAAM,sBAAsB,IAAI;YAE1D,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,iBAAiB,kBAAkB,IAAI;YACzC,OAAO;gBACL,SAAS,2BAA2B,kBAAkB,KAAK;YAC7D;YAEA,6BAA6B;YAC7B,IAAI,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC/D,MAAM,oBAAoB,kBAAkB,IAAI,CAAC,EAAE;gBACnD,MAAM,mBAAmB,MAAM,MAAM,CAAC,mBAAmB,EAAE,kBAAkB,SAAS,CAAC,EAAE,EAAE;gBAC3F,MAAM,eAAe,MAAM,iBAAiB,IAAI;gBAEhD,QAAQ,GAAG,CAAC,0BAA0B;gBAEtC,IAAI,aAAa,OAAO,EAAE;oBACxB,YAAY,aAAa,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,qBAAqB,MAAM,OAAO;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC;YAAI,WAAU;sBAAM;;;;;;IAC9B;IAEA,IAAI,WAAW,mBAAmB;QAChC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;8BAAE;;;;;;8BACH,6LAAC;oBAAE,MAAK;oBAAc,WAAU;8BAA0B;;;;;;;;;;;;IAKhE;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC;YAAI,WAAU;sBAAM;;;;;;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,6LAAC;;4BAAE;4BAAW,SAAS,MAAM;;;;;;;kCAC7B,6LAAC;;4BAAE;4BAAoB,SAAS,MAAM;;;;;;;kCACtC,6LAAC;;4BAAE;4BAAS,SAAS,MAAM;;;;;;;;;;;;;YAG5B,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAA6B;oCAAY,cAAc,MAAM;oCAAC;;;;;;;0CAC5E,6LAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;oCAAE,WAAU;8CAAgB;;;;;2CAE7B,cAAc,GAAG,CAAC,CAAC,cAAc,sBAC/B,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAE,WAAU;0DAAe,aAAa,SAAS,EAAE,QAAQ;;;;;;0DAC5D,6LAAC;gDAAE,WAAU;0DAAyB,aAAa,OAAO;;;;;;4CACzD,aAAa,EAAE,kBACd,6LAAC;gDAAE,WAAU;;oDAAwB;oDAAU,aAAa,EAAE,CAAC,KAAK;;;;;;;0DAEtE,6LAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc,CAAC;;;;;;;uCAP3C;;;;;;;;;;;;;;;;kCAgBlB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAA6B;oCAAU,SAAS,MAAM;oCAAC;;;;;;;0CACrE,6LAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;oCAAE,WAAU;8CAAgB;;;;;2CAE7B,SAAS,GAAG,CAAC,CAAC,SAAS,sBACrB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAE,WAAU;;oDAAc;oDACpB,QAAQ,IAAI,EAAE,QAAQ;;;;;;;0DAE7B,6LAAC;gDAAE,WAAU;;oDAAc;oDACnB,QAAQ,EAAE,EAAE,QAAQ;;;;;;;0DAE5B,6LAAC;gDAAE,WAAU;0DAAW,QAAQ,OAAO;;;;;;0DACvC,6LAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,QAAQ,SAAS,EAAE,cAAc,CAAC;;;;;;;uCATtC;;;;;;;;;;;;;;;;;;;;;;0BAkBpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAQ,WAAU;0CAA6B;;;;;;0CAChD,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,eAAe,MAAM;;;;;;;;;;;;kCAGzC,6LAAC;;0CACC,6LAAC;gCAAQ,WAAU;0CAA6B;;;;;;0CAChD,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GAjKwB;;QACY,iJAAA,CAAA,aAAU;;;KADtB", "debugId": null}}]}