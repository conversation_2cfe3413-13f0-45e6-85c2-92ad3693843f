import VariableAction from "./VariableAction.js";
import { ExpressionQualifier } from "../../qualifiers/expression/ExpressionQualifier.js";
/**
 * @description Sets a user-defined variable.
 * @memberOf Actions.Variable
 * @extends Variable.VariableAction
 * @see Visit {@link Actions.Variable|Variable} for an example
 */
declare class SetAction extends VariableAction {
    constructor(name: string, value: number | string | string[] | number[] | ExpressionQualifier, wrapper?: string);
}
export default SetAction;
