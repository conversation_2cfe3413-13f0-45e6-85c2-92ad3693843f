import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 بدء إضافة البيانات التجريبية...");

  // إنشاء مستخدمين تجريبيين
  const hashedPassword = await bcrypt.hash("123456", 12);

  const user1 = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      name: "أحمد محمد",
      email: "<EMAIL>",
      phone: "+963991234567",
      passwordHash: hashedPassword,
      freeAdsExpiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 يوم
      ratingAverage: 4.5,
      ratingCount: 12,
    },
  });

  const user2 = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      name: "سارة أحمد",
      email: "<EMAIL>",
      phone: "+963991234568",
      passwordHash: hashedPassword,
      freeAdsCount: 2,
      freeAdsExpiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
      ratingAverage: 4.8,
      ratingCount: 8,
    },
  });

  const user3 = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      name: "عمر خالد",
      email: "<EMAIL>",
      phone: "+963991234569",
      passwordHash: hashedPassword,
      freeAdsCount: 1,
      freeAdsExpiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
      ratingAverage: 4.2,
      ratingCount: 5,
    },
  });

  // إنشاء مستخدم مدير
  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      name: "مدير الموقع",
      email: "<EMAIL>",
      phone: "+963991234560",
      passwordHash: hashedPassword,
      role: "admin",
      freeAdsCount: 0,
      paidAdsCount: 100,
      paidAdsExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // سنة
      ratingAverage: 5.0,
      ratingCount: 1,
    },
  });

  console.log("✅ تم إنشاء المستخدمين");

  // إنشاء إعلانات تجريبية
  const ads = [
    {
      title: "تويوتا كامري 2018 فل كامل",
      description: `سيارة تويوتا كامري موديل 2018 بحالة ممتازة جداً

المواصفات:
- فل كامل (جلد، فتحة سقف، شاشة، كاميرا خلفية)
- صيانة دورية في الوكالة
- لون أبيض لؤلؤي
- عدد الكيلومترات: 85,000 كم
- جميع الأوراق سليمة
- فحص كامل متاح

السيارة بحالة ممتازة ولا تحتاج أي مصاريف. جاهزة للاستخدام فوراً.

للجادين فقط، يرجى التواصل عبر الرسائل أولاً.`,
      price: 45000000,
      category: "سيارات",
      subCategory: "تويوتا",
      condition: "مستعمل",
      city: "دمشق",
      region: "المزة",
      addressDetail: "قريب من جامع المزة",
      imageUrls: [],
      specifications: {
        year: 2018,
        mileage: 85000,
        fuel: "بنزين",
        color: "أبيض لؤلؤي",
        transmission: "أوتوماتيك",
        engine: "2.5 لتر",
      },
      views: 125,
      isPromoted: true,
      userId: user1.id,
    },
    {
      title: "شقة 3 غرف في المالكي",
      description: `شقة مفروشة بالكامل في منطقة المالكي الراقية

المواصفات:
- 3 غرف نوم واسعة
- 2 حمام
- صالون كبير
- مطبخ مجهز بالكامل
- بلكونة مع إطلالة رائعة
- الطابق الثالث مع مصعد
- قريبة من المدارس والجامعات
- موقف سيارة

الشقة جاهزة للسكن فوراً، مفروشة بالكامل بأثاث حديث وأجهزة كهربائية.`,
      price: 120000000,
      category: "عقارات",
      subCategory: "شقة",
      condition: "مستعمل",
      city: "دمشق",
      region: "المالكي",
      imageUrls: [],
      specifications: {
        rooms: 3,
        bathrooms: 2,
        area: 120,
        floor: 3,
        furnished: true,
        parking: true,
      },
      views: 89,
      isFreeAd: true,
      userId: user2.id,
    },
    {
      title: "آيفون 14 برو ماكس 256 جيجا",
      description: `جهاز آيفون 14 برو ماكس جديد لم يستعمل

المواصفات:
- 256 جيجا ذاكرة تخزين
- لون أسود
- مع الكرتونة الأصلية
- الشاحن الأصلي
- سماعات أصلية
- ضمان سنة من Apple

الجهاز جديد تماماً، تم شراؤه من الوكالة الرسمية ولم يستعمل إطلاقاً.`,
      price: 8500000,
      category: "إلكترونيات",
      subCategory: "هاتف ذكي",
      condition: "جديد",
      city: "حلب",
      region: "الفرقان",
      imageUrls: [],
      specifications: {
        brand: "Apple",
        model: "iPhone 14 Pro Max",
        storage: "256GB",
        color: "أسود",
        warranty: "سنة",
      },
      views: 234,
      isPromoted: true,
      userId: user3.id,
    },
    {
      title: "طقم غرفة نوم خشب زان",
      description: `طقم غرفة نوم كامل من خشب الزان الطبيعي

يتضمن:
- سرير مزدوج مع مرتبة
- دولاب 4 أبواب
- تسريحة مع مرآة
- 2 كومودينو

الطقم بحالة ممتازة، خشب زان طبيعي عالي الجودة. مناسب لغرفة نوم رئيسية.`,
      price: 15000000,
      category: "أثاث",
      subCategory: "غرفة نوم",
      condition: "مستعمل",
      city: "حمص",
      region: "الوعر",
      imageUrls: [],
      specifications: {
        material: "خشب زان",
        pieces: 5,
        condition: "ممتاز",
        bedSize: "مزدوج",
      },
      views: 67,
      isFreeAd: true,
      userId: user1.id,
    },
    {
      title: "لابتوب ديل XPS 13 للبرمجة",
      description: `لابتوب ديل XPS 13 مثالي للبرمجة والتصميم

المواصفات:
- معالج Intel Core i7 الجيل 11
- ذاكرة عشوائية 16 جيجا
- قرص صلب SSD 512 جيجا
- شاشة 13.3 بوصة 4K
- كرت شاشة Intel Iris Xe
- نظام Windows 11 Pro

اللابتوب بحالة ممتازة، استعمال خفيف للبرمجة فقط.`,
      price: 12000000,
      category: "إلكترونيات",
      subCategory: "لابتوب",
      condition: "مستعمل",
      city: "دمشق",
      region: "الشعلان",
      imageUrls: [],
      specifications: {
        brand: "Dell",
        model: "XPS 13",
        processor: "Intel Core i7",
        ram: "16GB",
        storage: "512GB SSD",
        screen: '13.3" 4K',
      },
      views: 156,
      userId: user2.id,
    },
  ];

  // إنشاء الإعلانات وجلب معرفاتها
  const createdAds = [];
  for (const adData of ads) {
    const createdAd = await prisma.ad.create({
      data: adData,
    });
    createdAds.push(createdAd);
  }

  console.log("✅ تم إنشاء الإعلانات");

  // إنشاء بعض المفضلة التجريبية
  await prisma.favorite.createMany({
    data: [
      { userId: user1.id, adId: createdAds[1].id }, // أحمد يحب الشقة
      { userId: user2.id, adId: createdAds[0].id }, // سارة تحب السيارة
      { userId: user3.id, adId: createdAds[3].id }, // عمر يحب الأثاث
    ],
  });

  console.log("✅ تم إنشاء المفضلة");

  // إنشاء بعض الرسائل التجريبية;

  await prisma.message.createMany({
    data: [
      {
        fromId: user2.id,
        toId: user1.id,
        adId: createdAds[0].id,
        content: "مرحبا، هل السيارة متاحة للمعاينة؟",
      },
      {
        fromId: user1.id,
        toId: user2.id,
        adId: createdAds[0].id,
        content: "أهلاً وسهلاً، نعم السيارة متاحة. متى تريد المعاينة؟",
      },
      {
        fromId: user2.id,
        toId: user1.id,
        adId: createdAds[0].id,
        content: "ممكن غداً في المساء؟",
      },
      {
        fromId: user3.id,
        toId: user2.id,
        adId: createdAds[1].id,
        content: "السلام عليكم، هل الشقة مفروشة بالكامل؟",
      },
      {
        fromId: user2.id,
        toId: user3.id,
        adId: createdAds[1].id,
        content: "وعليكم السلام، نعم مفروشة بالكامل وجاهزة للسكن",
      },
    ],
  });

  console.log("✅ تم إنشاء الرسائل");

  // إنشاء بعض التقييمات التجريبية
  await prisma.rating.createMany({
    data: [
      {
        userId: user2.id,
        sellerId: user1.id,
        rating: 5,
        comment: "بائع ممتاز، صادق في التعامل والسيارة كما وُصفت تماماً",
      },
      {
        userId: user3.id,
        sellerId: user2.id,
        rating: 4,
        comment: "تعامل جيد، الشقة نظيفة ومرتبة",
      },
      {
        userId: user1.id,
        sellerId: user3.id,
        rating: 5,
        comment: "سرعة في الرد وتعامل محترم",
      },
    ],
  });

  // تحديث متوسط التقييمات للمستخدمين
  for (const user of [user1, user2, user3]) {
    const avgRating = await prisma.rating.aggregate({
      where: { sellerId: user.id },
      _avg: { rating: true },
      _count: { rating: true },
    });

    await prisma.user.update({
      where: { id: user.id },
      data: {
        ratingAverage: avgRating._avg.rating || 0,
        ratingCount: avgRating._count.rating || 0,
      },
    });
  }

  // إضافة إعلانات مدفوعة ومميزة
  const paidAd = await prisma.ad.create({
    data: {
      title: "شقة فاخرة للبيع في دمشق الجديدة",
      description:
        "شقة 3 غرف نوم، 2 حمام، صالون، مطبخ، بلكونة. إطلالة رائعة، تشطيب سوبر ديلوكس، موقع مميز قريب من الخدمات.",
      price: 85000000,
      category: "عقارات",
      subCategory: "شقة",
      condition: "جديد",
      city: "دمشق",
      region: "دمشق الجديدة",
      addressDetail: "شارع الثورة، بناء حديث",
      imageUrls: [],
      specifications: {
        المساحة: "120 متر مربع",
        الطابق: "الثالث",
        "عدد الغرف": "3",
        "عدد الحمامات": "2",
        التدفئة: "مركزية",
        المصعد: "متوفر",
      },
      views: 45,
      contactsCount: 8,
      adType: "paid",
      isFreeAd: false,
      isPromoted: false,
      expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 يوم
      publishedAt: new Date(),
      userId: user2.id,
    },
  });

  const promotedAd = await prisma.ad.create({
    data: {
      title: "سيارة BMW X5 موديل 2020 للبيع",
      description:
        "سيارة BMW X5 بحالة ممتازة، فحص كامل، صيانة دورية، لون أسود، جلد طبيعي، شاشة تاتش، كاميرا خلفية، حساسات.",
      price: 45000000,
      category: "سيارات",
      subCategory: "BMW",
      condition: "مستعمل",
      city: "دمشق",
      region: "المزة",
      addressDetail: "قريب من المزة 86",
      imageUrls: [],
      specifications: {
        الموديل: "2020",
        الكيلومترات: "35000 كم",
        "نوع الوقود": "بنزين",
        "ناقل الحركة": "أوتوماتيك",
        اللون: "أسود",
        "عدد الأبواب": "5",
      },
      views: 120,
      contactsCount: 25,
      adType: "promoted",
      isFreeAd: false,
      isPromoted: true,
      promotedUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // أسبوع
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم
      publishedAt: new Date(),
      userId: user1.id,
    },
  });

  // تحديث عدادات المستخدمين
  await prisma.user.update({
    where: { id: user2.id },
    data: {
      paidAdsUsed: 1,
      totalViews: 45,
      totalContacts: 8,
    },
  });

  await prisma.user.update({
    where: { id: user1.id },
    data: {
      promotedAdsUsed: 1,
      totalViews: 120,
      totalContacts: 25,
    },
  });

  console.log("✅ تم إنشاء التقييمات");

  console.log("🎉 تم الانتهاء من إضافة البيانات التجريبية!");
  console.log("\n📊 ملخص البيانات المضافة:");
  console.log(`👥 المستخدمين: ${await prisma.user.count()}`);
  console.log(`📢 الإعلانات: ${await prisma.ad.count()}`);
  console.log(`💬 الرسائل: ${await prisma.message.count()}`);
  console.log(`❤️ المفضلة: ${await prisma.favorite.count()}`);
  console.log(`⭐ التقييمات: ${await prisma.rating.count()}`);
  console.log("\n🔑 بيانات تسجيل الدخول:");
  console.log("البريد الإلكتروني: <EMAIL>");
  console.log("كلمة المرور: 123456");
  console.log("\nأو للمدير:");
  console.log("البريد الإلكتروني: <EMAIL>");
  console.log("كلمة المرور: 123456");
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
