import { Dither, checkerboard2x1Dither, circles5x5Black, circles5x5White, circles6x6Black, circles6x6White, circles7x7Black, circles7x7White, halftone4x4Angled, halftone4x4Orthogonal, halftone6x6Angled, halftone6x6Orthogonal, halftone8x8Angled, halftone8x8Orthogonal, halftone16x16Orthogonal, ordered2x2Dispersed, ordered3x3Dispersed, ordered4x4Dispersed, ordered8x8Dispersed, threshold1x1Nondither } from "@cloudinary/transformation-builder-sdk/qualifiers/dither";
export { Dither, checkerboard2x1Dither, circles5x5Black, circles5x5White, circles6x6Black, circles6x6White, circles7x7Black, circles7x7White, halftone4x4Angled, halftone4x4Orthogonal, halftone6x6Angled, halftone6x6Orthogonal, halftone8x8Angled, halftone8x8Orthogonal, halftone16x16Orthogonal, ordered2x2Dispersed, ordered3x3Dispersed, ordered4x4Dispersed, ordered8x8Dispersed, threshold1x1Nondither };
