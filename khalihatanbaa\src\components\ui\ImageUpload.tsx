"use client";

import { useState, useRef } from "react";
import { CldUploadWidget } from "next-cloudinary";
import {
  PhotoIcon,
  XMarkIcon,
  PlusIcon,
  ArrowUpTrayIcon,
} from "@heroicons/react/24/outline";

interface ImageUploadProps {
  value: string[];
  onChange: (urls: string[]) => void;
  maxImages?: number;
  disabled?: boolean;
}

export function ImageUpload({
  value = [],
  onChange,
  maxImages = 5,
  disabled = false,
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);

  const handleUpload = (result: any) => {
    if (result.event === "success") {
      const newUrls = [...value, result.info.secure_url];
      onChange(newUrls);
      setUploading(false);
    }
  };

  const removeImage = (index: number) => {
    const newUrls = value.filter((_, i) => i !== index);
    onChange(newUrls);
  };

  const canAddMore = value.length < maxImages;

  return (
    <div className="space-y-4">
      {/* عرض الصور المرفوعة */}
      {value.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {value.map((url, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={url}
                  alt={`صورة ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* زر الحذف */}
              <button
                type="button"
                onClick={() => removeImage(index)}
                disabled={disabled}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity disabled:opacity-50"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>

              {/* رقم الصورة */}
              <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                {index + 1}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* زر إضافة صور */}
      {canAddMore && (
        <CldUploadWidget
          uploadPreset="khalihatanbaa_preset"
          options={{
            maxFiles: 1,
            resourceType: "image",
            clientAllowedFormats: ["jpg", "jpeg", "png", "webp"],
            maxFileSize: 5000000, // 5MB
            folder: "khalihatanbaa/ads",
            transformation: [
              { width: 800, height: 600, crop: "limit" },
              { quality: "auto" },
              { format: "auto" },
            ],
          }}
          onUpload={handleUpload}
          onOpen={() => setUploading(true)}
          onClose={() => setUploading(false)}
        >
          {({ open }) => (
            <button
              type="button"
              onClick={() => open()}
              disabled={disabled || uploading}
              className="w-full border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="flex flex-col items-center">
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-3"></div>
                    <p className="text-sm text-gray-600">جاري رفع الصورة...</p>
                  </>
                ) : (
                  <>
                    <PhotoIcon className="h-12 w-12 text-gray-400 mb-3" />
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      اضغط لإضافة صورة
                    </p>
                    <p className="text-xs text-gray-500">
                      PNG, JPG, WEBP حتى 5MB
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {value.length} من {maxImages} صور
                    </p>
                  </>
                )}
              </div>
            </button>
          )}
        </CldUploadWidget>
      )}

      {/* رسالة عند الوصول للحد الأقصى */}
      {!canAddMore && (
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600">
            تم الوصول للحد الأقصى من الصور ({maxImages} صور)
          </p>
        </div>
      )}

      {/* نصائح */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 mb-2">
          نصائح للحصول على أفضل النتائج:
        </h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• استخدم صور واضحة وعالية الجودة</li>
          <li>• اعرض المنتج من زوايا مختلفة</li>
          <li>• تأكد من الإضاءة الجيدة</li>
          <li>• الصورة الأولى ستكون الصورة الرئيسية</li>
        </ul>
      </div>
    </div>
  );
}

// مكون مبسط لعرض الصور فقط
export function ImageGallery({ images }: { images: string[] }) {
  const [currentImage, setCurrentImage] = useState(0);

  if (!images || images.length === 0) {
    return (
      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
        <PhotoIcon className="h-16 w-16 text-gray-400" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* الصورة الرئيسية */}
      <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
        <img
          src={images[currentImage]}
          alt={`صورة ${currentImage + 1}`}
          className="w-full h-full object-cover"
        />
      </div>

      {/* الصور المصغرة */}
      {images.length > 1 && (
        <div className="flex space-x-2 space-x-reverse overflow-x-auto">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => setCurrentImage(index)}
              className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${
                currentImage === index
                  ? "border-primary-500"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <img
                src={image}
                alt={`صورة ${index + 1}`}
                className="w-full h-full object-cover"
              />
            </button>
          ))}
        </div>
      )}

      {/* مؤشر الصور */}
      {images.length > 1 && (
        <div className="text-center">
          <span className="text-sm text-gray-500">
            {currentImage + 1} من {images.length}
          </span>
        </div>
      )}
    </div>
  );
}
