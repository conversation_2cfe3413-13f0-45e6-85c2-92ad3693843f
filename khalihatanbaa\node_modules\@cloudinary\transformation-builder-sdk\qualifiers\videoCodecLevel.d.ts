/**
 * @description Contains functions to select the video codec level.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_manipulation_and_delivery#video_codec_settings|Video codec settings}
 * @memberOf Qualifiers
 * @namespace VideoCodecLevel
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl30(): '3.0';
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl31(): 3.1;
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl40(): '4.0';
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl41(): 4.1;
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl42(): 4.2;
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl50(): '5.0';
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl51(): 5.1;
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
declare function vcl52(): 5.2;
declare const VideoCodecLevel: {
    vcl30: typeof vcl30;
    vcl31: typeof vcl31;
    vcl40: typeof vcl40;
    vcl41: typeof vcl41;
    vcl42: typeof vcl42;
    vcl50: typeof vcl50;
    vcl51: typeof vcl51;
    vcl52: typeof vcl52;
};
export { vcl30, vcl31, vcl40, vcl41, vcl42, vcl50, vcl51, vcl52, VideoCodecLevel };
