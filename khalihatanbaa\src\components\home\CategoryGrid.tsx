import Link from 'next/link'
import { CATEGORIES } from '@/types'

export function CategoryGrid() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-dark-800 mb-4">
            تصفح حسب الفئة
          </h2>
          <p className="text-lg text-dark-600 max-w-2xl mx-auto">
            اختر الفئة التي تهمك واعثر على أفضل العروض المتاحة
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          {Object.entries(CATEGORIES).map(([key, category]) => (
            <Link
              key={key}
              href={`/search?category=${key}`}
              className="group bg-white rounded-2xl border border-gray-200 p-6 hover:border-primary-500 hover:shadow-lg transition-all duration-300"
            >
              <div className="text-center">
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {category.icon}
                </div>
                <h3 className="font-semibold text-dark-800 group-hover:text-primary-500 transition-colors">
                  {category.name}
                </h3>
                <p className="text-sm text-dark-500 mt-1">
                  {category.subCategories.length} فئة فرعية
                </p>
              </div>
            </Link>
          ))}
        </div>

        {/* رابط عرض جميع الفئات */}
        <div className="text-center mt-12">
          <Link
            href="/categories"
            className="inline-flex items-center px-6 py-3 border border-primary-500 text-primary-500 rounded-lg hover:bg-primary-500 hover:text-white transition-colors"
          >
            عرض جميع الفئات
            <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
}
