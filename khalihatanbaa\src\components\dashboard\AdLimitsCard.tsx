"use client";

import { useState, useEffect } from "react";
import {
  ClockIcon,
  PlusIcon,
  StarIcon,
  ChartBarIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";

interface AdLimits {
  freeAds: {
    total: number;
    used: number;
    available: number;
    expiresAt?: string;
  };
  paidAds: {
    total: number;
    used: number;
    available: number;
    expiresAt?: string;
  };
  promotedAds: {
    total: number;
    used: number;
    available: number;
    expiresAt?: string;
  };
  totalActiveAds: number;
  totalViews: number;
  totalContacts: number;
  canCreateFreeAd: boolean;
  canCreatePaidAd: boolean;
  canCreatePromotedAd: boolean;
}

export function AdLimitsCard() {
  const [adLimits, setAdLimits] = useState<AdLimits | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAdLimits();
  }, []);

  const fetchAdLimits = async () => {
    try {
      const response = await fetch("/api/ads/limits");
      const data = await response.json();

      if (data.success) {
        setAdLimits(data.data);
      }
    } catch (error) {
      console.error("Error fetching ad limits:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "غير محدد";
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return "منتهي الصلاحية";
    if (diffDays === 0) return "ينتهي اليوم";
    if (diffDays === 1) return "ينتهي غداً";
    return `${diffDays} يوم متبقي`;
  };

  const getProgressPercentage = (used: number, total: number) => {
    if (total === 0) return 0;
    return Math.min((used / total) * 100, 100);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!adLimits) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
        <p className="text-gray-500 text-center">
          لا يمكن تحميل بيانات الإعلانات
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-dark-800">
          إحصائيات الإعلانات
        </h2>
        <ChartBarIcon className="h-5 w-5 text-gray-400" />
      </div>

      {/* الإحصائيات العامة */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="text-2xl font-bold text-blue-700">
            {adLimits.totalActiveAds}
          </div>
          <div className="text-sm text-blue-700 font-medium">إعلان نشط</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
          <div className="text-2xl font-bold text-green-700">
            {adLimits.totalViews.toLocaleString()}
          </div>
          <div className="text-sm text-green-700 font-medium">مشاهدة</div>
        </div>
        <div className="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
          <div className="text-2xl font-bold text-purple-700">
            {adLimits.totalContacts}
          </div>
          <div className="text-sm text-purple-700 font-medium">تواصل</div>
        </div>
      </div>

      {/* حدود الإعلانات */}
      <div className="space-y-4">
        {/* الإعلانات المجانية */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <ClockIcon className="h-5 w-5 text-green-500 ml-2" />
              <span className="font-medium text-dark-700">إعلانات مجانية</span>
            </div>
            <div
              className={`w-3 h-3 rounded-full ${
                adLimits.canCreateFreeAd ? "bg-green-500" : "bg-gray-400"
              }`}
            ></div>
          </div>

          <div className="mb-2">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>
                {adLimits.freeAds.used} من {adLimits.freeAds.total} مستخدم
              </span>
              <span>{adLimits.freeAds.available} متبقي</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${getProgressPercentage(
                    adLimits.freeAds.used,
                    adLimits.freeAds.total
                  )}%`,
                }}
              ></div>
            </div>
          </div>

          <p className="text-xs text-gray-500">
            {formatDate(adLimits.freeAds.expiresAt)}
          </p>
        </div>

        {/* الإعلانات المدفوعة */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <PlusIcon className="h-5 w-5 text-blue-500 ml-2" />
              <span className="font-medium text-dark-700">إعلانات مدفوعة</span>
            </div>
            <div
              className={`w-3 h-3 rounded-full ${
                adLimits.canCreatePaidAd ? "bg-blue-500" : "bg-gray-400"
              }`}
            ></div>
          </div>

          <div className="mb-2">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>
                {adLimits.paidAds.used} من {adLimits.paidAds.total} مستخدم
              </span>
              <span>{adLimits.paidAds.available} متبقي</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${getProgressPercentage(
                    adLimits.paidAds.used,
                    adLimits.paidAds.total
                  )}%`,
                }}
              ></div>
            </div>
          </div>

          <p className="text-xs text-gray-500">
            {formatDate(adLimits.paidAds.expiresAt)}
          </p>
        </div>

        {/* الإعلانات المميزة */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <StarIcon className="h-5 w-5 text-yellow-500 ml-2" />
              <span className="font-medium text-dark-700">إعلانات مميزة</span>
            </div>
            <div
              className={`w-3 h-3 rounded-full ${
                adLimits.canCreatePromotedAd ? "bg-yellow-500" : "bg-gray-400"
              }`}
            ></div>
          </div>

          <div className="mb-2">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>
                {adLimits.promotedAds.used} من {adLimits.promotedAds.total}{" "}
                مستخدم
              </span>
              <span>{adLimits.promotedAds.available} متبقي</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${getProgressPercentage(
                    adLimits.promotedAds.used,
                    adLimits.promotedAds.total
                  )}%`,
                }}
              ></div>
            </div>
          </div>

          <p className="text-xs text-gray-500">
            {formatDate(adLimits.promotedAds.expiresAt)}
          </p>
        </div>
      </div>

      {/* أزرار الإجراءات */}
      <div className="mt-6 space-y-2">
        <a
          href="/ads/create"
          className="w-full bg-primary-500 text-white py-2 px-4 rounded-lg hover:bg-primary-600 transition-colors text-center block"
        >
          إنشاء إعلان جديد
        </a>

        {!adLimits.canCreateFreeAd &&
          !adLimits.canCreatePaidAd &&
          !adLimits.canCreatePromotedAd && (
            <a
              href="/packages"
              className="w-full border border-primary-500 text-primary-500 py-2 px-4 rounded-lg hover:bg-primary-50 transition-colors text-center block"
            >
              شراء باقة إعلانات
            </a>
          )}
      </div>
    </div>
  );
}
