import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// مخطط التحقق من إضافة/إزالة المفضلة
const favoriteSchema = z.object({
  adId: z.string().min(1, 'معرف الإعلان مطلوب')
})

// جلب المفضلة
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const skip = (page - 1) * limit

    // جلب المفضلة مع تفاصيل الإعلانات
    const favorites = await prisma.favorite.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        ad: {
          include: {
            user: {
              select: { id: true, name: true, avatar: true }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    })

    // حساب العدد الإجمالي
    const total = await prisma.favorite.count({
      where: {
        userId: session.user.id
      }
    })

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: favorites,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    })

  } catch (error) {
    console.error('Error fetching favorites:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب المفضلة' },
      { status: 500 }
    )
  }
}

// إضافة إلى المفضلة
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = favoriteSchema.parse(body)

    // التحقق من وجود الإعلان
    const ad = await prisma.ad.findUnique({
      where: { id: validatedData.adId }
    })

    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود الإعلان في المفضلة مسبقاً
    const existingFavorite = await prisma.favorite.findUnique({
      where: {
        userId_adId: {
          userId: session.user.id,
          adId: validatedData.adId
        }
      }
    })

    if (existingFavorite) {
      return NextResponse.json(
        { success: false, error: 'الإعلان موجود في المفضلة بالفعل' },
        { status: 400 }
      )
    }

    // إضافة إلى المفضلة
    const favorite = await prisma.favorite.create({
      data: {
        userId: session.user.id,
        adId: validatedData.adId
      },
      include: {
        ad: {
          include: {
            user: {
              select: { id: true, name: true, avatar: true }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: favorite,
      message: 'تم إضافة الإعلان إلى المفضلة'
    })

  } catch (error) {
    console.error('Error adding to favorites:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'حدث خطأ في إضافة الإعلان إلى المفضلة' },
      { status: 500 }
    )
  }
}

// إزالة من المفضلة
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const adId = searchParams.get('adId')

    if (!adId) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان مطلوب' },
        { status: 400 }
      )
    }

    // البحث عن المفضلة
    const favorite = await prisma.favorite.findUnique({
      where: {
        userId_adId: {
          userId: session.user.id,
          adId: adId
        }
      }
    })

    if (!favorite) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود في المفضلة' },
        { status: 404 }
      )
    }

    // إزالة من المفضلة
    await prisma.favorite.delete({
      where: {
        id: favorite.id
      }
    })

    return NextResponse.json({
      success: true,
      message: 'تم إزالة الإعلان من المفضلة'
    })

  } catch (error) {
    console.error('Error removing from favorites:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في إزالة الإعلان من المفضلة' },
      { status: 500 }
    )
  }
}
