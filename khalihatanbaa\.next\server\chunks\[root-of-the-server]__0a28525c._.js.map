{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\nimport bcrypt from \"bcryptjs\";\nimport { prisma } from \"./prisma\";\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        emailOrPhone: {\n          label: \"البريد الإلكتروني أو رقم الهاتف\",\n          type: \"text\",\n        },\n        password: { label: \"كلمة المرور\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null;\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone },\n            ],\n            isActive: true,\n          },\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        };\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.phone = user.phone;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.phone = token.phone as string;\n        session.user.avatar = token.avatar as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/auth/login\",\n    // signUp: \"/auth/register\", // غير مدعوم في NextAuth\n  },\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBACZ,OAAO;oBACP,MAAM;gBACR;gBACA,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IAEV;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/api/messages/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\nimport { prisma } from \"@/lib/prisma\";\nimport { z } from \"zod\";\n\n// مخطط التحقق من إرسال رسالة\nconst sendMessageSchema = z.object({\n  toId: z.string().min(1, \"معرف المستقبل مطلوب\"),\n  adId: z.string().optional(),\n  content: z\n    .string()\n    .min(1, \"محتوى الرسالة مطلوب\")\n    .max(1000, \"الرسالة طويلة جداً\"),\n});\n\n// جلب المحادثات\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const conversationWith = searchParams.get(\"with\");\n    const adId = searchParams.get(\"adId\");\n\n    if (conversationWith) {\n      // جلب محادثة محددة\n      const messages = await prisma.message.findMany({\n        where: {\n          OR: [\n            { fromId: session.user.id, toId: conversationWith },\n            { fromId: conversationWith, toId: session.user.id },\n          ],\n          ...(adId && { adId }),\n        },\n        include: {\n          from: {\n            select: { id: true, name: true, avatar: true },\n          },\n          to: {\n            select: { id: true, name: true, avatar: true },\n          },\n          ad: {\n            select: { id: true, title: true, price: true, imageUrls: true },\n          },\n        },\n        orderBy: { createdAt: \"asc\" },\n      });\n\n      // تحديد الرسائل كمقروءة\n      await prisma.message.updateMany({\n        where: {\n          fromId: conversationWith,\n          toId: session.user.id,\n          isRead: false,\n        },\n        data: { isRead: true },\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: messages,\n      });\n    } else {\n      // جلب قائمة المحادثات - مع حد للنتائج لتجنب تجاوز 5MB\n      const conversations = await prisma.message.findMany({\n        where: {\n          OR: [{ fromId: session.user.id }, { toId: session.user.id }],\n        },\n        include: {\n          from: {\n            select: { id: true, name: true, avatar: true },\n          },\n          to: {\n            select: { id: true, name: true, avatar: true },\n          },\n          ad: {\n            select: { id: true, title: true, price: true, imageUrls: true },\n          },\n        },\n        orderBy: { createdAt: \"desc\" },\n        take: 1000, // حد أقصى 1000 رسالة لتجنب تجاوز حد الحجم\n      });\n\n      // تجميع المحادثات حسب المستخدم والإعلان\n      const groupedConversations = conversations.reduce((acc, message) => {\n        const otherUserId =\n          message.fromId === session.user.id ? message.toId : message.fromId;\n        const key = `${otherUserId}-${message.adId || \"general\"}`;\n\n        if (!acc[key] || acc[key].createdAt < message.createdAt) {\n          acc[key] = {\n            ...message,\n            otherUser:\n              message.fromId === session.user.id ? message.to : message.from,\n            unreadCount: 0,\n          };\n        }\n\n        return acc;\n      }, {} as any);\n\n      // حساب عدد الرسائل غير المقروءة لكل محادثة\n      for (const conversation of Object.values(groupedConversations) as any[]) {\n        const unreadCount = await prisma.message.count({\n          where: {\n            fromId: conversation.otherUser.id,\n            toId: session.user.id,\n            isRead: false,\n            ...(conversation.adId && { adId: conversation.adId }),\n          },\n        });\n        conversation.unreadCount = unreadCount;\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: Object.values(groupedConversations),\n      });\n    }\n  } catch (error) {\n    console.error(\"Error fetching messages:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في جلب الرسائل\" },\n      { status: 500 }\n    );\n  }\n}\n\n// إرسال رسالة جديدة\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const validatedData = sendMessageSchema.parse(body);\n\n    // التحقق من وجود المستقبل\n    const recipient = await prisma.user.findUnique({\n      where: { id: validatedData.toId },\n    });\n\n    if (!recipient) {\n      return NextResponse.json(\n        { success: false, error: \"المستخدم المستقبل غير موجود\" },\n        { status: 404 }\n      );\n    }\n\n    // التحقق من وجود الإعلان إذا تم تحديده\n    if (validatedData.adId) {\n      const ad = await prisma.ad.findUnique({\n        where: { id: validatedData.adId },\n      });\n\n      if (!ad) {\n        return NextResponse.json(\n          { success: false, error: \"الإعلان غير موجود\" },\n          { status: 404 }\n        );\n      }\n    }\n\n    // إنشاء الرسالة\n    const message = await prisma.message.create({\n      data: {\n        fromId: session.user.id,\n        toId: validatedData.toId,\n        adId: validatedData.adId,\n        content: validatedData.content,\n      },\n      include: {\n        from: {\n          select: { id: true, name: true, avatar: true },\n        },\n        to: {\n          select: { id: true, name: true, avatar: true },\n        },\n        ad: {\n          select: { id: true, title: true, price: true, imageUrls: true },\n        },\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: message,\n      message: \"تم إرسال الرسالة بنجاح\",\n    });\n  } catch (error) {\n    console.error(\"Error sending message:\", error);\n\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { success: false, error: error.errors[0].message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في إرسال الرسالة\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,6BAA6B;AAC7B,MAAM,oBAAoB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,SAAS,mLAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG,uBACP,GAAG,CAAC,MAAM;AACf;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,mBAAmB,aAAa,GAAG,CAAC;QAC1C,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,IAAI,kBAAkB;YACpB,mBAAmB;YACnB,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC7C,OAAO;oBACL,IAAI;wBACF;4BAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;4BAAE,MAAM;wBAAiB;wBAClD;4BAAE,QAAQ;4BAAkB,MAAM,QAAQ,IAAI,CAAC,EAAE;wBAAC;qBACnD;oBACD,GAAI,QAAQ;wBAAE;oBAAK,CAAC;gBACtB;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BAAE,IAAI;4BAAM,MAAM;4BAAM,QAAQ;wBAAK;oBAC/C;oBACA,IAAI;wBACF,QAAQ;4BAAE,IAAI;4BAAM,MAAM;4BAAM,QAAQ;wBAAK;oBAC/C;oBACA,IAAI;wBACF,QAAQ;4BAAE,IAAI;4BAAM,OAAO;4BAAM,OAAO;4BAAM,WAAW;wBAAK;oBAChE;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAM;YAC9B;YAEA,wBAAwB;YACxB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9B,OAAO;oBACL,QAAQ;oBACR,MAAM,QAAQ,IAAI,CAAC,EAAE;oBACrB,QAAQ;gBACV;gBACA,MAAM;oBAAE,QAAQ;gBAAK;YACvB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF,OAAO;YACL,sDAAsD;YACtD,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAClD,OAAO;oBACL,IAAI;wBAAC;4BAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;wBAAC;wBAAG;4BAAE,MAAM,QAAQ,IAAI,CAAC,EAAE;wBAAC;qBAAE;gBAC9D;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BAAE,IAAI;4BAAM,MAAM;4BAAM,QAAQ;wBAAK;oBAC/C;oBACA,IAAI;wBACF,QAAQ;4BAAE,IAAI;4BAAM,MAAM;4BAAM,QAAQ;wBAAK;oBAC/C;oBACA,IAAI;wBACF,QAAQ;4BAAE,IAAI;4BAAM,OAAO;4BAAM,OAAO;4BAAM,WAAW;wBAAK;oBAChE;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,MAAM;YACR;YAEA,wCAAwC;YACxC,MAAM,uBAAuB,cAAc,MAAM,CAAC,CAAC,KAAK;gBACtD,MAAM,cACJ,QAAQ,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,GAAG,QAAQ,MAAM;gBACpE,MAAM,MAAM,GAAG,YAAY,CAAC,EAAE,QAAQ,IAAI,IAAI,WAAW;gBAEzD,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,EAAE;oBACvD,GAAG,CAAC,IAAI,GAAG;wBACT,GAAG,OAAO;wBACV,WACE,QAAQ,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,IAAI;wBAChE,aAAa;oBACf;gBACF;gBAEA,OAAO;YACT,GAAG,CAAC;YAEJ,2CAA2C;YAC3C,KAAK,MAAM,gBAAgB,OAAO,MAAM,CAAC,sBAAgC;gBACvE,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC7C,OAAO;wBACL,QAAQ,aAAa,SAAS,CAAC,EAAE;wBACjC,MAAM,QAAQ,IAAI,CAAC,EAAE;wBACrB,QAAQ;wBACR,GAAI,aAAa,IAAI,IAAI;4BAAE,MAAM,aAAa,IAAI;wBAAC,CAAC;oBACtD;gBACF;gBACA,aAAa,WAAW,GAAG;YAC7B;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM,OAAO,MAAM,CAAC;YACtB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAyB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,kBAAkB,KAAK,CAAC;QAE9C,0BAA0B;QAC1B,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,OAAO;gBAAE,IAAI,cAAc,IAAI;YAAC;QAClC;QAEA,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA8B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,IAAI,cAAc,IAAI,EAAE;YACtB,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;gBACpC,OAAO;oBAAE,IAAI,cAAc,IAAI;gBAAC;YAClC;YAEA,IAAI,CAAC,IAAI;gBACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAoB,GAC7C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,gBAAgB;QAChB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,MAAM,cAAc,IAAI;gBACxB,MAAM,cAAc,IAAI;gBACxB,SAAS,cAAc,OAAO;YAChC;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAC/C;gBACA,IAAI;oBACF,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAC/C;gBACA,IAAI;oBACF,QAAQ;wBAAE,IAAI;wBAAM,OAAO;wBAAM,OAAO;wBAAM,WAAW;oBAAK;gBAChE;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QAExC,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAAC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}