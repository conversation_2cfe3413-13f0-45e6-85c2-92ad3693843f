import { NamedRegion } from "./NamedRegion.js";
/**
 * @memberOf Qualifiers.Region
 */
declare class CustomRegion extends NamedRegion {
    constructor();
    /**
     * @description The x position in pixels.
     * @param {number} x
     */
    x(x: number | string): this;
    /**
     * @description The y position in pixels.
     * @param {number} y
     */
    y(y: number | string): this;
    /**
     * @description The width of the region in pixels.
     * @param {number} width
     */
    width(width: number): this;
    /**
     * @description The height of the region in pixels.
     * @param {number} height
     */
    height(height: number): this;
}
export { CustomRegion };
