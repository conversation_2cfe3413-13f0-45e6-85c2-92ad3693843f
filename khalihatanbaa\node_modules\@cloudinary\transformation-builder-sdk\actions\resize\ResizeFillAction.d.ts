import { ResizeAdvancedAction } from "./ResizeAdvancedAction.js";
import { IActionModel } from "../../internal/models/IActionModel.js";
/**
 * @description Defines how to crop-fill an asset
 * @extends Actions.Resize.ResizeAdvancedAction
 * @memberOf Actions.Resize
 * @see Visit {@link Actions.Resize| Resize} for examples
 */
declare class ResizeFillAction extends ResizeAdvancedAction {
    /**
     * @description Absolute X position when used with Gravity.xyCenter {@link Qualifiers.Gravity.GravityQualifier}}
     * @param {number} x The x position.
     */
    x(x: number | string): this;
    /**
     * @description Absolute Y position when used with Gravity.xyCenter {@link Qualifiers.Gravity.GravityQualifier}}
     * @param {number} y The y position.
     */
    y(y: number | string): this;
    static fromJson(actionModel: IActionModel): ResizeFillAction;
}
export { ResizeFillAction };
