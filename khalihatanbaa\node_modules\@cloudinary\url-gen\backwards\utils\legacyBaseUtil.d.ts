export declare const withCamelCaseKeys: (source: {}) => {};
/**
 * Convert string to camelCase
 * @function Util.camelCase
 * @param {string} source - the string to convert
 * @return {string} in camelCase format
 */
export declare const camelCase: (source: string) => string;
/**
 * Creates a new object from source, with the keys transformed using the converter.
 * @param {object} source
 * @param {function|null} converter
 * @returns {object}
 */
export declare var convertKeys: (source: {
    [x: string]: any;
}, converter: {
    (source: string): string;
    (arg0: string): string;
}) => {};
export declare var reWords: RegExp;
export declare function identity(x: any): any;
export declare function contains(a: string | any[], obj: any): boolean;
export declare function difference(arr1: any[], arr2: any[]): any[];
