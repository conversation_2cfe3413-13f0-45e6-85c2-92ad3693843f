import { FocusOnValue, FocusOn, person, cat, microwave, refrigerator, skateboard, bird, bottle, dog, sink, face, train, sofa, sheep, pottedplant, horse, faces, cow, bus, boat, advancedEyes, advancedFace, advancedFaces, aeroplane, background, bicycle, car, chair, diningtable, tvmonitor, motorbike, ocr } from "@cloudinary/transformation-builder-sdk/qualifiers/focusOn";
export { FocusOnValue, FocusOn, person, cat, microwave, refrigerator, skateboard, bird, bottle, dog, sink, face, train, sofa, sheep, pottedplant, horse, faces, cow, bus, boat, advancedEyes, advancedFace, advancedFaces, aeroplane, background, bicycle, car, chair, diningtable, tvmonitor, motorbike, ocr };
