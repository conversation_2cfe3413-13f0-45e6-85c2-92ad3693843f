/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/create/route";
exports.ids = ["app/api/ads/create/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fcreate%2Froute&page=%2Fapi%2Fads%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fcreate%2Froute&page=%2Fapi%2Fads%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ads_create_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ads/create/route.ts */ \"(rsc)/./src/app/api/ads/create/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/create/route\",\n        pathname: \"/api/ads/create\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/create/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\kaliha\\\\khalihatanbaa\\\\src\\\\app\\\\api\\\\ads\\\\create\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_WIN10_Desktop_kaliha_khalihatanbaa_src_app_api_ads_create_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fcreate%2Froute&page=%2Fapi%2Fads%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ads/create/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/ads/create/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n\n// مخطط التحقق من بيانات الإعلان\nconst adSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(5, \"العنوان يجب أن يكون 5 أحرف على الأقل\").max(100, \"العنوان طويل جداً\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(20, \"الوصف يجب أن يكون 20 حرف على الأقل\").max(1000, \"الوصف طويل جداً\"),\n    price: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().min(0, \"السعر يجب أن يكون أكبر من أو يساوي صفر\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"الفئة مطلوبة\"),\n    subCategory: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    condition: zod__WEBPACK_IMPORTED_MODULE_4__.z[\"enum\"]([\n        \"جديد\",\n        \"مستعمل\"\n    ], {\n        errorMap: ()=>({\n                message: \"الحالة يجب أن تكون جديد أو مستعمل\"\n            })\n    }),\n    city: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"المدينة مطلوبة\"),\n    region: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    addressDetail: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    imageUrls: zod__WEBPACK_IMPORTED_MODULE_4__.z.array(zod__WEBPACK_IMPORTED_MODULE_4__.z.string()).optional(),\n    specifications: zod__WEBPACK_IMPORTED_MODULE_4__.z.record(zod__WEBPACK_IMPORTED_MODULE_4__.z.any()).optional(),\n    adType: zod__WEBPACK_IMPORTED_MODULE_4__.z[\"enum\"]([\n        \"free\",\n        \"paid\",\n        \"promoted\"\n    ], {\n        errorMap: ()=>({\n                message: \"نوع الإعلان غير صحيح\"\n            })\n    }).default(\"free\"),\n    duration: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().min(1).max(365).optional()\n});\nasync function POST(request) {\n    try {\n        console.log(\"=== API Create Ad Start ===\");\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        console.log(\"Session:\", session?.user?.id ? \"Authenticated\" : \"Not authenticated\");\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"يجب تسجيل الدخول أولاً\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        console.log(\"Request body:\", body);\n        const validatedData = adSchema.parse(body);\n        console.log(\"Validated data:\", validatedData);\n        // جلب بيانات المستخدم للتحقق من الحدود\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: session.user.id\n            },\n            select: {\n                freeAdsCount: true,\n                freeAdsExpiresAt: true,\n                paidAdsCount: true,\n                paidAdsExpiresAt: true,\n                _count: {\n                    select: {\n                        ads: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        if (!user) {\n            console.log(\"User not found:\", session.user.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"المستخدم غير موجود\"\n            }, {\n                status: 404\n            });\n        }\n        console.log(\"User data:\", user);\n        const now = new Date();\n        let canCreate = false;\n        let adData = {\n            ...validatedData,\n            userId: session.user.id\n        };\n        console.log(\"Initial ad data:\", adData);\n        // التحقق من نوع الإعلان والحدود المتاحة\n        console.log(\"Checking ad type:\", validatedData.adType);\n        switch(validatedData.adType){\n            case \"free\":\n                const freeAdsAvailable = user.freeAdsExpiresAt && user.freeAdsExpiresAt < now ? 0 : Math.max(0, user.freeAdsCount - user._count.ads);\n                console.log(\"Free ads check:\", {\n                    freeAdsCount: user.freeAdsCount,\n                    currentActiveAds: user._count.ads,\n                    freeAdsAvailable,\n                    expiresAt: user.freeAdsExpiresAt,\n                    now\n                });\n                if (freeAdsAvailable > 0) {\n                    canCreate = true;\n                    adData.isFreeAd = true;\n                    adData.expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 يوم\n                    console.log(\"Free ad approved\");\n                } else {\n                    console.log(\"Free ad rejected - no available slots\");\n                }\n                break;\n            case \"paid\":\n                const paidAdsAvailable = user.paidAdsExpiresAt && user.paidAdsExpiresAt < now ? 0 : Math.max(0, user.paidAdsCount - user._count.ads);\n                if (paidAdsAvailable > 0) {\n                    canCreate = true;\n                    adData.isFreeAd = false;\n                    const duration = validatedData.duration || 60; // 60 يوم افتراضي\n                    adData.expiresAt = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000);\n                }\n                break;\n            case \"promoted\":\n                // للإعلانات المميزة، نسمح بها دائماً (يمكن تعديل هذا لاحقاً)\n                canCreate = true;\n                adData.isFreeAd = false;\n                adData.isPromoted = true;\n                const duration = validatedData.duration || 30; // 30 يوم افتراضي\n                adData.expiresAt = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000);\n                console.log(\"Promoted ad approved\");\n                break;\n        }\n        console.log(\"Can create ad:\", canCreate);\n        console.log(\"Final ad data:\", adData);\n        if (!canCreate) {\n            const errorMsg = `لا يمكنك إنشاء إعلان ${validatedData.adType === \"free\" ? \"مجاني\" : validatedData.adType === \"paid\" ? \"مدفوع\" : \"مميز\"} في الوقت الحالي. تحقق من حدود الإعلانات المتاحة.`;\n            console.log(\"Ad creation rejected:\", errorMsg);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: errorMsg\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء الإعلان\n        console.log(\"Creating ad in database...\");\n        // إزالة الحقول غير الموجودة في schema\n        const { duration: duration1, ...cleanAdData } = adData;\n        console.log(\"Clean ad data (after removing duration):\", cleanAdData);\n        const result = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.ad.create({\n            data: cleanAdData,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        name: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        console.log(\"Ad created successfully:\", result.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result,\n            message: \"تم إنشاء الإعلان بنجاح\"\n        });\n    } catch (error) {\n        console.error(\"Error creating ad:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_4__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: error.errors[0].message\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"حدث خطأ في إنشاء الإعلان\"\n        }, {\n            status: 500\n        });\n    }\n}\n// جلب الإعلانات مع فلترة حسب النوع\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n        const adType = searchParams.get(\"adType\"); // 'free', 'paid', 'promoted'\n        const category = searchParams.get(\"category\");\n        const city = searchParams.get(\"city\");\n        const minPrice = searchParams.get(\"minPrice\");\n        const maxPrice = searchParams.get(\"maxPrice\");\n        const condition = searchParams.get(\"condition\");\n        const search = searchParams.get(\"search\");\n        const skip = (page - 1) * limit;\n        const where = {\n            isActive: true,\n            OR: [\n                {\n                    expiresAt: null\n                },\n                {\n                    expiresAt: {\n                        gte: new Date()\n                    }\n                }\n            ]\n        };\n        // فلترة حسب نوع الإعلان\n        if (adType) {\n            where.adType = adType;\n        }\n        // فلترة حسب الفئة\n        if (category) {\n            where.category = category;\n        }\n        // فلترة حسب المدينة\n        if (city) {\n            where.city = city;\n        }\n        // فلترة حسب السعر\n        if (minPrice || maxPrice) {\n            where.price = {};\n            if (minPrice) where.price.gte = parseFloat(minPrice);\n            if (maxPrice) where.price.lte = parseFloat(maxPrice);\n        }\n        // فلترة حسب الحالة\n        if (condition) {\n            where.condition = condition;\n        }\n        // البحث في العنوان والوصف\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        // ترتيب الإعلانات (المميزة أولاً)\n        const orderBy = [\n            {\n                isPromoted: \"desc\"\n            },\n            {\n                createdAt: \"desc\"\n            }\n        ];\n        const [ads, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.ad.findMany({\n                where,\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            avatar: true,\n                            ratingAverage: true,\n                            ratingCount: true\n                        }\n                    }\n                },\n                orderBy,\n                skip,\n                take: limit\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.ad.count({\n                where\n            })\n        ]);\n        const totalPages = Math.ceil(total / limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: ads,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching ads:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"حدث خطأ في جلب الإعلانات\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hZHMvY3JlYXRlL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXdEO0FBQ1g7QUFDSjtBQUNIO0FBQ2Q7QUFFeEIsZ0NBQWdDO0FBQ2hDLE1BQU1LLFdBQVdELHlDQUFRLENBQUM7SUFDeEJHLE9BQU9ILHlDQUNFLEdBQ05LLEdBQUcsQ0FBQyxHQUFHLHdDQUNQQyxHQUFHLENBQUMsS0FBSztJQUNaQyxhQUFhUCx5Q0FDSixHQUNOSyxHQUFHLENBQUMsSUFBSSxzQ0FDUkMsR0FBRyxDQUFDLE1BQU07SUFDYkUsT0FBT1IseUNBQVEsR0FBR0ssR0FBRyxDQUFDLEdBQUc7SUFDekJLLFVBQVVWLHlDQUFRLEdBQUdLLEdBQUcsQ0FBQyxHQUFHO0lBQzVCTSxhQUFhWCx5Q0FBUSxHQUFHWSxRQUFRO0lBQ2hDQyxXQUFXYiwwQ0FBTSxDQUFDO1FBQUM7UUFBUTtLQUFTLEVBQUU7UUFDcENlLFVBQVUsSUFBTztnQkFBRUMsU0FBUztZQUFvQztJQUNsRTtJQUNBQyxNQUFNakIseUNBQVEsR0FBR0ssR0FBRyxDQUFDLEdBQUc7SUFDeEJhLFFBQVFsQix5Q0FBUSxHQUFHWSxRQUFRO0lBQzNCTyxlQUFlbkIseUNBQVEsR0FBR1ksUUFBUTtJQUNsQ1EsV0FBV3BCLHdDQUFPLENBQUNBLHlDQUFRLElBQUlZLFFBQVE7SUFDdkNVLGdCQUFnQnRCLHlDQUFRLENBQUNBLHNDQUFLLElBQUlZLFFBQVE7SUFDMUNhLFFBQVF6QiwwQ0FDRCxDQUFDO1FBQUM7UUFBUTtRQUFRO0tBQVcsRUFBRTtRQUNsQ2UsVUFBVSxJQUFPO2dCQUFFQyxTQUFTO1lBQXVCO0lBQ3JELEdBQ0NVLE9BQU8sQ0FBQztJQUNYQyxVQUFVM0IseUNBQVEsR0FBR0ssR0FBRyxDQUFDLEdBQUdDLEdBQUcsQ0FBQyxLQUFLTSxRQUFRO0FBQy9DO0FBRU8sZUFBZWdCLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRkMsUUFBUUMsR0FBRyxDQUFDO1FBRVosTUFBTUMsVUFBVSxNQUFNbkMsMkRBQWdCQSxDQUFDQyxrREFBV0E7UUFDbERnQyxRQUFRQyxHQUFHLENBQ1QsWUFDQUMsU0FBU0MsTUFBTUMsS0FBSyxrQkFBa0I7UUFHeEMsSUFBSSxDQUFDRixTQUFTQyxNQUFNQyxJQUFJO1lBQ3RCLE9BQU90QyxxREFBWUEsQ0FBQ3VDLElBQUksQ0FDdEI7Z0JBQUVDLFNBQVM7Z0JBQU9DLE9BQU87WUFBeUIsR0FDbEQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1DLE9BQU8sTUFBTVYsUUFBUU0sSUFBSTtRQUMvQkwsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQlE7UUFFN0IsTUFBTUMsZ0JBQWdCdkMsU0FBU3dDLEtBQUssQ0FBQ0Y7UUFDckNULFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJTO1FBRS9CLHVDQUF1QztRQUN2QyxNQUFNUCxPQUFPLE1BQU1sQywrQ0FBTUEsQ0FBQ2tDLElBQUksQ0FBQ1MsVUFBVSxDQUFDO1lBQ3hDQyxPQUFPO2dCQUFFVCxJQUFJRixRQUFRQyxJQUFJLENBQUNDLEVBQUU7WUFBQztZQUM3QlUsUUFBUTtnQkFDTkMsY0FBYztnQkFDZEMsa0JBQWtCO2dCQUNsQkMsY0FBYztnQkFDZEMsa0JBQWtCO2dCQUNsQkMsUUFBUTtvQkFDTkwsUUFBUTt3QkFDTk0sS0FBSzs0QkFDSFAsT0FBTztnQ0FBRVEsVUFBVTs0QkFBSzt3QkFDMUI7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDbEIsTUFBTTtZQUNUSCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CQyxRQUFRQyxJQUFJLENBQUNDLEVBQUU7WUFDOUMsT0FBT3RDLHFEQUFZQSxDQUFDdUMsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUFxQixHQUM5QztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUFSLFFBQVFDLEdBQUcsQ0FBQyxjQUFjRTtRQUUxQixNQUFNbUIsTUFBTSxJQUFJQztRQUNoQixJQUFJQyxZQUFZO1FBQ2hCLElBQUlDLFNBQWM7WUFDaEIsR0FBR2YsYUFBYTtZQUNoQmdCLFFBQVF4QixRQUFRQyxJQUFJLENBQUNDLEVBQUU7UUFDekI7UUFFQUosUUFBUUMsR0FBRyxDQUFDLG9CQUFvQndCO1FBRWhDLHdDQUF3QztRQUN4Q3pCLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJTLGNBQWNmLE1BQU07UUFFckQsT0FBUWUsY0FBY2YsTUFBTTtZQUMxQixLQUFLO2dCQUNILE1BQU1nQyxtQkFDSnhCLEtBQUthLGdCQUFnQixJQUFJYixLQUFLYSxnQkFBZ0IsR0FBR00sTUFDN0MsSUFDQU0sS0FBS3BELEdBQUcsQ0FBQyxHQUFHMkIsS0FBS1ksWUFBWSxHQUFHWixLQUFLZ0IsTUFBTSxDQUFDQyxHQUFHO2dCQUVyRHBCLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUI7b0JBQzdCYyxjQUFjWixLQUFLWSxZQUFZO29CQUMvQmMsa0JBQWtCMUIsS0FBS2dCLE1BQU0sQ0FBQ0MsR0FBRztvQkFDakNPO29CQUNBRyxXQUFXM0IsS0FBS2EsZ0JBQWdCO29CQUNoQ007Z0JBQ0Y7Z0JBRUEsSUFBSUssbUJBQW1CLEdBQUc7b0JBQ3hCSCxZQUFZO29CQUNaQyxPQUFPTSxRQUFRLEdBQUc7b0JBQ2xCTixPQUFPSyxTQUFTLEdBQUcsSUFBSVAsS0FBS0QsSUFBSVUsT0FBTyxLQUFLLEtBQUssS0FBSyxLQUFLLEtBQUssT0FBTyxTQUFTO29CQUNoRmhDLFFBQVFDLEdBQUcsQ0FBQztnQkFDZCxPQUFPO29CQUNMRCxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7Z0JBQ0E7WUFFRixLQUFLO2dCQUNILE1BQU1nQyxtQkFDSjlCLEtBQUtlLGdCQUFnQixJQUFJZixLQUFLZSxnQkFBZ0IsR0FBR0ksTUFDN0MsSUFDQU0sS0FBS3BELEdBQUcsQ0FBQyxHQUFHMkIsS0FBS2MsWUFBWSxHQUFHZCxLQUFLZ0IsTUFBTSxDQUFDQyxHQUFHO2dCQUVyRCxJQUFJYSxtQkFBbUIsR0FBRztvQkFDeEJULFlBQVk7b0JBQ1pDLE9BQU9NLFFBQVEsR0FBRztvQkFDbEIsTUFBTWxDLFdBQVdhLGNBQWNiLFFBQVEsSUFBSSxJQUFJLGlCQUFpQjtvQkFDaEU0QixPQUFPSyxTQUFTLEdBQUcsSUFBSVAsS0FDckJELElBQUlVLE9BQU8sS0FBS25DLFdBQVcsS0FBSyxLQUFLLEtBQUs7Z0JBRTlDO2dCQUNBO1lBRUYsS0FBSztnQkFDSCw2REFBNkQ7Z0JBQzdEMkIsWUFBWTtnQkFDWkMsT0FBT00sUUFBUSxHQUFHO2dCQUNsQk4sT0FBT1MsVUFBVSxHQUFHO2dCQUNwQixNQUFNckMsV0FBV2EsY0FBY2IsUUFBUSxJQUFJLElBQUksaUJBQWlCO2dCQUNoRTRCLE9BQU9LLFNBQVMsR0FBRyxJQUFJUCxLQUNyQkQsSUFBSVUsT0FBTyxLQUFLbkMsV0FBVyxLQUFLLEtBQUssS0FBSztnQkFFNUNHLFFBQVFDLEdBQUcsQ0FBQztnQkFDWjtRQUNKO1FBRUFELFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0J1QjtRQUM5QnhCLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0J3QjtRQUU5QixJQUFJLENBQUNELFdBQVc7WUFDZCxNQUFNVyxXQUFXLENBQUMscUJBQXFCLEVBQ3JDekIsY0FBY2YsTUFBTSxLQUFLLFNBQ3JCLFVBQ0FlLGNBQWNmLE1BQU0sS0FBSyxTQUN6QixVQUNBLE9BQ0wsaURBQWlELENBQUM7WUFFbkRLLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJrQztZQUVyQyxPQUFPckUscURBQVlBLENBQUN1QyxJQUFJLENBQ3RCO2dCQUNFQyxTQUFTO2dCQUNUQyxPQUFPNEI7WUFDVCxHQUNBO2dCQUFFM0IsUUFBUTtZQUFJO1FBRWxCO1FBRUEsZ0JBQWdCO1FBQ2hCUixRQUFRQyxHQUFHLENBQUM7UUFFWixzQ0FBc0M7UUFDdEMsTUFBTSxFQUFFSixVQUFBQSxTQUFRLEVBQUUsR0FBR3VDLGFBQWEsR0FBR1g7UUFDckN6QixRQUFRQyxHQUFHLENBQUMsNENBQTRDbUM7UUFFeEQsTUFBTUMsU0FBUyxNQUFNcEUsK0NBQU1BLENBQUNxRSxFQUFFLENBQUNDLE1BQU0sQ0FBQztZQUNwQ0MsTUFBTUo7WUFDTkssU0FBUztnQkFDUHRDLE1BQU07b0JBQ0pXLFFBQVE7d0JBQUVWLElBQUk7d0JBQU1zQyxNQUFNO3dCQUFNQyxRQUFRO29CQUFLO2dCQUMvQztZQUNGO1FBQ0Y7UUFFQTNDLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJvQyxPQUFPakMsRUFBRTtRQUVqRCxPQUFPdEMscURBQVlBLENBQUN1QyxJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVGtDLE1BQU1IO1lBQ05uRCxTQUFTO1FBQ1g7SUFDRixFQUFFLE9BQU9xQixPQUFPO1FBQ2RQLFFBQVFPLEtBQUssQ0FBQyxzQkFBc0JBO1FBRXBDLElBQUlBLGlCQUFpQnJDLDJDQUFVLEVBQUU7WUFDL0IsT0FBT0oscURBQVlBLENBQUN1QyxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPQSxNQUFNc0MsTUFBTSxDQUFDLEVBQUUsQ0FBQzNELE9BQU87WUFBQyxHQUNqRDtnQkFBRXNCLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE9BQU8xQyxxREFBWUEsQ0FBQ3VDLElBQUksQ0FDdEI7WUFBRUMsU0FBUztZQUFPQyxPQUFPO1FBQTJCLEdBQ3BEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsbUNBQW1DO0FBQzVCLGVBQWVzQyxJQUFJL0MsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU0sRUFBRWdELFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlqRCxRQUFRa0QsR0FBRztRQUM1QyxNQUFNQyxPQUFPQyxTQUFTSixhQUFhSyxHQUFHLENBQUMsV0FBVztRQUNsRCxNQUFNQyxRQUFRRixTQUFTSixhQUFhSyxHQUFHLENBQUMsWUFBWTtRQUNwRCxNQUFNekQsU0FBU29ELGFBQWFLLEdBQUcsQ0FBQyxXQUFXLDZCQUE2QjtRQUN4RSxNQUFNeEUsV0FBV21FLGFBQWFLLEdBQUcsQ0FBQztRQUNsQyxNQUFNakUsT0FBTzRELGFBQWFLLEdBQUcsQ0FBQztRQUM5QixNQUFNRSxXQUFXUCxhQUFhSyxHQUFHLENBQUM7UUFDbEMsTUFBTUcsV0FBV1IsYUFBYUssR0FBRyxDQUFDO1FBQ2xDLE1BQU1yRSxZQUFZZ0UsYUFBYUssR0FBRyxDQUFDO1FBQ25DLE1BQU1JLFNBQVNULGFBQWFLLEdBQUcsQ0FBQztRQUNoQyxNQUFNSyxPQUFPLENBQUNQLE9BQU8sS0FBS0c7UUFFMUIsTUFBTXhDLFFBQWE7WUFDakJRLFVBQVU7WUFDVnFDLElBQUk7Z0JBQ0Y7b0JBQUU1QixXQUFXO2dCQUFLO2dCQUNsQjtvQkFBRUEsV0FBVzt3QkFBRTZCLEtBQUssSUFBSXBDO29CQUFPO2dCQUFFO2FBQ2xDO1FBQ0g7UUFFQSx3QkFBd0I7UUFDeEIsSUFBSTVCLFFBQVE7WUFDVmtCLE1BQU1sQixNQUFNLEdBQUdBO1FBQ2pCO1FBRUEsa0JBQWtCO1FBQ2xCLElBQUlmLFVBQVU7WUFDWmlDLE1BQU1qQyxRQUFRLEdBQUdBO1FBQ25CO1FBRUEsb0JBQW9CO1FBQ3BCLElBQUlPLE1BQU07WUFDUjBCLE1BQU0xQixJQUFJLEdBQUdBO1FBQ2Y7UUFFQSxrQkFBa0I7UUFDbEIsSUFBSW1FLFlBQVlDLFVBQVU7WUFDeEIxQyxNQUFNbkMsS0FBSyxHQUFHLENBQUM7WUFDZixJQUFJNEUsVUFBVXpDLE1BQU1uQyxLQUFLLENBQUNpRixHQUFHLEdBQUdDLFdBQVdOO1lBQzNDLElBQUlDLFVBQVUxQyxNQUFNbkMsS0FBSyxDQUFDbUYsR0FBRyxHQUFHRCxXQUFXTDtRQUM3QztRQUVBLG1CQUFtQjtRQUNuQixJQUFJeEUsV0FBVztZQUNiOEIsTUFBTTlCLFNBQVMsR0FBR0E7UUFDcEI7UUFFQSwwQkFBMEI7UUFDMUIsSUFBSXlFLFFBQVE7WUFDVjNDLE1BQU02QyxFQUFFLEdBQUc7Z0JBQ1Q7b0JBQUVyRixPQUFPO3dCQUFFeUYsVUFBVU47d0JBQVFPLE1BQU07b0JBQWM7Z0JBQUU7Z0JBQ25EO29CQUFFdEYsYUFBYTt3QkFBRXFGLFVBQVVOO3dCQUFRTyxNQUFNO29CQUFjO2dCQUFFO2FBQzFEO1FBQ0g7UUFFQSxrQ0FBa0M7UUFDbEMsTUFBTUMsVUFBVTtZQUNkO2dCQUFFOUIsWUFBWTtZQUFnQjtZQUM5QjtnQkFBRStCLFdBQVc7WUFBZ0I7U0FDOUI7UUFFRCxNQUFNLENBQUM3QyxLQUFLOEMsTUFBTSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztZQUNyQ25HLCtDQUFNQSxDQUFDcUUsRUFBRSxDQUFDK0IsUUFBUSxDQUFDO2dCQUNqQnhEO2dCQUNBNEIsU0FBUztvQkFDUHRDLE1BQU07d0JBQ0pXLFFBQVE7NEJBQ05WLElBQUk7NEJBQ0pzQyxNQUFNOzRCQUNOQyxRQUFROzRCQUNSMkIsZUFBZTs0QkFDZkMsYUFBYTt3QkFDZjtvQkFDRjtnQkFDRjtnQkFDQVA7Z0JBQ0FQO2dCQUNBZSxNQUFNbkI7WUFDUjtZQUNBcEYsK0NBQU1BLENBQUNxRSxFQUFFLENBQUNtQyxLQUFLLENBQUM7Z0JBQUU1RDtZQUFNO1NBQ3pCO1FBRUQsTUFBTTZELGFBQWE5QyxLQUFLK0MsSUFBSSxDQUFDVCxRQUFRYjtRQUVyQyxPQUFPdkYscURBQVlBLENBQUN1QyxJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVGtDLE1BQU1wQjtZQUNOd0QsWUFBWTtnQkFDVjFCO2dCQUNBRztnQkFDQWE7Z0JBQ0FRO1lBQ0Y7UUFDRjtJQUNGLEVBQUUsT0FBT25FLE9BQU87UUFDZFAsUUFBUU8sS0FBSyxDQUFDLHVCQUF1QkE7UUFDckMsT0FBT3pDLHFEQUFZQSxDQUFDdUMsSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9DLE9BQU87UUFBMkIsR0FDcEQ7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcc3JjXFxhcHBcXGFwaVxcYWRzXFxjcmVhdGVcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIjtcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tIFwibmV4dC1hdXRoXCI7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gXCJAL2xpYi9hdXRoXCI7XG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tIFwiQC9saWIvcHJpc21hXCI7XG5pbXBvcnQgeyB6IH0gZnJvbSBcInpvZFwiO1xuXG4vLyDZhdiu2LfYtyDYp9mE2KrYrdmC2YIg2YXZhiDYqNmK2KfZhtin2Kog2KfZhNil2LnZhNin2YZcbmNvbnN0IGFkU2NoZW1hID0gei5vYmplY3Qoe1xuICB0aXRsZTogelxuICAgIC5zdHJpbmcoKVxuICAgIC5taW4oNSwgXCLYp9mE2LnZhtmI2KfZhiDZitis2Kgg2KPZhiDZitmD2YjZhiA1INij2K3YsdmBINi52YTZiSDYp9mE2KPZgtmEXCIpXG4gICAgLm1heCgxMDAsIFwi2KfZhNi52YbZiNin2YYg2LfZiNmK2YQg2KzYr9in2YtcIiksXG4gIGRlc2NyaXB0aW9uOiB6XG4gICAgLnN0cmluZygpXG4gICAgLm1pbigyMCwgXCLYp9mE2YjYtdmBINmK2KzYqCDYo9mGINmK2YPZiNmGIDIwINit2LHZgSDYudmE2Ykg2KfZhNij2YLZhFwiKVxuICAgIC5tYXgoMTAwMCwgXCLYp9mE2YjYtdmBINi32YjZitmEINis2K/Yp9mLXCIpLFxuICBwcmljZTogei5udW1iZXIoKS5taW4oMCwgXCLYp9mE2LPYudixINmK2KzYqCDYo9mGINmK2YPZiNmGINij2YPYqNixINmF2YYg2KPZiCDZitiz2KfZiNmKINi12YHYsVwiKSxcbiAgY2F0ZWdvcnk6IHouc3RyaW5nKCkubWluKDEsIFwi2KfZhNmB2KbYqSDZhdi32YTZiNio2KlcIiksXG4gIHN1YkNhdGVnb3J5OiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIGNvbmRpdGlvbjogei5lbnVtKFtcItis2K/ZitivXCIsIFwi2YXYs9iq2LnZhdmEXCJdLCB7XG4gICAgZXJyb3JNYXA6ICgpID0+ICh7IG1lc3NhZ2U6IFwi2KfZhNit2KfZhNipINmK2KzYqCDYo9mGINiq2YPZiNmGINis2K/ZitivINij2Ygg2YXYs9iq2LnZhdmEXCIgfSksXG4gIH0pLFxuICBjaXR5OiB6LnN0cmluZygpLm1pbigxLCBcItin2YTZhdiv2YrZhtipINmF2LfZhNmI2KjYqVwiKSxcbiAgcmVnaW9uOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIGFkZHJlc3NEZXRhaWw6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgaW1hZ2VVcmxzOiB6LmFycmF5KHouc3RyaW5nKCkpLm9wdGlvbmFsKCksXG4gIHNwZWNpZmljYXRpb25zOiB6LnJlY29yZCh6LmFueSgpKS5vcHRpb25hbCgpLFxuICBhZFR5cGU6IHpcbiAgICAuZW51bShbXCJmcmVlXCIsIFwicGFpZFwiLCBcInByb21vdGVkXCJdLCB7XG4gICAgICBlcnJvck1hcDogKCkgPT4gKHsgbWVzc2FnZTogXCLZhtmI2Lkg2KfZhNil2LnZhNin2YYg2LrZitixINi12K3ZititXCIgfSksXG4gICAgfSlcbiAgICAuZGVmYXVsdChcImZyZWVcIiksXG4gIGR1cmF0aW9uOiB6Lm51bWJlcigpLm1pbigxKS5tYXgoMzY1KS5vcHRpb25hbCgpLCAvLyDZhdiv2Kkg2KfZhNil2LnZhNin2YYg2KjYp9mE2KPZitin2YVcbn0pO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKFwiPT09IEFQSSBDcmVhdGUgQWQgU3RhcnQgPT09XCIpO1xuXG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpO1xuICAgIGNvbnNvbGUubG9nKFxuICAgICAgXCJTZXNzaW9uOlwiLFxuICAgICAgc2Vzc2lvbj8udXNlcj8uaWQgPyBcIkF1dGhlbnRpY2F0ZWRcIiA6IFwiTm90IGF1dGhlbnRpY2F0ZWRcIlxuICAgICk7XG5cbiAgICBpZiAoIXNlc3Npb24/LnVzZXI/LmlkKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcItmK2KzYqCDYqtiz2KzZitmEINin2YTYr9iu2YjZhCDYo9mI2YTYp9mLXCIgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBjb25zb2xlLmxvZyhcIlJlcXVlc3QgYm9keTpcIiwgYm9keSk7XG5cbiAgICBjb25zdCB2YWxpZGF0ZWREYXRhID0gYWRTY2hlbWEucGFyc2UoYm9keSk7XG4gICAgY29uc29sZS5sb2coXCJWYWxpZGF0ZWQgZGF0YTpcIiwgdmFsaWRhdGVkRGF0YSk7XG5cbiAgICAvLyDYrNmE2Kgg2KjZitin2YbYp9iqINin2YTZhdiz2KrYrtiv2YUg2YTZhNiq2K3ZgtmCINmF2YYg2KfZhNit2K/ZiNivXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xuICAgICAgd2hlcmU6IHsgaWQ6IHNlc3Npb24udXNlci5pZCB9LFxuICAgICAgc2VsZWN0OiB7XG4gICAgICAgIGZyZWVBZHNDb3VudDogdHJ1ZSxcbiAgICAgICAgZnJlZUFkc0V4cGlyZXNBdDogdHJ1ZSxcbiAgICAgICAgcGFpZEFkc0NvdW50OiB0cnVlLFxuICAgICAgICBwYWlkQWRzRXhwaXJlc0F0OiB0cnVlLFxuICAgICAgICBfY291bnQ6IHtcbiAgICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICAgIGFkczoge1xuICAgICAgICAgICAgICB3aGVyZTogeyBpc0FjdGl2ZTogdHJ1ZSB9LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgY29uc29sZS5sb2coXCJVc2VyIG5vdCBmb3VuZDpcIiwgc2Vzc2lvbi51c2VyLmlkKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IFwi2KfZhNmF2LPYqtiu2K/ZhSDYutmK2LEg2YXZiNis2YjYr1wiIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDQgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhcIlVzZXIgZGF0YTpcIiwgdXNlcik7XG5cbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgIGxldCBjYW5DcmVhdGUgPSBmYWxzZTtcbiAgICBsZXQgYWREYXRhOiBhbnkgPSB7XG4gICAgICAuLi52YWxpZGF0ZWREYXRhLFxuICAgICAgdXNlcklkOiBzZXNzaW9uLnVzZXIuaWQsXG4gICAgfTtcblxuICAgIGNvbnNvbGUubG9nKFwiSW5pdGlhbCBhZCBkYXRhOlwiLCBhZERhdGEpO1xuXG4gICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2YbZiNi5INin2YTYpdi52YTYp9mGINmI2KfZhNit2K/ZiNivINin2YTZhdiq2KfYrdipXG4gICAgY29uc29sZS5sb2coXCJDaGVja2luZyBhZCB0eXBlOlwiLCB2YWxpZGF0ZWREYXRhLmFkVHlwZSk7XG5cbiAgICBzd2l0Y2ggKHZhbGlkYXRlZERhdGEuYWRUeXBlKSB7XG4gICAgICBjYXNlIFwiZnJlZVwiOlxuICAgICAgICBjb25zdCBmcmVlQWRzQXZhaWxhYmxlID1cbiAgICAgICAgICB1c2VyLmZyZWVBZHNFeHBpcmVzQXQgJiYgdXNlci5mcmVlQWRzRXhwaXJlc0F0IDwgbm93XG4gICAgICAgICAgICA/IDBcbiAgICAgICAgICAgIDogTWF0aC5tYXgoMCwgdXNlci5mcmVlQWRzQ291bnQgLSB1c2VyLl9jb3VudC5hZHMpO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKFwiRnJlZSBhZHMgY2hlY2s6XCIsIHtcbiAgICAgICAgICBmcmVlQWRzQ291bnQ6IHVzZXIuZnJlZUFkc0NvdW50LFxuICAgICAgICAgIGN1cnJlbnRBY3RpdmVBZHM6IHVzZXIuX2NvdW50LmFkcyxcbiAgICAgICAgICBmcmVlQWRzQXZhaWxhYmxlLFxuICAgICAgICAgIGV4cGlyZXNBdDogdXNlci5mcmVlQWRzRXhwaXJlc0F0LFxuICAgICAgICAgIG5vdyxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgaWYgKGZyZWVBZHNBdmFpbGFibGUgPiAwKSB7XG4gICAgICAgICAgY2FuQ3JlYXRlID0gdHJ1ZTtcbiAgICAgICAgICBhZERhdGEuaXNGcmVlQWQgPSB0cnVlO1xuICAgICAgICAgIGFkRGF0YS5leHBpcmVzQXQgPSBuZXcgRGF0ZShub3cuZ2V0VGltZSgpICsgMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKTsgLy8gMzAg2YrZiNmFXG4gICAgICAgICAgY29uc29sZS5sb2coXCJGcmVlIGFkIGFwcHJvdmVkXCIpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwiRnJlZSBhZCByZWplY3RlZCAtIG5vIGF2YWlsYWJsZSBzbG90c1wiKTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcblxuICAgICAgY2FzZSBcInBhaWRcIjpcbiAgICAgICAgY29uc3QgcGFpZEFkc0F2YWlsYWJsZSA9XG4gICAgICAgICAgdXNlci5wYWlkQWRzRXhwaXJlc0F0ICYmIHVzZXIucGFpZEFkc0V4cGlyZXNBdCA8IG5vd1xuICAgICAgICAgICAgPyAwXG4gICAgICAgICAgICA6IE1hdGgubWF4KDAsIHVzZXIucGFpZEFkc0NvdW50IC0gdXNlci5fY291bnQuYWRzKTtcblxuICAgICAgICBpZiAocGFpZEFkc0F2YWlsYWJsZSA+IDApIHtcbiAgICAgICAgICBjYW5DcmVhdGUgPSB0cnVlO1xuICAgICAgICAgIGFkRGF0YS5pc0ZyZWVBZCA9IGZhbHNlO1xuICAgICAgICAgIGNvbnN0IGR1cmF0aW9uID0gdmFsaWRhdGVkRGF0YS5kdXJhdGlvbiB8fCA2MDsgLy8gNjAg2YrZiNmFINin2YHYqtix2KfYttmKXG4gICAgICAgICAgYWREYXRhLmV4cGlyZXNBdCA9IG5ldyBEYXRlKFxuICAgICAgICAgICAgbm93LmdldFRpbWUoKSArIGR1cmF0aW9uICogMjQgKiA2MCAqIDYwICogMTAwMFxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgXCJwcm9tb3RlZFwiOlxuICAgICAgICAvLyDZhNmE2KXYudmE2KfZhtin2Kog2KfZhNmF2YXZitiy2KnYjCDZhtiz2YXYrSDYqNmH2Kcg2K/Yp9im2YXYp9mLICjZitmF2YPZhiDYqti52K/ZitmEINmH2LDYpyDZhNin2K3Zgtin2YspXG4gICAgICAgIGNhbkNyZWF0ZSA9IHRydWU7XG4gICAgICAgIGFkRGF0YS5pc0ZyZWVBZCA9IGZhbHNlO1xuICAgICAgICBhZERhdGEuaXNQcm9tb3RlZCA9IHRydWU7XG4gICAgICAgIGNvbnN0IGR1cmF0aW9uID0gdmFsaWRhdGVkRGF0YS5kdXJhdGlvbiB8fCAzMDsgLy8gMzAg2YrZiNmFINin2YHYqtix2KfYttmKXG4gICAgICAgIGFkRGF0YS5leHBpcmVzQXQgPSBuZXcgRGF0ZShcbiAgICAgICAgICBub3cuZ2V0VGltZSgpICsgZHVyYXRpb24gKiAyNCAqIDYwICogNjAgKiAxMDAwXG4gICAgICAgICk7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiUHJvbW90ZWQgYWQgYXBwcm92ZWRcIik7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKFwiQ2FuIGNyZWF0ZSBhZDpcIiwgY2FuQ3JlYXRlKTtcbiAgICBjb25zb2xlLmxvZyhcIkZpbmFsIGFkIGRhdGE6XCIsIGFkRGF0YSk7XG5cbiAgICBpZiAoIWNhbkNyZWF0ZSkge1xuICAgICAgY29uc3QgZXJyb3JNc2cgPSBg2YTYpyDZitmF2YPZhtmDINil2YbYtNin2KEg2KXYudmE2KfZhiAke1xuICAgICAgICB2YWxpZGF0ZWREYXRhLmFkVHlwZSA9PT0gXCJmcmVlXCJcbiAgICAgICAgICA/IFwi2YXYrNin2YbZilwiXG4gICAgICAgICAgOiB2YWxpZGF0ZWREYXRhLmFkVHlwZSA9PT0gXCJwYWlkXCJcbiAgICAgICAgICA/IFwi2YXYr9mB2YjYuVwiXG4gICAgICAgICAgOiBcItmF2YXZitiyXCJcbiAgICAgIH0g2YHZiiDYp9mE2YjZgtiqINin2YTYrdin2YTZii4g2KrYrdmC2YIg2YXZhiDYrdiv2YjYryDYp9mE2KXYudmE2KfZhtin2Kog2KfZhNmF2KrYp9it2KkuYDtcblxuICAgICAgY29uc29sZS5sb2coXCJBZCBjcmVhdGlvbiByZWplY3RlZDpcIiwgZXJyb3JNc2cpO1xuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogZXJyb3JNc2csXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyDYpdmG2LTYp9ihINin2YTYpdi52YTYp9mGXG4gICAgY29uc29sZS5sb2coXCJDcmVhdGluZyBhZCBpbiBkYXRhYmFzZS4uLlwiKTtcblxuICAgIC8vINil2LLYp9mE2Kkg2KfZhNit2YLZiNmEINi62YrYsSDYp9mE2YXZiNis2YjYr9ipINmB2Yogc2NoZW1hXG4gICAgY29uc3QgeyBkdXJhdGlvbiwgLi4uY2xlYW5BZERhdGEgfSA9IGFkRGF0YTtcbiAgICBjb25zb2xlLmxvZyhcIkNsZWFuIGFkIGRhdGEgKGFmdGVyIHJlbW92aW5nIGR1cmF0aW9uKTpcIiwgY2xlYW5BZERhdGEpO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcHJpc21hLmFkLmNyZWF0ZSh7XG4gICAgICBkYXRhOiBjbGVhbkFkRGF0YSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgdXNlcjoge1xuICAgICAgICAgIHNlbGVjdDogeyBpZDogdHJ1ZSwgbmFtZTogdHJ1ZSwgYXZhdGFyOiB0cnVlIH0sXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coXCJBZCBjcmVhdGVkIHN1Y2Nlc3NmdWxseTpcIiwgcmVzdWx0LmlkKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogcmVzdWx0LFxuICAgICAgbWVzc2FnZTogXCLYqtmFINil2YbYtNin2KEg2KfZhNil2LnZhNin2YYg2KjZhtis2KfYrVwiLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBjcmVhdGluZyBhZDpcIiwgZXJyb3IpO1xuXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2Ygei5ab2RFcnJvcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3IuZXJyb3JzWzBdLm1lc3NhZ2UgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcItit2K/YqyDYrti32KMg2YHZiiDYpdmG2LTYp9ihINin2YTYpdi52YTYp9mGXCIgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8g2KzZhNioINin2YTYpdi52YTYp9mG2KfYqiDZhdi5INmB2YTYqtix2Kkg2K3Ys9ioINin2YTZhtmI2LlcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgcGFnZSA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoXCJwYWdlXCIpIHx8IFwiMVwiKTtcbiAgICBjb25zdCBsaW1pdCA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoXCJsaW1pdFwiKSB8fCBcIjEyXCIpO1xuICAgIGNvbnN0IGFkVHlwZSA9IHNlYXJjaFBhcmFtcy5nZXQoXCJhZFR5cGVcIik7IC8vICdmcmVlJywgJ3BhaWQnLCAncHJvbW90ZWQnXG4gICAgY29uc3QgY2F0ZWdvcnkgPSBzZWFyY2hQYXJhbXMuZ2V0KFwiY2F0ZWdvcnlcIik7XG4gICAgY29uc3QgY2l0eSA9IHNlYXJjaFBhcmFtcy5nZXQoXCJjaXR5XCIpO1xuICAgIGNvbnN0IG1pblByaWNlID0gc2VhcmNoUGFyYW1zLmdldChcIm1pblByaWNlXCIpO1xuICAgIGNvbnN0IG1heFByaWNlID0gc2VhcmNoUGFyYW1zLmdldChcIm1heFByaWNlXCIpO1xuICAgIGNvbnN0IGNvbmRpdGlvbiA9IHNlYXJjaFBhcmFtcy5nZXQoXCJjb25kaXRpb25cIik7XG4gICAgY29uc3Qgc2VhcmNoID0gc2VhcmNoUGFyYW1zLmdldChcInNlYXJjaFwiKTtcbiAgICBjb25zdCBza2lwID0gKHBhZ2UgLSAxKSAqIGxpbWl0O1xuXG4gICAgY29uc3Qgd2hlcmU6IGFueSA9IHtcbiAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgT1I6IFtcbiAgICAgICAgeyBleHBpcmVzQXQ6IG51bGwgfSwgLy8g2KfZhNil2LnZhNin2YbYp9iqINio2K/ZiNmGINiq2KfYsdmK2K4g2KfZhtiq2YfYp9ihXG4gICAgICAgIHsgZXhwaXJlc0F0OiB7IGd0ZTogbmV3IERhdGUoKSB9IH0sIC8vINin2YTYpdi52YTYp9mG2KfYqiDYutmK2LEg2YXZhtiq2YfZitipINin2YTYtdmE2KfYrdmK2KlcbiAgICAgIF0sXG4gICAgfTtcblxuICAgIC8vINmB2YTYqtix2Kkg2K3Ys9ioINmG2YjYuSDYp9mE2KXYudmE2KfZhlxuICAgIGlmIChhZFR5cGUpIHtcbiAgICAgIHdoZXJlLmFkVHlwZSA9IGFkVHlwZTtcbiAgICB9XG5cbiAgICAvLyDZgdmE2KrYsdipINit2LPYqCDYp9mE2YHYptipXG4gICAgaWYgKGNhdGVnb3J5KSB7XG4gICAgICB3aGVyZS5jYXRlZ29yeSA9IGNhdGVnb3J5O1xuICAgIH1cblxuICAgIC8vINmB2YTYqtix2Kkg2K3Ys9ioINin2YTZhdiv2YrZhtipXG4gICAgaWYgKGNpdHkpIHtcbiAgICAgIHdoZXJlLmNpdHkgPSBjaXR5O1xuICAgIH1cblxuICAgIC8vINmB2YTYqtix2Kkg2K3Ys9ioINin2YTYs9i52LFcbiAgICBpZiAobWluUHJpY2UgfHwgbWF4UHJpY2UpIHtcbiAgICAgIHdoZXJlLnByaWNlID0ge307XG4gICAgICBpZiAobWluUHJpY2UpIHdoZXJlLnByaWNlLmd0ZSA9IHBhcnNlRmxvYXQobWluUHJpY2UpO1xuICAgICAgaWYgKG1heFByaWNlKSB3aGVyZS5wcmljZS5sdGUgPSBwYXJzZUZsb2F0KG1heFByaWNlKTtcbiAgICB9XG5cbiAgICAvLyDZgdmE2KrYsdipINit2LPYqCDYp9mE2K3Yp9mE2KlcbiAgICBpZiAoY29uZGl0aW9uKSB7XG4gICAgICB3aGVyZS5jb25kaXRpb24gPSBjb25kaXRpb247XG4gICAgfVxuXG4gICAgLy8g2KfZhNio2K3YqyDZgdmKINin2YTYudmG2YjYp9mGINmI2KfZhNmI2LXZgVxuICAgIGlmIChzZWFyY2gpIHtcbiAgICAgIHdoZXJlLk9SID0gW1xuICAgICAgICB7IHRpdGxlOiB7IGNvbnRhaW5zOiBzZWFyY2gsIG1vZGU6IFwiaW5zZW5zaXRpdmVcIiB9IH0sXG4gICAgICAgIHsgZGVzY3JpcHRpb246IHsgY29udGFpbnM6IHNlYXJjaCwgbW9kZTogXCJpbnNlbnNpdGl2ZVwiIH0gfSxcbiAgICAgIF07XG4gICAgfVxuXG4gICAgLy8g2KrYsdiq2YrYqCDYp9mE2KXYudmE2KfZhtin2KogKNin2YTZhdmF2YrYstipINij2YjZhNin2YspXG4gICAgY29uc3Qgb3JkZXJCeSA9IFtcbiAgICAgIHsgaXNQcm9tb3RlZDogXCJkZXNjXCIgYXMgY29uc3QgfSxcbiAgICAgIHsgY3JlYXRlZEF0OiBcImRlc2NcIiBhcyBjb25zdCB9LFxuICAgIF07XG5cbiAgICBjb25zdCBbYWRzLCB0b3RhbF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICBwcmlzbWEuYWQuZmluZE1hbnkoe1xuICAgICAgICB3aGVyZSxcbiAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgIHVzZXI6IHtcbiAgICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgICBpZDogdHJ1ZSxcbiAgICAgICAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgICAgICAgYXZhdGFyOiB0cnVlLFxuICAgICAgICAgICAgICByYXRpbmdBdmVyYWdlOiB0cnVlLFxuICAgICAgICAgICAgICByYXRpbmdDb3VudDogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgb3JkZXJCeSxcbiAgICAgICAgc2tpcCxcbiAgICAgICAgdGFrZTogbGltaXQsXG4gICAgICB9KSxcbiAgICAgIHByaXNtYS5hZC5jb3VudCh7IHdoZXJlIH0pLFxuICAgIF0pO1xuXG4gICAgY29uc3QgdG90YWxQYWdlcyA9IE1hdGguY2VpbCh0b3RhbCAvIGxpbWl0KTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogYWRzLFxuICAgICAgcGFnaW5hdGlvbjoge1xuICAgICAgICBwYWdlLFxuICAgICAgICBsaW1pdCxcbiAgICAgICAgdG90YWwsXG4gICAgICAgIHRvdGFsUGFnZXMsXG4gICAgICB9LFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBhZHM6XCIsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCLYrdiv2Ksg2K7Yt9ijINmB2Yog2KzZhNioINin2YTYpdi52YTYp9mG2KfYqlwiIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZ2V0U2VydmVyU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwicHJpc21hIiwieiIsImFkU2NoZW1hIiwib2JqZWN0IiwidGl0bGUiLCJzdHJpbmciLCJtaW4iLCJtYXgiLCJkZXNjcmlwdGlvbiIsInByaWNlIiwibnVtYmVyIiwiY2F0ZWdvcnkiLCJzdWJDYXRlZ29yeSIsIm9wdGlvbmFsIiwiY29uZGl0aW9uIiwiZW51bSIsImVycm9yTWFwIiwibWVzc2FnZSIsImNpdHkiLCJyZWdpb24iLCJhZGRyZXNzRGV0YWlsIiwiaW1hZ2VVcmxzIiwiYXJyYXkiLCJzcGVjaWZpY2F0aW9ucyIsInJlY29yZCIsImFueSIsImFkVHlwZSIsImRlZmF1bHQiLCJkdXJhdGlvbiIsIlBPU1QiLCJyZXF1ZXN0IiwiY29uc29sZSIsImxvZyIsInNlc3Npb24iLCJ1c2VyIiwiaWQiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwic3RhdHVzIiwiYm9keSIsInZhbGlkYXRlZERhdGEiLCJwYXJzZSIsImZpbmRVbmlxdWUiLCJ3aGVyZSIsInNlbGVjdCIsImZyZWVBZHNDb3VudCIsImZyZWVBZHNFeHBpcmVzQXQiLCJwYWlkQWRzQ291bnQiLCJwYWlkQWRzRXhwaXJlc0F0IiwiX2NvdW50IiwiYWRzIiwiaXNBY3RpdmUiLCJub3ciLCJEYXRlIiwiY2FuQ3JlYXRlIiwiYWREYXRhIiwidXNlcklkIiwiZnJlZUFkc0F2YWlsYWJsZSIsIk1hdGgiLCJjdXJyZW50QWN0aXZlQWRzIiwiZXhwaXJlc0F0IiwiaXNGcmVlQWQiLCJnZXRUaW1lIiwicGFpZEFkc0F2YWlsYWJsZSIsImlzUHJvbW90ZWQiLCJlcnJvck1zZyIsImNsZWFuQWREYXRhIiwicmVzdWx0IiwiYWQiLCJjcmVhdGUiLCJkYXRhIiwiaW5jbHVkZSIsIm5hbWUiLCJhdmF0YXIiLCJab2RFcnJvciIsImVycm9ycyIsIkdFVCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsInBhZ2UiLCJwYXJzZUludCIsImdldCIsImxpbWl0IiwibWluUHJpY2UiLCJtYXhQcmljZSIsInNlYXJjaCIsInNraXAiLCJPUiIsImd0ZSIsInBhcnNlRmxvYXQiLCJsdGUiLCJjb250YWlucyIsIm1vZGUiLCJvcmRlckJ5IiwiY3JlYXRlZEF0IiwidG90YWwiLCJQcm9taXNlIiwiYWxsIiwiZmluZE1hbnkiLCJyYXRpbmdBdmVyYWdlIiwicmF0aW5nQ291bnQiLCJ0YWtlIiwiY291bnQiLCJ0b3RhbFBhZ2VzIiwiY2VpbCIsInBhZ2luYXRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ads/create/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                emailOrPhone: {\n                    label: \"البريد الإلكتروني أو رقم الهاتف\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"كلمة المرور\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.emailOrPhone || !credentials?.password) {\n                    return null;\n                }\n                // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findFirst({\n                    where: {\n                        OR: [\n                            {\n                                email: credentials.emailOrPhone\n                            },\n                            {\n                                phone: credentials.emailOrPhone\n                            }\n                        ],\n                        isActive: true\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                // التحقق من كلمة المرور\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(credentials.password, user.passwordHash);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    name: user.name,\n                    email: user.email,\n                    phone: user.phone,\n                    role: user.role,\n                    avatar: user.avatar || undefined\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.phone = user.phone;\n                token.avatar = user.avatar;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.phone = token.phone;\n                session.user.avatar = token.avatar;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNrRTtBQUNwQztBQUNJO0FBRTNCLE1BQU1HLGNBQStCO0lBQzFDQyxXQUFXO1FBQ1RKLDJFQUFtQkEsQ0FBQztZQUNsQkssTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxjQUFjO29CQUNaQyxPQUFPO29CQUNQQyxNQUFNO2dCQUNSO2dCQUNBQyxVQUFVO29CQUFFRixPQUFPO29CQUFlQyxNQUFNO2dCQUFXO1lBQ3JEO1lBQ0EsTUFBTUUsV0FBVUwsV0FBVztnQkFDekIsSUFBSSxDQUFDQSxhQUFhQyxnQkFBZ0IsQ0FBQ0QsYUFBYUksVUFBVTtvQkFDeEQsT0FBTztnQkFDVDtnQkFFQSxxREFBcUQ7Z0JBQ3JELE1BQU1FLE9BQU8sTUFBTVYsMkNBQU1BLENBQUNVLElBQUksQ0FBQ0MsU0FBUyxDQUFDO29CQUN2Q0MsT0FBTzt3QkFDTEMsSUFBSTs0QkFDRjtnQ0FBRUMsT0FBT1YsWUFBWUMsWUFBWTs0QkFBQzs0QkFDbEM7Z0NBQUVVLE9BQU9YLFlBQVlDLFlBQVk7NEJBQUM7eUJBQ25DO3dCQUNEVyxVQUFVO29CQUNaO2dCQUNGO2dCQUVBLElBQUksQ0FBQ04sTUFBTTtvQkFDVCxPQUFPO2dCQUNUO2dCQUVBLHdCQUF3QjtnQkFDeEIsTUFBTU8sa0JBQWtCLE1BQU1sQix3REFBYyxDQUMxQ0ssWUFBWUksUUFBUSxFQUNwQkUsS0FBS1MsWUFBWTtnQkFHbkIsSUFBSSxDQUFDRixpQkFBaUI7b0JBQ3BCLE9BQU87Z0JBQ1Q7Z0JBRUEsT0FBTztvQkFDTEcsSUFBSVYsS0FBS1UsRUFBRTtvQkFDWGpCLE1BQU1PLEtBQUtQLElBQUk7b0JBQ2ZXLE9BQU9KLEtBQUtJLEtBQUs7b0JBQ2pCQyxPQUFPTCxLQUFLSyxLQUFLO29CQUNqQk0sTUFBTVgsS0FBS1csSUFBSTtvQkFDZkMsUUFBUVosS0FBS1ksTUFBTSxJQUFJQztnQkFDekI7WUFDRjtRQUNGO0tBQ0Q7SUFDREMsU0FBUztRQUNQQyxVQUFVO0lBQ1o7SUFDQUMsV0FBVztRQUNULE1BQU1DLEtBQUksRUFBRUMsS0FBSyxFQUFFbEIsSUFBSSxFQUFFO1lBQ3ZCLElBQUlBLE1BQU07Z0JBQ1JrQixNQUFNUCxJQUFJLEdBQUdYLEtBQUtXLElBQUk7Z0JBQ3RCTyxNQUFNYixLQUFLLEdBQUdMLEtBQUtLLEtBQUs7Z0JBQ3hCYSxNQUFNTixNQUFNLEdBQUdaLEtBQUtZLE1BQU07WUFDNUI7WUFDQSxPQUFPTTtRQUNUO1FBQ0EsTUFBTUosU0FBUSxFQUFFQSxPQUFPLEVBQUVJLEtBQUssRUFBRTtZQUM5QixJQUFJQSxPQUFPO2dCQUNUSixRQUFRZCxJQUFJLENBQUNVLEVBQUUsR0FBR1EsTUFBTUMsR0FBRztnQkFDM0JMLFFBQVFkLElBQUksQ0FBQ1csSUFBSSxHQUFHTyxNQUFNUCxJQUFJO2dCQUM5QkcsUUFBUWQsSUFBSSxDQUFDSyxLQUFLLEdBQUdhLE1BQU1iLEtBQUs7Z0JBQ2hDUyxRQUFRZCxJQUFJLENBQUNZLE1BQU0sR0FBR00sTUFBTU4sTUFBTTtZQUNwQztZQUNBLE9BQU9FO1FBQ1Q7SUFDRjtJQUNBTSxPQUFPO1FBQ0xDLFFBQVE7SUFFVjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV0lOMTBcXERlc2t0b3BcXGthbGloYVxca2hhbGloYXRhbmJhYVxcc3JjXFxsaWJcXGF1dGgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dEF1dGhPcHRpb25zIH0gZnJvbSBcIm5leHQtYXV0aFwiO1xuaW1wb3J0IENyZWRlbnRpYWxzUHJvdmlkZXIgZnJvbSBcIm5leHQtYXV0aC9wcm92aWRlcnMvY3JlZGVudGlhbHNcIjtcbmltcG9ydCBiY3J5cHQgZnJvbSBcImJjcnlwdGpzXCI7XG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tIFwiLi9wcmlzbWFcIjtcblxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XG4gIHByb3ZpZGVyczogW1xuICAgIENyZWRlbnRpYWxzUHJvdmlkZXIoe1xuICAgICAgbmFtZTogXCJjcmVkZW50aWFsc1wiLFxuICAgICAgY3JlZGVudGlhbHM6IHtcbiAgICAgICAgZW1haWxPclBob25lOiB7XG4gICAgICAgICAgbGFiZWw6IFwi2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKINij2Ygg2LHZgtmFINin2YTZh9in2KrZgVwiLFxuICAgICAgICAgIHR5cGU6IFwidGV4dFwiLFxuICAgICAgICB9LFxuICAgICAgICBwYXNzd29yZDogeyBsYWJlbDogXCLZg9mE2YXYqSDYp9mE2YXYsdmI2LFcIiwgdHlwZTogXCJwYXNzd29yZFwiIH0sXG4gICAgICB9LFxuICAgICAgYXN5bmMgYXV0aG9yaXplKGNyZWRlbnRpYWxzKSB7XG4gICAgICAgIGlmICghY3JlZGVudGlhbHM/LmVtYWlsT3JQaG9uZSB8fCAhY3JlZGVudGlhbHM/LnBhc3N3b3JkKSB7XG4gICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDYp9mE2KjYrdirINi52YYg2KfZhNmF2LPYqtiu2K/ZhSDYqNin2YTYqNix2YrYryDYp9mE2KXZhNmD2KrYsdmI2YbZiiDYo9mIINix2YLZhSDYp9mE2YfYp9iq2YFcbiAgICAgICAgY29uc3QgdXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRGaXJzdCh7XG4gICAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICAgIE9SOiBbXG4gICAgICAgICAgICAgIHsgZW1haWw6IGNyZWRlbnRpYWxzLmVtYWlsT3JQaG9uZSB9LFxuICAgICAgICAgICAgICB7IHBob25lOiBjcmVkZW50aWFscy5lbWFpbE9yUGhvbmUgfSxcbiAgICAgICAgICAgIF0sXG4gICAgICAgICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINmD2YTZhdipINin2YTZhdix2YjYsVxuICAgICAgICBjb25zdCBpc1Bhc3N3b3JkVmFsaWQgPSBhd2FpdCBiY3J5cHQuY29tcGFyZShcbiAgICAgICAgICBjcmVkZW50aWFscy5wYXNzd29yZCxcbiAgICAgICAgICB1c2VyLnBhc3N3b3JkSGFzaFxuICAgICAgICApO1xuXG4gICAgICAgIGlmICghaXNQYXNzd29yZFZhbGlkKSB7XG4gICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICAgIG5hbWU6IHVzZXIubmFtZSxcbiAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgICBwaG9uZTogdXNlci5waG9uZSxcbiAgICAgICAgICByb2xlOiB1c2VyLnJvbGUsXG4gICAgICAgICAgYXZhdGFyOiB1c2VyLmF2YXRhciB8fCB1bmRlZmluZWQsXG4gICAgICAgIH07XG4gICAgICB9LFxuICAgIH0pLFxuICBdLFxuICBzZXNzaW9uOiB7XG4gICAgc3RyYXRlZ3k6IFwiand0XCIsXG4gIH0sXG4gIGNhbGxiYWNrczoge1xuICAgIGFzeW5jIGp3dCh7IHRva2VuLCB1c2VyIH0pIHtcbiAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgIHRva2VuLnJvbGUgPSB1c2VyLnJvbGU7XG4gICAgICAgIHRva2VuLnBob25lID0gdXNlci5waG9uZTtcbiAgICAgICAgdG9rZW4uYXZhdGFyID0gdXNlci5hdmF0YXI7XG4gICAgICB9XG4gICAgICByZXR1cm4gdG9rZW47XG4gICAgfSxcbiAgICBhc3luYyBzZXNzaW9uKHsgc2Vzc2lvbiwgdG9rZW4gfSkge1xuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIHNlc3Npb24udXNlci5pZCA9IHRva2VuLnN1YiE7XG4gICAgICAgIHNlc3Npb24udXNlci5yb2xlID0gdG9rZW4ucm9sZSBhcyBzdHJpbmc7XG4gICAgICAgIHNlc3Npb24udXNlci5waG9uZSA9IHRva2VuLnBob25lIGFzIHN0cmluZztcbiAgICAgICAgc2Vzc2lvbi51c2VyLmF2YXRhciA9IHRva2VuLmF2YXRhciBhcyBzdHJpbmc7XG4gICAgICB9XG4gICAgICByZXR1cm4gc2Vzc2lvbjtcbiAgICB9LFxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogXCIvYXV0aC9sb2dpblwiLFxuICAgIC8vIHNpZ25VcDogXCIvYXV0aC9yZWdpc3RlclwiLCAvLyDYutmK2LEg2YXYr9i52YjZhSDZgdmKIE5leHRBdXRoXG4gIH0sXG59O1xuIl0sIm5hbWVzIjpbIkNyZWRlbnRpYWxzUHJvdmlkZXIiLCJiY3J5cHQiLCJwcmlzbWEiLCJhdXRoT3B0aW9ucyIsInByb3ZpZGVycyIsIm5hbWUiLCJjcmVkZW50aWFscyIsImVtYWlsT3JQaG9uZSIsImxhYmVsIiwidHlwZSIsInBhc3N3b3JkIiwiYXV0aG9yaXplIiwidXNlciIsImZpbmRGaXJzdCIsIndoZXJlIiwiT1IiLCJlbWFpbCIsInBob25lIiwiaXNBY3RpdmUiLCJpc1Bhc3N3b3JkVmFsaWQiLCJjb21wYXJlIiwicGFzc3dvcmRIYXNoIiwiaWQiLCJyb2xlIiwiYXZhdGFyIiwidW5kZWZpbmVkIiwic2Vzc2lvbiIsInN0cmF0ZWd5IiwiY2FsbGJhY2tzIiwiand0IiwidG9rZW4iLCJzdWIiLCJwYWdlcyIsInNpZ25JbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO0tBQVE7QUFDaEIsR0FBRTtBQUVKLElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXSU4xMFxcRGVza3RvcFxca2FsaWhhXFxraGFsaWhhdGFuYmFhXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknXSxcbiAgfSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fcreate%2Froute&page=%2Fapi%2Fads%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWIN10%5CDesktop%5Ckaliha%5Ckhalihatanbaa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();