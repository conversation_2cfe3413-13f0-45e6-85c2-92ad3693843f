import { AdvVideoCodecType, VideoCodecType } from "./videoCodecType/VideoCodecType.js";
/**
 * @description Determines the video codec to use.
 * @memberOf Qualifiers
 * @namespace VideoCodec
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @summary qualifier
 * @description Auto video codec.
 * @memberOf Qualifiers.VideoCodec
 * @returns {Qualifiers.VideoCodec.VideoCodecType}
 */
declare function auto(): VideoCodecType;
/**
 * @summary qualifier
 * @description Video codec h264.
 * @memberOf Qualifiers.VideoCodec
 * @returns {Qualifiers.VideoCodec.AdvVideoCodecType}
 */
declare function h264(): AdvVideoCodecType;
/**
 * @summary qualifier
 * @description h265 video codec.
 * @memberOf Qualifiers.VideoCodec
 * @returns {Qualifiers.VideoCodec.VideoCodecType}
 */
declare function h265(): VideoCodecType;
/**
 * @summary qualifier
 * @description Video codec proRes (Apple ProRes 422 HQ).
 * @memberOf Qualifiers.VideoCodec
 * @returns {Qualifiers.VideoCodec.VideoCodecType}
 */
declare function proRes(): VideoCodecType;
/**
 * @summary qualifier
 * @description Video codec theora.
 * @memberOf Qualifiers.VideoCodec
 * @returns {Qualifiers.VideoCodec.VideoCodecType}
 */
declare function theora(): VideoCodecType;
/**
 * @summary qualifier
 * @description Video codec vp8.
 * @memberOf Qualifiers.VideoCodec
 * @returns {Qualifiers.VideoCodec.VideoCodecType}
 */
declare function vp8(): VideoCodecType;
/**
 * @summary qualifier
 * @description Video codec vp9.
 * @memberOf Qualifiers.VideoCodec
 * @returns {Qualifiers.VideoCodec.VideoCodecType}
 */
declare function vp9(): VideoCodecType;
export declare const VIDEO_CODEC_TO_TRANSFORMATION: Record<string, VideoCodecType | AdvVideoCodecType>;
declare const VideoCodec: {
    auto: typeof auto;
    h264: typeof h264;
    h265: typeof h265;
    proRes: typeof proRes;
    theora: typeof theora;
    vp8: typeof vp8;
    vp9: typeof vp9;
};
export { VideoCodec, auto, h264, h265, proRes, theora, vp8, vp9 };
