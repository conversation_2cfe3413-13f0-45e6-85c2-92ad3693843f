import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    // جلب جميع الرسائل للمستخدم
    const allMessages = await prisma.message.findMany({
      where: {
        OR: [{ fromId: session.user.id }, { toId: session.user.id }],
      },
      orderBy: { createdAt: "desc" },
    });

    // تجميع الرسائل حسب المحادثة
    const conversationMap = new Map();

    for (const message of allMessages) {
      const otherUserId =
        message.fromId === session.user.id ? message.toId : message.fromId;
      const conversationKey = `${otherUserId}-${message.adId || "general"}`;

      if (!conversationMap.has(conversationKey)) {
        conversationMap.set(conversationKey, {
          ...message,
          otherUserId,
        });
      }
    }

    const conversations = Array.from(conversationMap.values());

    // جلب بيانات المستخدمين والإعلانات
    const userIds = [...new Set(conversations.map((c) => c.otherUserId))];
    const adIds = [
      ...new Set(conversations.map((c) => c.adId).filter(Boolean)),
    ];

    const [users, ads] = await Promise.all([
      prisma.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true, avatar: true },
      }),
      adIds.length > 0
        ? prisma.ad.findMany({
            where: { id: { in: adIds } },
            select: { id: true, title: true, price: true, imageUrls: true },
          })
        : [],
    ]);

    // تجميع البيانات
    const formattedConversations = conversations.map((conv) => {
      const otherUser = users.find((u) => u.id === conv.otherUserId);
      const ad = conv.adId ? ads.find((a) => a.id === conv.adId) : null;

      // حساب الرسائل غير المقروءة
      const unreadCount = 0; // يمكن تحسينه لاحقاً

      return {
        id: `${conv.otherUserId}-${conv.adId || "general"}`,
        content: conv.content,
        createdAt: conv.createdAt,
        adId: conv.adId,
        otherUser: {
          id: otherUser?.id || conv.otherUserId,
          name: otherUser?.name || "مستخدم غير معروف",
          avatar: otherUser?.avatar,
        },
        ad: ad
          ? {
              id: ad.id,
              title: ad.title,
              price: ad.price,
              imageUrls: ad.imageUrls,
            }
          : null,
        unreadCount,
      };
    });

    return NextResponse.json({
      success: true,
      data: formattedConversations,
    });
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في جلب المحادثات" },
      { status: 500 }
    );
  }
}
