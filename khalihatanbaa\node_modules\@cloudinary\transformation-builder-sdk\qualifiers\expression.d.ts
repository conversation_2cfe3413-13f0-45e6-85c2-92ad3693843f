import { ExpressionQualifier } from "./expression/ExpressionQualifier.js";
/**
 * @description
 * Used for variable or conditional expressions
 * <b>Learn more:</b> {@link https://cloudinary.com/documentation/user_defined_variables#arithmetic_expressions|Arithmetic expressions }
 * @namespace Expression
 * @memberOf Qualifiers
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.Expression
 * @return {Qualifiers.Expression.ExpressionQualifier}
 */
declare function expression(exp: string): ExpressionQualifier;
declare const Expression: {
    expression: typeof expression;
};
export { Expression, expression };
