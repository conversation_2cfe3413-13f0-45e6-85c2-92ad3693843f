import { Action } from "../../internal/Action.js";
import { stringOrNumber } from "../../types/types.js";
/**
 * @description Skews the image according to the two specified values in degrees.
 * @extends SDK.Action
 * @memberOf Actions.Reshape
 * @see Visit {@link Actions.Reshape| Reshape} for examples
 */
declare class ShearAction extends Action {
    private _x;
    private _y;
    constructor(x: stringOrNumber, y: stringOrNumber);
    /**
     * @param {number} x Skews the image according to the two specified values in degrees. (X and Y)
     */
    skewX(x: stringOrNumber): this;
    /**
     * @param {number} y Skews the image according to the two specified values in degrees. (X and Y)
     */
    skewY(y: stringOrNumber): this;
    toString(): string;
}
export { ShearAction };
