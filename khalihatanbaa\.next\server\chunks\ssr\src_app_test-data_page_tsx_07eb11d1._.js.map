{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/test-data/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\n\nexport default function TestDataPage() {\n  const { data: session } = useSession();\n  const [data, setData] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      // جلب المفضلة\n      const favResponse = await fetch(\"/api/favorites\");\n      const favData = await favResponse.json();\n\n      // جلب الرسائل\n      const msgResponse = await fetch(\"/api/messages\");\n      const msgData = await msgResponse.json();\n\n      setData({\n        session,\n        favorites: favData,\n        messages: msgData,\n      });\n    } catch (error) {\n      console.error(\"Error:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const addTestFavorite = async () => {\n    try {\n      const response = await fetch(\"/api/test-favorites\", {\n        method: \"POST\",\n      });\n      const result = await response.json();\n\n      if (result.success) {\n        alert(\"تم إضافة مفضلة تجريبية!\");\n        fetchData(); // إعادة جلب البيانات\n      } else {\n        alert(result.error);\n      }\n    } catch (error) {\n      console.error(\"Error adding test favorite:\", error);\n      alert(\"حدث خطأ\");\n    }\n  };\n\n  if (loading) {\n    return <div className=\"p-8\">جاري التحميل...</div>;\n  }\n\n  return (\n    <div className=\"p-8 max-w-4xl mx-auto\">\n      <h1 className=\"text-2xl font-bold mb-6\">اختبار البيانات</h1>\n\n      <div className=\"mb-6\">\n        <button\n          onClick={addTestFavorite}\n          className=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\"\n        >\n          إضافة مفضلة تجريبية\n        </button>\n      </div>\n\n      <div className=\"space-y-6\">\n        {/* معلومات الجلسة */}\n        <div className=\"bg-white p-4 rounded-lg border\">\n          <h2 className=\"text-lg font-semibold mb-3\">معلومات الجلسة</h2>\n          <pre className=\"bg-gray-100 p-3 rounded text-sm overflow-auto\">\n            {JSON.stringify(data?.session, null, 2)}\n          </pre>\n        </div>\n\n        {/* المفضلة */}\n        <div className=\"bg-white p-4 rounded-lg border\">\n          <h2 className=\"text-lg font-semibold mb-3\">المفضلة</h2>\n          <pre className=\"bg-gray-100 p-3 rounded text-sm overflow-auto\">\n            {JSON.stringify(data?.favorites, null, 2)}\n          </pre>\n        </div>\n\n        {/* الرسائل */}\n        <div className=\"bg-white p-4 rounded-lg border\">\n          <h2 className=\"text-lg font-semibold mb-3\">الرسائل</h2>\n          <pre className=\"bg-gray-100 p-3 rounded text-sm overflow-auto\">\n            {JSON.stringify(data?.messages, null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,cAAc;YACd,MAAM,cAAc,MAAM,MAAM;YAChC,MAAM,UAAU,MAAM,YAAY,IAAI;YAEtC,cAAc;YACd,MAAM,cAAc,MAAM,MAAM;YAChC,MAAM,UAAU,MAAM,YAAY,IAAI;YAEtC,QAAQ;gBACN;gBACA,WAAW;gBACX,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,UAAU;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;YACV;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;gBACN,aAAa,qBAAqB;YACpC,OAAO;gBACL,MAAM,OAAO,KAAK;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,IAAI,SAAS;QACX,qBAAO,8OAAC;YAAI,WAAU;sBAAM;;;;;;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,MAAM,SAAS,MAAM;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,MAAM,WAAW,MAAM;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,MAAM,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMlD", "debugId": null}}]}