{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\nimport bcrypt from \"bcryptjs\";\nimport { prisma } from \"./prisma\";\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        emailOrPhone: {\n          label: \"البريد الإلكتروني أو رقم الهاتف\",\n          type: \"text\",\n        },\n        password: { label: \"كلمة المرور\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null;\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone },\n            ],\n            isActive: true,\n          },\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        };\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.phone = user.phone;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.phone = token.phone as string;\n        session.user.avatar = token.avatar as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/auth/login\",\n    // signUp: \"/auth/register\", // غير مدعوم في NextAuth\n  },\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBACZ,OAAO;oBACP,MAAM;gBACR;gBACA,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IAEV;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/api/favorites/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\n\n// مخطط التحقق من إضافة/إزالة المفضلة\nconst favoriteSchema = z.object({\n  adId: z.string().min(1, 'معرف الإعلان مطلوب')\n})\n\n// جلب المفضلة\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: 'يجب تسجيل الدخول أولاً' },\n        { status: 401 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '12')\n    const skip = (page - 1) * limit\n\n    // جلب المفضلة مع تفاصيل الإعلانات\n    const favorites = await prisma.favorite.findMany({\n      where: {\n        userId: session.user.id\n      },\n      include: {\n        ad: {\n          include: {\n            user: {\n              select: { id: true, name: true, avatar: true }\n            }\n          }\n        }\n      },\n      orderBy: { createdAt: 'desc' },\n      skip,\n      take: limit\n    })\n\n    // حساب العدد الإجمالي\n    const total = await prisma.favorite.count({\n      where: {\n        userId: session.user.id\n      }\n    })\n\n    const totalPages = Math.ceil(total / limit)\n\n    return NextResponse.json({\n      success: true,\n      data: favorites,\n      pagination: {\n        page,\n        limit,\n        total,\n        totalPages\n      }\n    })\n\n  } catch (error) {\n    console.error('Error fetching favorites:', error)\n    return NextResponse.json(\n      { success: false, error: 'حدث خطأ في جلب المفضلة' },\n      { status: 500 }\n    )\n  }\n}\n\n// إضافة إلى المفضلة\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: 'يجب تسجيل الدخول أولاً' },\n        { status: 401 }\n      )\n    }\n\n    const body = await request.json()\n    const validatedData = favoriteSchema.parse(body)\n\n    // التحقق من وجود الإعلان\n    const ad = await prisma.ad.findUnique({\n      where: { id: validatedData.adId }\n    })\n\n    if (!ad) {\n      return NextResponse.json(\n        { success: false, error: 'الإعلان غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    // التحقق من عدم وجود الإعلان في المفضلة مسبقاً\n    const existingFavorite = await prisma.favorite.findUnique({\n      where: {\n        userId_adId: {\n          userId: session.user.id,\n          adId: validatedData.adId\n        }\n      }\n    })\n\n    if (existingFavorite) {\n      return NextResponse.json(\n        { success: false, error: 'الإعلان موجود في المفضلة بالفعل' },\n        { status: 400 }\n      )\n    }\n\n    // إضافة إلى المفضلة\n    const favorite = await prisma.favorite.create({\n      data: {\n        userId: session.user.id,\n        adId: validatedData.adId\n      },\n      include: {\n        ad: {\n          include: {\n            user: {\n              select: { id: true, name: true, avatar: true }\n            }\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: favorite,\n      message: 'تم إضافة الإعلان إلى المفضلة'\n    })\n\n  } catch (error) {\n    console.error('Error adding to favorites:', error)\n    \n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { success: false, error: error.errors[0].message },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { success: false, error: 'حدث خطأ في إضافة الإعلان إلى المفضلة' },\n      { status: 500 }\n    )\n  }\n}\n\n// إزالة من المفضلة\nexport async function DELETE(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: 'يجب تسجيل الدخول أولاً' },\n        { status: 401 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n    const adId = searchParams.get('adId')\n\n    if (!adId) {\n      return NextResponse.json(\n        { success: false, error: 'معرف الإعلان مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    // البحث عن المفضلة\n    const favorite = await prisma.favorite.findUnique({\n      where: {\n        userId_adId: {\n          userId: session.user.id,\n          adId: adId\n        }\n      }\n    })\n\n    if (!favorite) {\n      return NextResponse.json(\n        { success: false, error: 'الإعلان غير موجود في المفضلة' },\n        { status: 404 }\n      )\n    }\n\n    // إزالة من المفضلة\n    await prisma.favorite.delete({\n      where: {\n        id: favorite.id\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: 'تم إزالة الإعلان من المفضلة'\n    })\n\n  } catch (error) {\n    console.error('Error removing from favorites:', error)\n    return NextResponse.json(\n      { success: false, error: 'حدث خطأ في إزالة الإعلان من المفضلة' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,qCAAqC;AACrC,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC1B;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,kCAAkC;QAClC,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;YACA,SAAS;gBACP,IAAI;oBACF,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCAAE,IAAI;gCAAM,MAAM;gCAAM,QAAQ;4BAAK;wBAC/C;oBACF;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;YAC7B;YACA,MAAM;QACR;QAEA,sBAAsB;QACtB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YACxC,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAyB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,eAAe,KAAK,CAAC;QAE3C,yBAAyB;QACzB,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,IAAI,cAAc,IAAI;YAAC;QAClC;QAEA,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YACxD,OAAO;gBACL,aAAa;oBACX,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBACvB,MAAM,cAAc,IAAI;gBAC1B;YACF;QACF;QAEA,IAAI,kBAAkB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,MAAM,cAAc,IAAI;YAC1B;YACA,SAAS;gBACP,IAAI;oBACF,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCAAE,IAAI;gCAAM,MAAM;gCAAM,QAAQ;4BAAK;wBAC/C;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAE5C,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAAC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAuC,GAChE;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAqB,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBACL,aAAa;oBACX,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBACvB,MAAM;gBACR;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA+B,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,OAAO;gBACL,IAAI,SAAS,EAAE;YACjB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAsC,GAC/D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}