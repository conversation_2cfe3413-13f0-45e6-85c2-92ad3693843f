"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { HeartIcon, MapPinIcon, ClockIcon } from "@heroicons/react/24/outline";
import { HeartIcon as HeartSolidIcon } from "@heroicons/react/24/solid";
import { Ad } from "@/types";
import { PlaceholderImage } from "@/components/ui/PlaceholderImage";

// بيانات تجريبية للإعلانات
const sampleAds: Ad[] = [
  {
    id: "1",
    title: "تويوتا كامري 2018 فل كامل",
    description: "سيارة بحالة ممتازة، صيانة دورية، لون أبيض",
    price: 45000000,
    category: "سيارات",
    subCategory: "تويوتا",
    condition: "مستعمل",
    city: "دمشق",
    region: "المزة",
    imageUrls: ["/placeholder-car.jpg"],
    views: 125,
    isActive: true,
    isFreeAd: false,
    isPromoted: true,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
    userId: "user1",
    specifications: {
      year: 2018,
      mileage: 85000,
      fuel: "بنزين",
      color: "أبيض",
    },
  },
  {
    id: "2",
    title: "شقة 3 غرف في المالكي",
    description: "شقة مفروشة بالكامل، إطلالة رائعة، قريبة من الخدمات",
    price: 120000000,
    category: "عقارات",
    subCategory: "شقة",
    condition: "مستعمل",
    city: "دمشق",
    region: "المالكي",
    imageUrls: ["/placeholder-apartment.jpg"],
    views: 89,
    isActive: true,
    isFreeAd: true,
    isPromoted: false,
    createdAt: new Date("2024-01-14"),
    updatedAt: new Date("2024-01-14"),
    userId: "user2",
    specifications: {
      rooms: 3,
      bathrooms: 2,
      area: 120,
      floor: 3,
    },
  },
  {
    id: "3",
    title: "آيفون 14 برو ماكس 256 جيجا",
    description: "جهاز جديد لم يستعمل، مع الكرتونة والشاحن الأصلي",
    price: 8500000,
    category: "إلكترونيات",
    subCategory: "هاتف ذكي",
    condition: "جديد",
    city: "حلب",
    region: "الفرقان",
    imageUrls: ["/placeholder-phone.jpg"],
    views: 234,
    isActive: true,
    isFreeAd: false,
    isPromoted: true,
    createdAt: new Date("2024-01-13"),
    updatedAt: new Date("2024-01-13"),
    userId: "user3",
    specifications: {
      brand: "Apple",
      model: "iPhone 14 Pro Max",
      storage: "256GB",
      color: "أسود",
    },
  },
  {
    id: "4",
    title: "طقم غرفة نوم خشب زان",
    description: "طقم كامل: سرير + دولاب + تسريحة، خشب زان طبيعي",
    price: 15000000,
    category: "أثاث",
    subCategory: "غرفة نوم",
    condition: "مستعمل",
    city: "حمص",
    region: "الوعر",
    imageUrls: ["/placeholder-furniture.jpg"],
    views: 67,
    isActive: true,
    isFreeAd: true,
    isPromoted: false,
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-12"),
    userId: "user4",
    specifications: {
      material: "خشب زان",
      pieces: 3,
      condition: "ممتاز",
    },
  },
];

export function FeaturedAds() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  useEffect(() => {
    // في التطبيق الحقيقي، سنجلب البيانات من API
    setAds(sampleAds);
  }, []);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("ar-SY").format(price);
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "منذ يوم واحد";
    if (diffDays < 7) return `منذ ${diffDays} أيام`;
    if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`;
    return `منذ ${Math.ceil(diffDays / 30)} شهر`;
  };

  const toggleFavorite = (adId: string) => {
    setFavorites((prev) => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(adId)) {
        newFavorites.delete(adId);
      } else {
        newFavorites.add(adId);
      }
      return newFavorites;
    });
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-dark-800 mb-4">
            أحدث الإعلانات
          </h2>
          <p className="text-lg text-dark-600 max-w-2xl mx-auto">
            اكتشف أحدث العروض والمنتجات المتاحة في جميع أنحاء سوريا
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {ads.map((ad) => (
            <div
              key={ad.id}
              className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300"
            >
              {/* صورة الإعلان */}
              <div className="relative h-48 bg-gray-200">
                <Image
                  src={ad.imageUrls[0] || "/placeholder-image.jpg"}
                  alt={ad.title}
                  fill
                  className="object-cover"
                />

                {/* شارة الإعلان المميز */}
                {ad.isPromoted && (
                  <div className="absolute top-3 right-3 bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                    مميز
                  </div>
                )}

                {/* زر المفضلة */}
                <button
                  onClick={() => toggleFavorite(ad.id)}
                  className="absolute top-3 left-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
                >
                  {favorites.has(ad.id) ? (
                    <HeartSolidIcon className="h-5 w-5 text-red-500" />
                  ) : (
                    <HeartIcon className="h-5 w-5 text-gray-600" />
                  )}
                </button>
              </div>

              {/* محتوى الإعلان */}
              <div className="p-4">
                <Link href={`/ads/${ad.id}`}>
                  <h3 className="font-semibold text-dark-800 mb-2 hover:text-primary-500 transition-colors line-clamp-2">
                    {ad.title}
                  </h3>
                </Link>

                <div className="flex items-center text-sm text-dark-500 mb-2">
                  <MapPinIcon className="h-4 w-4 ml-1" />
                  {ad.city} - {ad.region}
                </div>

                <div className="flex items-center text-sm text-dark-500 mb-3">
                  <ClockIcon className="h-4 w-4 ml-1" />
                  {formatDate(ad.createdAt)}
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-lg font-bold text-primary-500">
                    {formatPrice(ad.price)} <span className="text-sm">ل.س</span>
                  </div>
                  <div className="text-sm text-dark-500">{ad.views} مشاهدة</div>
                </div>

                <div className="mt-3 flex items-center justify-between">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      ad.condition === "جديد"
                        ? "bg-green-100 text-green-800"
                        : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {ad.condition}
                  </span>

                  {ad.isFreeAd && (
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                      إعلان مجاني
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* رابط عرض المزيد */}
        <div className="text-center mt-12">
          <Link
            href="/ads"
            className="inline-flex items-center px-8 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors font-semibold"
          >
            عرض جميع الإعلانات
            <svg
              className="mr-2 h-5 w-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
