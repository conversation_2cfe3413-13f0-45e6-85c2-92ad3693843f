{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/providers/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/AuthProvider.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2EACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/providers/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/AuthProvider.tsx\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport { AuthProvider } from \"@/components/providers/AuthProvider\";\n\nexport const metadata: Metadata = {\n  title: \"خَلّيها تنْباع - منصة البيع والشراء في سوريا\",\n  description:\n    \"منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا. اعثر على أفضل العروض للعقارات، السيارات، الإلكترونيات وأكثر.\",\n  keywords: \"بيع، شراء، سوريا، إعلانات، عقارات، سيارات، إلكترونيات\",\n  authors: [{ name: \"خَلّيها تنْباع\" }],\n  openGraph: {\n    title: \"خَلّيها تنْباع - منصة البيع والشراء في سوريا\",\n    description: \"منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا\",\n    url: \"https://khalihatanbaa.com\",\n    siteName: \"خَلّيها تنْباع\",\n    locale: \"ar_SY\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"خَلّيها تنْباع - منصة البيع والشراء في سوريا\",\n    description: \"منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"ar\" dir=\"rtl\">\n      <body className=\"font-arabic antialiased\">\n        <AuthProvider>{children}</AuthProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aACE;IACF,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAiB;KAAE;IACrC,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;kBAClB,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,+IAAA,CAAA,eAAY;0BAAE;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}