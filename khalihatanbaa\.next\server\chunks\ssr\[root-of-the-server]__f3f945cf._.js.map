{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/auth/register/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'\n\nexport default function RegisterPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: ''\n  })\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState('')\n  \n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n    setSuccess('')\n\n    // التحقق من تطابق كلمات المرور\n    if (formData.password !== formData.confirmPassword) {\n      setError('كلمات المرور غير متطابقة')\n      setIsLoading(false)\n      return\n    }\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          phone: formData.phone,\n          password: formData.password\n        }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        setSuccess('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول')\n        setTimeout(() => {\n          router.push('/auth/login')\n        }, 2000)\n      } else {\n        setError(data.error || 'حدث خطأ أثناء إنشاء الحساب')\n      }\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }))\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"flex justify-center\">\n            <div className=\"w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center\">\n              <span className=\"text-white font-bold text-2xl\">خ</span>\n            </div>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-bold text-dark-800\">\n            إنشاء حساب جديد\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-dark-600\">\n            أو{' '}\n            <Link\n              href=\"/auth/login\"\n              className=\"font-medium text-primary-600 hover:text-primary-500\"\n            >\n              تسجيل الدخول إلى حسابك\n            </Link>\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-dark-700\">\n                الاسم الكامل\n              </label>\n              <input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-dark-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\n                placeholder=\"أدخل اسمك الكامل\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-dark-700\">\n                البريد الإلكتروني\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-dark-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\n                placeholder=\"أدخل بريدك الإلكتروني\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-dark-700\">\n                رقم الهاتف\n              </label>\n              <input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                required\n                value={formData.phone}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-dark-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\n                placeholder=\"أدخل رقم هاتفك\"\n                dir=\"ltr\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-dark-700\">\n                كلمة المرور\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  className=\"appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-dark-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"أدخل كلمة المرور (6 أحرف على الأقل)\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-dark-700\">\n                تأكيد كلمة المرور\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type={showConfirmPassword ? 'text' : 'password'}\n                  required\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  className=\"appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-dark-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"أعد إدخال كلمة المرور\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                >\n                  {showConfirmPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n              {error}\n            </div>\n          )}\n\n          {success && (\n            <div className=\"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg text-sm\">\n              {success}\n            </div>\n          )}\n\n          <div className=\"flex items-center\">\n            <input\n              id=\"agree-terms\"\n              name=\"agree-terms\"\n              type=\"checkbox\"\n              required\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"agree-terms\" className=\"mr-2 block text-sm text-dark-900\">\n              أوافق على{' '}\n              <Link href=\"/terms\" className=\"text-primary-600 hover:text-primary-500\">\n                شروط الاستخدام\n              </Link>{' '}\n              و{' '}\n              <Link href=\"/privacy\" className=\"text-primary-600 hover:text-primary-500\">\n                سياسة الخصوصية\n              </Link>\n            </label>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب'}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-dark-600\">\n              لديك حساب بالفعل؟{' '}\n              <Link\n                href=\"/auth/login\"\n                className=\"font-medium text-primary-600 hover:text-primary-500\"\n              >\n                تسجيل الدخول\n              </Link>\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QACT,WAAW;QAEX,+BAA+B;QAC/B,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;gBAC7B;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW;gBACX,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;sCAGpD,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;;gCAAyC;gCACjD;8CACH,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,KAAI;;;;;;;;;;;;8CAIR,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,eAAe,SAAS;oDAC9B,QAAQ;oDACR,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB,CAAC;8DAE/B,6BACC,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;6EAExB,8OAAC,6MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM3B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA0C;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,sBAAsB,SAAS;oDACrC,QAAQ;oDACR,OAAO,SAAS,eAAe;oDAC/B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,uBAAuB,CAAC;8DAEtC,oCACC,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;6EAExB,8OAAC,6MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAO5B,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;wBAIJ,yBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;;wCAAmC;wCAC9D;sDACV,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA0C;;;;;;wCAEhE;wCAAI;wCACV;sDACF,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;sCAM9E,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,yBAAyB;;;;;;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCACjB;kDAClB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}