'use client'

import { useState } from 'react'
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { CATEGORIES } from '@/types'

export function Hero() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    const params = new URLSearchParams()
    if (searchQuery.trim()) params.set('q', searchQuery)
    if (selectedCategory) params.set('category', selectedCategory)
    
    window.location.href = `/search?${params.toString()}`
  }

  return (
    <section className="bg-gradient-to-br from-primary-50 to-secondary-50 py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* العنوان الرئيسي */}
          <h1 className="text-4xl lg:text-6xl font-bold text-dark-800 mb-6">
            <span className="text-primary-500">خَلّيها</span>{' '}
            <span className="text-secondary-500">تنْباع</span>
          </h1>
          
          <p className="text-xl lg:text-2xl text-dark-600 mb-8 max-w-3xl mx-auto">
            منصة البيع والشراء الأولى في سوريا
            <br />
            <span className="text-lg text-dark-500">
              اعثر على أفضل العروض أو بع منتجاتك بسهولة
            </span>
          </p>

          {/* شريط البحث المتقدم */}
          <div className="max-w-4xl mx-auto">
            <form onSubmit={handleSearch} className="bg-white rounded-2xl shadow-lg p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* حقل البحث */}
                <div className="flex-1 relative">
                  <input
                    type="text"
                    placeholder="ابحث عن أي شيء... (سيارة، شقة، هاتف، إلخ)"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
                </div>

                {/* اختيار الفئة */}
                <div className="lg:w-64">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">جميع الفئات</option>
                    {Object.entries(CATEGORIES).map(([key, category]) => (
                      <option key={key} value={key}>
                        {category.icon} {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* زر البحث */}
                <button
                  type="submit"
                  className="lg:w-32 bg-primary-500 text-white px-8 py-4 rounded-xl hover:bg-primary-600 transition-colors font-semibold text-lg"
                >
                  بحث
                </button>
              </div>
            </form>
          </div>

          {/* إحصائيات سريعة */}
          <div className="mt-16 grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-primary-500 mb-2">
                1000+
              </div>
              <div className="text-dark-600">إعلان نشط</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-secondary-500 mb-2">
                500+
              </div>
              <div className="text-dark-600">مستخدم مسجل</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-primary-500 mb-2">
                14
              </div>
              <div className="text-dark-600">محافظة</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-secondary-500 mb-2">
                24/7
              </div>
              <div className="text-dark-600">دعم متواصل</div>
            </div>
          </div>

          {/* الكلمات المفتاحية الشائعة */}
          <div className="mt-12">
            <p className="text-dark-600 mb-4">البحث الشائع:</p>
            <div className="flex flex-wrap justify-center gap-3">
              {[
                'سيارات مستعملة',
                'شقق للبيع',
                'هواتف ذكية',
                'أثاث منزلي',
                'لابتوب',
                'دراجات نارية'
              ].map((keyword) => (
                <button
                  key={keyword}
                  onClick={() => {
                    setSearchQuery(keyword)
                    handleSearch({ preventDefault: () => {} } as React.FormEvent)
                  }}
                  className="px-4 py-2 bg-white text-dark-600 rounded-full border border-gray-300 hover:border-primary-500 hover:text-primary-500 transition-colors"
                >
                  {keyword}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
