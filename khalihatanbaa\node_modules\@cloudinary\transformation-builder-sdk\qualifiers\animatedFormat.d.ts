import { AnimatedFormatQualifierValue } from "./animatedFormat/AnimatedFormatQualifierValue.js";
/**
 * @description Contains methods to specify the animated format
 * @namespace AnimatedFormat
 * @memberOf Qualifiers
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @description Automatically sets the animated format
 * @summary qualifier
 * @memberOf Qualifiers.AnimatedFormat
 * @return {Qualifiers.AnimatedFormatQualifierValue}
 */
declare function auto(): AnimatedFormatQualifierValue;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AnimatedFormat
 * @return {Qualifiers.AnimatedFormatQualifierValue}
 */
declare function gif(): AnimatedFormatQualifierValue;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AnimatedFormat
 * @return {Qualifiers.AnimatedFormatQualifierValue}
 */
declare function webp(): AnimatedFormatQualifierValue;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AnimatedFormat
 * @return {Qualifiers.AnimatedFormatQualifierValue}
 */
declare function png(): AnimatedFormatQualifierValue;
declare const AnimatedFormat: {
    auto: typeof auto;
    gif: typeof gif;
    webp: typeof webp;
    png: typeof png;
};
export { auto, gif, webp, png, AnimatedFormat };
