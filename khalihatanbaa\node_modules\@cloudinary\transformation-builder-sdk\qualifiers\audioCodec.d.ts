/**
 * @description Contains functions to select an audio codec.
 * @memberOf Qualifiers
 * @namespace AudioCodec
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @summary qualifier
 * @description Removes the audio channel from the video, effectively muting it.
 * @memberOf Qualifiers.AudioCodec
 */
declare function none(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioCodec
 */
declare function aac(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioCodec
 */
declare function vorbis(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioCodec
 */
declare function mp3(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioCodec
 */
declare function opus(): string;
declare const AudioCodec: {
    aac: typeof aac;
    mp3: typeof mp3;
    opus: typeof opus;
    none: typeof none;
    vorbis: typeof vorbis;
};
export { AudioCodec, aac, mp3, opus, none, vorbis };
