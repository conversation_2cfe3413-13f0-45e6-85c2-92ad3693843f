'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib_es6 = require('../../tslib.es6-7a681263.cjs');
var internal_qualifier_Qualifier = require('../../internal/qualifier/Qualifier.cjs');
var internal_qualifier_QualifierValue = require('../../internal/qualifier/QualifierValue.cjs');
require('../../internal/models/QualifierModel.cjs');
require('../../internal/models/qualifierToJson.cjs');
require('../../internal/utils/unsupportedError.cjs');

/**
 * @memberOf Gravity.GravityQualifier
 * @extends {SDK.Qualifier}
 */
var GravityQualifier = /** @class */ (function (_super) {
    tslib_es6.__extends(GravityQualifier, _super);
    /**
     * @param value, an array containing (GravityObject | AutoGravity | string) or a string;
     */
    function GravityQualifier(value) {
        return _super.call(this, 'g', new internal_qualifier_QualifierValue.QualifierValue(value)) || this;
    }
    return GravityQualifier;
}(internal_qualifier_Qualifier.Qualifier));

exports.GravityQualifier = GravityQualifier;
