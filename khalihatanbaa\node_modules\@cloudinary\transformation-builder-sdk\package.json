{"name": "@cloudinary/transformation-builder-sdk", "version": "1.17.0", "description": "", "keywords": [], "author": "", "license": "MIT", "exports": {"./bundles/umd/package.json": "./bundles/umd/package.json", "./bundles/umd": {"require": "./bundles/umd/base.js", "import": "./bundles/umd/base.js"}, "./package.json": "./package.json", "./actions/*": {"require": "./actions/*.cjs", "import": "./actions/*.js"}, "./backwards/*": {"require": "./backwards/*.cjs", "import": "./backwards/*.js"}, "./bundles/*": {"require": "./bundles/*.cjs", "import": "./bundles/*.js"}, "./qualifiers/*": {"require": "./qualifiers/*.cjs", "import": "./qualifiers/*.js"}, "./transformation/*": {"require": "./transformation/*.cjs", "import": "./transformation/*.js"}, "./types/*": {"require": "./types/*.cjs", "import": "./types/*.js"}, "./*": {"require": "./*.cjs", "import": "./*.js"}, ".": {"require": "./index.cjs", "import": "./index.js", "node": "./index.js", "default": "./index.js"}}, "dependencies": {"@cloudinary/url-gen": "^1.7.0"}, "main": "./bundles/umd/base.js", "browser": "./index.js", "module": "./index.js", "type": "module", "sideEffects": false}