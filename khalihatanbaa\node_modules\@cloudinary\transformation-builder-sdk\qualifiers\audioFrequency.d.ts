/**
 * @description Controls the audio sampling frequency.
 * @namespace AudioFrequency
 * @memberOf Qualifiers
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @summary qualifier
 * @description Preserves the original audio frequency of the video when used with vc_auto.
 * @memberOf Qualifiers.AudioFrequency
 */
declare function ORIGINAL(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ192000(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ176400(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ96000(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ88200(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ48000(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ8000(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ11025(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ16000(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ22050(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ32000(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ37800(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ44056(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ44100(): number;
/**
 * @summary qualifier
 * @memberOf Qualifiers.AudioFrequency
 */
declare function FREQ47250(): number;
declare const AudioFrequency: {
    FREQ8000: typeof FREQ8000;
    FREQ11025: typeof FREQ11025;
    FREQ16000: typeof FREQ16000;
    FREQ22050: typeof FREQ22050;
    FREQ32000: typeof FREQ32000;
    FREQ37800: typeof FREQ37800;
    FREQ44056: typeof FREQ44056;
    FREQ44100: typeof FREQ44100;
    FREQ47250: typeof FREQ47250;
    FREQ48000: typeof FREQ48000;
    FREQ88200: typeof FREQ88200;
    FREQ96000: typeof FREQ96000;
    FREQ176400: typeof FREQ176400;
    FREQ192000: typeof FREQ192000;
    ORIGINAL: typeof ORIGINAL;
};
export { AudioFrequency, FREQ8000, FREQ11025, FREQ16000, FREQ22050, FREQ32000, FREQ37800, FREQ44056, FREQ44100, FREQ47250, FREQ48000, FREQ88200, FREQ96000, FREQ176400, FREQ192000, ORIGINAL };
