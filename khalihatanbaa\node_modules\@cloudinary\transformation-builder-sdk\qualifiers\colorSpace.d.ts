/**
 * @description Contains functions to select the color space mode.
 * @namespace ColorSpace
 * @memberOf Qualifiers
 * @see Visit {@link Actions.Delivery.colorSpace|Delivery Color Space} for an example
 */
import { ColorSpaceType } from "../types/types.js";
/**
 * @summary qualifier
 * @memberOf Qualifiers.ColorSpace
 */
declare function srgb(): ColorSpaceType;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ColorSpace
 */
declare function trueColor(): ColorSpaceType;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ColorSpace
 */
declare function tinySrgb(): ColorSpaceType;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ColorSpace
 */
declare function cmyk(): ColorSpaceType;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ColorSpace
 */
declare function noCmyk(): ColorSpaceType;
/**
 * @summary qualifier
 * @memberOf Qualifiers.ColorSpace
 */
declare function keepCmyk(): ColorSpaceType;
declare const ColorSpace: Record<string, () => ColorSpaceType>;
export { ColorSpace, cmyk, keepCmyk, noCmyk, srgb, tinySrgb, trueColor };
