'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { XMarkIcon, StarIcon } from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'

interface RatingModalProps {
  isOpen: boolean
  onClose: () => void
  sellerId: string
  sellerName: string
  onRatingAdded: () => void
}

export function RatingModal({ isOpen, onClose, sellerId, sellerName, onRatingAdded }: RatingModalProps) {
  const { data: session } = useSession()
  const [rating, setRating] = useState(0)
  const [hoverRating, setHoverRating] = useState(0)
  const [comment, setComment] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session?.user?.id) {
      setError('يجب تسجيل الدخول أولاً')
      return
    }

    if (rating === 0) {
      setError('يرجى اختيار تقييم')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      const response = await fetch('/api/ratings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sellerId,
          rating,
          comment: comment.trim() || undefined
        })
      })

      const data = await response.json()

      if (data.success) {
        onRatingAdded()
        onClose()
        // إعادة تعيين النموذج
        setRating(0)
        setComment('')
        setError('')
      } else {
        setError(data.error || 'حدث خطأ في إضافة التقييم')
      }
    } catch (error) {
      console.error('Error submitting rating:', error)
      setError('حدث خطأ في إضافة التقييم')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
      setRating(0)
      setComment('')
      setError('')
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-dark-800">تقييم البائع</h3>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-600 mb-2">كيف كانت تجربتك مع البائع؟</p>
          <p className="font-medium text-dark-800">{sellerName}</p>
        </div>

        <form onSubmit={handleSubmit}>
          {/* نجوم التقييم */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-dark-700 mb-2">
              التقييم *
            </label>
            <div className="flex items-center space-x-1 space-x-reverse">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoverRating(star)}
                  onMouseLeave={() => setHoverRating(0)}
                  className="p-1 transition-colors"
                  disabled={isSubmitting}
                >
                  {star <= (hoverRating || rating) ? (
                    <StarSolidIcon className="h-8 w-8 text-yellow-400" />
                  ) : (
                    <StarIcon className="h-8 w-8 text-gray-300" />
                  )}
                </button>
              ))}
            </div>
            <div className="mt-1 text-sm text-gray-500">
              {rating === 0 && 'اختر تقييمك'}
              {rating === 1 && 'سيء جداً'}
              {rating === 2 && 'سيء'}
              {rating === 3 && 'متوسط'}
              {rating === 4 && 'جيد'}
              {rating === 5 && 'ممتاز'}
            </div>
          </div>

          {/* تعليق */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-dark-700 mb-2">
              تعليق (اختياري)
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="شارك تجربتك مع البائع..."
              maxLength={500}
              disabled={isSubmitting}
            />
            <div className="mt-1 text-xs text-gray-500 text-left">
              {comment.length}/500
            </div>
          </div>

          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-3 py-2 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="flex space-x-3 space-x-reverse">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={rating === 0 || isSubmitting}
              className="flex-1 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'جاري الإرسال...' : 'إرسال التقييم'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
