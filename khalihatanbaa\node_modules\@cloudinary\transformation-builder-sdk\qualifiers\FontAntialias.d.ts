/**
 * @description Contains functions to select the font antialias setting.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/layers#text_layer_options|Adding text overlays to images}
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_layers#text_layer_options|Adding text overlays to videos}
 * @memberOf Qualifiers
 * @namespace FontAntialias
 * @see To be used with {@link Qualifiers.TextStyle|Text Style}
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.FontAntialias
 */
declare function none(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.FontAntialias
 */
declare function gray(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.FontAntialias
 */
declare function subpixel(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.FontAntialias
 */
declare function fast(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.FontAntialias
 */
declare function good(): string;
/**
 * @summary qualifier
 * @memberOf Qualifiers.FontAntialias
 */
declare function best(): string;
declare const FontAntialias: {
    gray: typeof gray;
    subpixel: typeof subpixel;
    best: typeof best;
    fast: typeof fast;
    none: typeof none;
    good: typeof good;
};
export { FontAntialias, gray, subpixel, best, fast, none, good };
