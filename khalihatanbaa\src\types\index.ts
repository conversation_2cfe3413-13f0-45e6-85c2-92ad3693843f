// أنواع البيانات الأساسية للتطبيق

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: "user" | "admin";
  avatar?: string;
  isActive: boolean;
  freeAdsCount: number;
  freeAdsExpiresAt?: Date;
  paidAdsCount: number;
  paidAdsExpiresAt?: Date;
  ratingAverage: number;
  ratingCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Ad {
  id: string;
  title: string;
  description: string;
  price: number;
  category: string;
  subCategory?: string;
  condition: "جديد" | "مستعمل";
  city: string;
  region?: string;
  addressDetail?: string;
  imageUrls: string[];
  specifications?: Record<string, any>;
  views: number;
  isActive: boolean;
  isFreeAd: boolean;
  isPromoted: boolean;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  user?: User;
}

export interface Message {
  id: string;
  content: string;
  isRead: boolean;
  createdAt: Date;
  fromId: string;
  toId: string;
  adId?: string;
  from?: User;
  to?: User;
  ad?: Ad;
}

export interface Favorite {
  id: string;
  userId: string;
  adId: string;
  createdAt: Date;
  user?: User;
  ad?: Ad;
}

export interface Rating {
  id: string;
  rating: number;
  comment?: string;
  createdAt: Date;
  userId: string;
  sellerId: string;
  giver?: User;
  receiver?: User;
}

export interface AdPackage {
  id: string;
  name: string;
  adsCount: number;
  price: number;
  duration: number;
  isActive: boolean;
  createdAt: Date;
  userId: string;
  user?: User;
}

// أنواع الفئات
export const CATEGORIES = {
  عقارات: {
    name: "عقارات",
    icon: "🏠",
    subCategories: ["شقة", "فيلا", "أرض", "محل تجاري", "مكتب"],
  },
  سيارات: {
    name: "سيارات",
    icon: "🚗",
    subCategories: [
      "تويوتا",
      "نيسان",
      "هيونداي",
      "كيا",
      "مرسيدس",
      "BMW",
      "أخرى",
    ],
  },
  إلكترونيات: {
    name: "إلكترونيات",
    icon: "📱",
    subCategories: ["هاتف ذكي", "لابتوب", "تلفاز", "كاميرا", "أجهزة منزلية"],
  },
  أثاث: {
    name: "أثاث",
    icon: "🪑",
    subCategories: ["غرفة نوم", "غرفة جلوس", "مطبخ", "مكتب", "ديكور"],
  },
  ملابس: {
    name: "ملابس",
    icon: "👕",
    subCategories: ["رجالي", "نسائي", "أطفال", "أحذية", "إكسسوارات"],
  },
  رياضة: {
    name: "رياضة",
    icon: "⚽",
    subCategories: ["كرة قدم", "كرة سلة", "جيم", "دراجات", "أخرى"],
  },
} as const;

export type CategoryKey = keyof typeof CATEGORIES;

// أنواع المدن السورية
export const SYRIAN_CITIES = [
  "دمشق",
  "حلب",
  "حمص",
  "حماة",
  "اللاذقية",
  "طرطوس",
  "درعا",
  "السويداء",
  "القنيطرة",
  "دير الزور",
  "الرقة",
  "الحسكة",
  "إدلب",
  "ريف دمشق",
] as const;

export type SyrianCity = (typeof SYRIAN_CITIES)[number];

// أنواع الاستعلامات
export interface SearchFilters {
  keyword?: string;
  category?: CategoryKey;
  subCategory?: string;
  city?: SyrianCity;
  minPrice?: number;
  maxPrice?: number;
  condition?: "جديد" | "مستعمل";
  sortBy?: "newest" | "oldest" | "price_low" | "price_high";
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
