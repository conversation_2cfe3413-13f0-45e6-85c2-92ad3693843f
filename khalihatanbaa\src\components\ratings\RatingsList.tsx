'use client'

import { useState, useEffect } from 'react'
import { StarIcon, UserIcon } from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'

interface Rating {
  id: string
  rating: number
  comment?: string
  createdAt: string
  giver: {
    id: string
    name: string
    avatar?: string
  }
}

interface RatingSummary {
  total: number
  average: number
  distribution: {
    star: number
    count: number
  }[]
}

interface RatingsListProps {
  sellerId: string
  refreshTrigger?: number
}

export function RatingsList({ sellerId, refreshTrigger }: RatingsListProps) {
  const [ratings, setRatings] = useState<Rating[]>([])
  const [summary, setSummary] = useState<RatingSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  useEffect(() => {
    fetchRatings(1, true)
  }, [sellerId, refreshTrigger])

  const fetchRatings = async (pageNum: number = 1, reset: boolean = false) => {
    try {
      const response = await fetch(`/api/ratings?sellerId=${sellerId}&page=${pageNum}&limit=10`)
      const data = await response.json()

      if (data.success) {
        if (reset) {
          setRatings(data.data.ratings)
        } else {
          setRatings(prev => [...prev, ...data.data.ratings])
        }
        
        setSummary(data.data.summary)
        setHasMore(data.data.pagination.page < data.data.pagination.totalPages)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('Error fetching ratings:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadMore = () => {
    if (!loading && hasMore) {
      setLoading(true)
      fetchRatings(page + 1, false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const sizeClass = size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'
    
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <div key={star}>
            {star <= rating ? (
              <StarSolidIcon className={`${sizeClass} text-yellow-400`} />
            ) : (
              <StarIcon className={`${sizeClass} text-gray-300`} />
            )}
          </div>
        ))}
      </div>
    )
  }

  if (loading && ratings.length === 0) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-20 bg-gray-200 rounded-lg"></div>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-200 rounded-lg"></div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ملخص التقييمات */}
      {summary && summary.total > 0 && (
        <div className="bg-gray-50 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* متوسط التقييم */}
            <div className="text-center">
              <div className="text-3xl font-bold text-dark-800 mb-2">
                {summary.average.toFixed(1)}
              </div>
              <div className="mb-2">
                {renderStars(Math.round(summary.average), 'md')}
              </div>
              <div className="text-sm text-gray-600">
                بناءً على {summary.total} تقييم
              </div>
            </div>

            {/* توزيع التقييمات */}
            <div className="space-y-2">
              {summary.distribution.reverse().map((dist) => (
                <div key={dist.star} className="flex items-center space-x-2 space-x-reverse">
                  <span className="text-sm text-gray-600 w-8">
                    {dist.star} ⭐
                  </span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-400 h-2 rounded-full"
                      style={{
                        width: `${summary.total > 0 ? (dist.count / summary.total) * 100 : 0}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-8">
                    {dist.count}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* قائمة التقييمات */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-dark-800">
          التقييمات ({summary?.total || 0})
        </h3>

        {ratings.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <StarIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>لا توجد تقييمات حتى الآن</p>
            <p className="text-sm mt-1">كن أول من يقيم هذا البائع</p>
          </div>
        ) : (
          <>
            {ratings.map((rating) => (
              <div key={rating.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                    {rating.giver.avatar ? (
                      <img
                        src={rating.giver.avatar}
                        alt={rating.giver.name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <UserIcon className="h-5 w-5 text-primary-600" />
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <p className="font-medium text-dark-800">
                          {rating.giver.name}
                        </p>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          {renderStars(rating.rating)}
                          <span className="text-sm text-gray-600">
                            {formatDate(rating.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {rating.comment && (
                      <p className="text-gray-700 text-sm leading-relaxed">
                        {rating.comment}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {/* زر تحميل المزيد */}
            {hasMore && (
              <div className="text-center">
                <button
                  onClick={loadMore}
                  disabled={loading}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  {loading ? 'جاري التحميل...' : 'تحميل المزيد'}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
