# Database (Prisma Accelerate with PostgreSQL)
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiMDFKV1I4TVdXSkZTRlAxR0VWQ1JXTURQUjAiLCJ0ZW5hbnRfaWQiOiIyMzRmOGY1NDk5Y2Y2NzQ1ZmFlNDYzMjlhMGFkNGQ0YjI1ODY5MDkwMGUwNjNlZWU3MzQzNTEwYjA3Y2Y1ZTUxIiwiaW50ZXJuYWxfc2VjcmV0IjoiY2Q5ODFkYjEtN2EyNS00NjgwLWFlMmMtYzg0NWViOWEyMmFmIn0.ZQqHVCPUS37eLCxlDKdsFggVsx-7LS4pcQUzlvCQqiw"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# Cloudinary
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# App Settings
APP_NAME="خَلّيها تنْباع"
APP_DOMAIN="khalihatanbaa.com"
