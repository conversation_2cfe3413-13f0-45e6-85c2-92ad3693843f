"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { PlaceholderImage } from "@/components/ui/PlaceholderImage";
import {
  PaperAirplaneIcon,
  ChatBubbleLeftRightIcon,
  UserIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

interface Message {
  id: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  fromId: string;
  toId: string;
  adId?: string;
  from: {
    id: string;
    name: string;
    avatar?: string;
  };
  to: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
}

interface Conversation {
  id: string;
  content: string;
  createdAt: string;
  adId?: string;
  otherUser: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
  unreadCount: number;
}

export default function MessagesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<
    string | null
  >(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // تعريف الدوال
  const fetchConversations = async () => {
    try {
      const response = await fetch("/api/messages");
      const data = await response.json();

      if (data.success) {
        setConversations(data.data);
        // إذا لم تكن هناك محادثة محددة وهناك محادثات متاحة، اختر الأولى
        if (!selectedConversation && data.data.length > 0) {
          setSelectedConversation(data.data[0].otherUser.id);
          fetchMessages(data.data[0].otherUser.id, data.data[0].adId);
        }
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (userId: string, adId?: string | null) => {
    try {
      const url = new URL("/api/messages", window.location.origin);
      url.searchParams.set("with", userId);
      if (adId) url.searchParams.set("adId", adId);

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.success) {
        setMessages(data.data);
      }
    } catch (error) {
      console.error("Error fetching messages:", error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // جميع useEffect يجب أن تكون في الأعلى قبل أي return مشروط
  useEffect(() => {
    if (status === "authenticated") {
      fetchConversations();

      // التحقق من وجود محادثة محددة في URL
      const withUser = searchParams.get("with");
      const adId = searchParams.get("ad");

      if (withUser) {
        setSelectedConversation(withUser);
        fetchMessages(withUser, adId);
      }
    }
  }, [searchParams, status]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login");
    }
  }, [status, router]);

  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        جاري التحميل...
      </div>
    );
  }

  if (status === "unauthenticated") {
    return null;
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || sending) return;

    setSending(true);
    try {
      const adId = searchParams.get("ad");

      const response = await fetch("/api/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toId: selectedConversation,
          content: newMessage.trim(),
          ...(adId && { adId }),
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessages((prev) => [...prev, data.data]);
        setNewMessage("");
        fetchConversations(); // تحديث قائمة المحادثات
      } else {
        alert(data.error || "حدث خطأ في إرسال الرسالة");
      }
    } catch (error) {
      console.error("Error sending message:", error);
      alert("حدث خطأ في إرسال الرسالة");
    } finally {
      setSending(false);
    }
  };

  const selectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation.otherUser.id);
    fetchMessages(conversation.otherUser.id, conversation.adId);

    // تحديث URL
    const url = new URL(window.location.href);
    url.searchParams.set("with", conversation.otherUser.id);
    if (conversation.adId) {
      url.searchParams.set("ad", conversation.adId);
    } else {
      url.searchParams.delete("ad");
    }
    window.history.pushState({}, "", url.toString());
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return date.toLocaleTimeString("ar-SY", {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else if (diffDays < 7) {
      return `${diffDays} أيام`;
    } else {
      return date.toLocaleDateString("ar-SY");
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("ar-SY").format(price);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="h-96 bg-gray-200 rounded-lg"></div>
              <div className="lg:col-span-2 h-96 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-2xl font-bold text-dark-800 mb-6">الرسائل</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
          {/* قائمة المحادثات */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <h2 className="font-semibold text-dark-800">المحادثات</h2>
            </div>

            <div className="overflow-y-auto h-full">
              {conversations.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <ChatBubbleLeftRightIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p>لا توجد محادثات حتى الآن</p>
                  <p className="text-sm mt-1">
                    ابدأ محادثة من خلال التواصل مع بائع
                  </p>
                </div>
              ) : (
                conversations.map((conversation) => (
                  <button
                    key={`${conversation.otherUser.id}-${
                      conversation.adId || "general"
                    }`}
                    onClick={() => selectConversation(conversation)}
                    className={`w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-right transition-colors ${
                      selectedConversation === conversation.otherUser.id
                        ? "bg-primary-50"
                        : ""
                    }`}
                  >
                    <div className="flex items-start space-x-3 space-x-reverse">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                        {conversation.otherUser.avatar ? (
                          <img
                            src={conversation.otherUser.avatar}
                            alt={conversation.otherUser.name}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <UserIcon className="h-5 w-5 text-primary-600" />
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className="font-medium text-dark-800 truncate">
                            {conversation.otherUser.name}
                          </p>
                          {conversation.unreadCount > 0 && (
                            <span className="bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                              {conversation.unreadCount}
                            </span>
                          )}
                        </div>

                        {conversation.ad && (
                          <p className="text-sm text-gray-600 truncate mb-1">
                            {conversation.ad.title}
                          </p>
                        )}

                        <p className="text-sm text-gray-500 truncate">
                          {conversation.content}
                        </p>

                        <div className="flex items-center mt-1">
                          <ClockIcon className="h-3 w-3 text-gray-400 ml-1" />
                          <span className="text-xs text-gray-400">
                            {formatTime(conversation.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                ))
              )}
            </div>
          </div>

          {/* منطقة المحادثة */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col">
            {selectedConversation ? (
              <>
                {/* رأس المحادثة */}
                <div className="p-4 border-b border-gray-200">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <UserIcon className="h-5 w-5 text-primary-600" />
                    </div>
                    <div>
                      <p className="font-medium text-dark-800">
                        {
                          conversations.find(
                            (c) => c.otherUser.id === selectedConversation
                          )?.otherUser.name
                        }
                      </p>
                      {searchParams.get("ad") && (
                        <p className="text-sm text-gray-500">
                          حول:{" "}
                          {
                            conversations.find(
                              (c) => c.otherUser.id === selectedConversation
                            )?.ad?.title
                          }
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* الرسائل */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.fromId === session?.user?.id
                          ? "justify-end"
                          : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.fromId === session?.user?.id
                            ? "bg-primary-500 text-white"
                            : "bg-gray-100 text-dark-800"
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                        <p
                          className={`text-xs mt-1 ${
                            message.fromId === session?.user?.id
                              ? "text-primary-100"
                              : "text-gray-500"
                          }`}
                        >
                          {formatTime(message.createdAt)}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>

                {/* إرسال رسالة */}
                <div className="p-4 border-t border-gray-200">
                  <div className="flex space-x-2 space-x-reverse">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === "Enter" && sendMessage()}
                      placeholder="اكتب رسالتك..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      disabled={sending}
                    />
                    <button
                      onClick={sendMessage}
                      disabled={!newMessage.trim() || sending}
                      className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <PaperAirplaneIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <ChatBubbleLeftRightIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">
                    اختر محادثة لبدء المراسلة
                  </p>
                  <p className="text-sm mt-1">
                    اختر محادثة من القائمة الجانبية
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
