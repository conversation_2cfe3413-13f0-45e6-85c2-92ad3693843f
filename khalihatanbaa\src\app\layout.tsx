import type { Metadata } from "next";
import "./globals.css";
import { AuthProvider } from "@/components/providers/AuthProvider";

export const metadata: Metadata = {
  title: "خَلّيها تنْباع - منصة البيع والشراء في سوريا",
  description:
    "منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا. اعثر على أفضل العروض للعقارات، السيارات، الإلكترونيات وأكثر.",
  keywords: "بيع، شراء، سوريا، إعلانات، عقارات، سيارات، إلكترونيات",
  authors: [{ name: "خَلّيها تنْباع" }],
  openGraph: {
    title: "خَلّيها تنْباع - منصة البيع والشراء في سوريا",
    description: "منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا",
    url: "https://khalihatanbaa.com",
    siteName: "خَلّيها تنْباع",
    locale: "ar_SY",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "خَلّيها تنْباع - منصة البيع والشراء في سوريا",
    description: "منصة إلكترونية لبيع وشراء المنتجات المستعملة في سوريا",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning={true}>
      <body className="font-arabic antialiased" suppressHydrationWarning={true}>
        <div suppressHydrationWarning={true}>
          <AuthProvider>{children}</AuthProvider>
        </div>
      </body>
    </html>
  );
}
