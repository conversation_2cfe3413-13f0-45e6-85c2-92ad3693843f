"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export default function TestMessagesPage() {
  const { data: session, status } = useSession();
  const [messages, setMessages] = useState([]);
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (status === "authenticated") {
      fetchData();
    }
  }, [status]);

  const fetchData = async () => {
    try {
      // جلب المحادثات
      const conversationsResponse = await fetch("/api/messages");
      const conversationsData = await conversationsResponse.json();
      
      console.log("Conversations API Response:", conversationsData);
      
      if (conversationsData.success) {
        setConversations(conversationsData.data);
      } else {
        setError("خطأ في جلب المحادثات: " + conversationsData.error);
      }

      // جلب الرسائل مع مستخدم محدد
      if (conversationsData.data && conversationsData.data.length > 0) {
        const firstConversation = conversationsData.data[0];
        const messagesResponse = await fetch(`/api/messages?with=${firstConversation.otherUser.id}`);
        const messagesData = await messagesResponse.json();
        
        console.log("Messages API Response:", messagesData);
        
        if (messagesData.success) {
          setMessages(messagesData.data);
        }
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      setError("خطأ في الاتصال: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading") {
    return <div className="p-8">جاري تحميل الجلسة...</div>;
  }

  if (status === "unauthenticated") {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">اختبار الرسائل</h1>
        <p>يجب تسجيل الدخول أولاً</p>
        <a href="/auth/login" className="text-blue-500 underline">
          تسجيل الدخول
        </a>
      </div>
    );
  }

  if (loading) {
    return <div className="p-8">جاري التحميل...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">اختبار الرسائل</h1>
      
      <div className="mb-4 p-4 bg-blue-50 rounded">
        <h3 className="font-semibold">معلومات الجلسة:</h3>
        <p>المستخدم: {session?.user?.name}</p>
        <p>البريد الإلكتروني: {session?.user?.email}</p>
        <p>المعرف: {session?.user?.id}</p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded text-red-700">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* المحادثات */}
        <div>
          <h2 className="text-xl font-semibold mb-4">المحادثات ({conversations.length})</h2>
          <div className="space-y-2">
            {conversations.length === 0 ? (
              <p className="text-gray-500">لا توجد محادثات</p>
            ) : (
              conversations.map((conversation, index) => (
                <div key={index} className="p-3 border rounded bg-gray-50">
                  <p className="font-medium">{conversation.otherUser?.name || "مستخدم غير معروف"}</p>
                  <p className="text-sm text-gray-600">{conversation.content}</p>
                  {conversation.ad && (
                    <p className="text-xs text-blue-600">الإعلان: {conversation.ad.title}</p>
                  )}
                  <p className="text-xs text-gray-400">
                    {new Date(conversation.createdAt).toLocaleString("ar-SY")}
                  </p>
                </div>
              ))
            )}
          </div>
        </div>

        {/* الرسائل */}
        <div>
          <h2 className="text-xl font-semibold mb-4">الرسائل ({messages.length})</h2>
          <div className="space-y-2">
            {messages.length === 0 ? (
              <p className="text-gray-500">لا توجد رسائل</p>
            ) : (
              messages.map((message, index) => (
                <div key={index} className="p-3 border rounded bg-gray-50">
                  <p className="font-medium">
                    من: {message.from?.name || "مستخدم غير معروف"}
                  </p>
                  <p className="font-medium">
                    إلى: {message.to?.name || "مستخدم غير معروف"}
                  </p>
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs text-gray-400">
                    {new Date(message.createdAt).toLocaleString("ar-SY")}
                  </p>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      <div className="mt-8">
        <button
          onClick={fetchData}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          إعادة تحميل البيانات
        </button>
      </div>

      {/* عرض البيانات الخام */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold mb-2">البيانات الخام:</h3>
        <details className="mb-4">
          <summary className="cursor-pointer font-medium">المحادثات (JSON)</summary>
          <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto">
            {JSON.stringify(conversations, null, 2)}
          </pre>
        </details>
        <details>
          <summary className="cursor-pointer font-medium">الرسائل (JSON)</summary>
          <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto">
            {JSON.stringify(messages, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
}
