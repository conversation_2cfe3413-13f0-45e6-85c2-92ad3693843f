"use client";

import { useState } from "react";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";

export function Header() {
  const { data: session } = useSession();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* الشعار */}
          <Link
            href="/"
            className="flex items-center space-x-2 space-x-reverse"
          >
            <div className="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">خ</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-dark-800">
                خَلّيها تنْباع
              </h1>
              <p className="text-xs text-dark-500">منصة البيع والشراء</p>
            </div>
          </Link>

          {/* شريط البحث */}
          <div className="flex-1 max-w-lg mx-4">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                placeholder="ابحث عن أي شيء..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </form>
          </div>

          {/* أزرار التنقل */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {session ? (
              <>
                {/* نشر إعلان */}
                <Link
                  href="/ads/new"
                  className="bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse"
                >
                  <PlusIcon className="h-5 w-5" />
                  <span className="hidden sm:inline">نشر إعلان</span>
                </Link>

                {/* الرسائل */}
                <Link
                  href="/messages"
                  className="p-2 text-dark-600 hover:text-primary-500 transition-colors relative"
                >
                  <ChatBubbleLeftRightIcon className="h-6 w-6" />
                  {/* نقطة الإشعارات */}
                  <span className="absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full"></span>
                </Link>

                {/* المفضلة */}
                <Link
                  href="/favorites"
                  className="p-2 text-dark-600 hover:text-primary-500 transition-colors"
                >
                  <HeartIcon className="h-6 w-6" />
                </Link>

                {/* لوحة التحكم */}
                <Link
                  href="/dashboard"
                  className="p-2 text-dark-600 hover:text-primary-500 transition-colors"
                >
                  <ChartBarIcon className="h-6 w-6" />
                </Link>

                {/* قائمة المستخدم */}
                <div className="relative">
                  <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    className="flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors"
                  >
                    <UserIcon className="h-6 w-6" />
                    <span className="hidden sm:inline">
                      {session.user.name}
                    </span>
                  </button>

                  {isMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
                      <Link
                        href="/profile"
                        className="block px-4 py-2 text-dark-700 hover:bg-gray-50"
                      >
                        الملف الشخصي
                      </Link>
                      <Link
                        href="/profile/ads"
                        className="block px-4 py-2 text-dark-700 hover:bg-gray-50"
                      >
                        إعلاناتي
                      </Link>
                      <Link
                        href="/profile/settings"
                        className="block px-4 py-2 text-dark-700 hover:bg-gray-50"
                      >
                        الإعدادات
                      </Link>
                      <hr className="my-2" />
                      <button
                        onClick={() => signOut()}
                        className="block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50"
                      >
                        تسجيل الخروج
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <>
                <Link
                  href="/auth/login"
                  className="text-dark-600 hover:text-primary-500 transition-colors"
                >
                  تسجيل الدخول
                </Link>
                <Link
                  href="/auth/register"
                  className="bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors"
                >
                  إنشاء حساب
                </Link>
              </>
            )}

            {/* قائمة الموبايل */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="sm:hidden p-2 text-dark-600"
            >
              {isMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* قائمة الموبايل */}
        {isMenuOpen && (
          <div className="sm:hidden border-t border-gray-200 py-4">
            <div className="space-y-2">
              <Link
                href="/ads"
                className="block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded"
              >
                جميع الإعلانات
              </Link>
              <Link
                href="/categories"
                className="block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded"
              >
                جميع الفئات
              </Link>
              {session && (
                <>
                  <Link
                    href="/ads/new"
                    className="block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded"
                  >
                    نشر إعلان جديد
                  </Link>
                  <Link
                    href="/messages"
                    className="block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded"
                  >
                    الرسائل
                  </Link>
                  <Link
                    href="/favorites"
                    className="block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded"
                  >
                    المفضلة
                  </Link>
                  <Link
                    href="/dashboard"
                    className="block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded"
                  >
                    لوحة التحكم
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
