{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/ads\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الإعلانات\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-dark-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* معلومات الموقع */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4\">\n              <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">خ</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">خَلّيها تنْباع</h3>\n                <p className=\"text-sm text-gray-400\">منصة البيع والشراء</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. \n              نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">فيسبوك</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">تلغرام</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">واتساب</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* روابط سريعة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  من نحن\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  كيف يعمل الموقع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety-tips\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  نصائح الأمان\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  باقات الإعلانات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  اتصل بنا\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* الدعم والمساعدة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">الدعم والمساعدة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  مركز المساعدة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  شروط الاستخدام\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الإبلاغ عن مشكلة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* معلومات الاتصال */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center md:text-right\">\n              <h5 className=\"font-semibold mb-2\">البريد الإلكتروني</h5>\n              <p className=\"text-gray-300\"><EMAIL></p>\n            </div>\n            <div className=\"text-center\">\n              <h5 className=\"font-semibold mb-2\">رقم الهاتف</h5>\n              <p className=\"text-gray-300\" dir=\"ltr\">+963 11 123 4567</p>\n            </div>\n            <div className=\"text-center md:text-left\">\n              <h5 className=\"font-semibold mb-2\">العنوان</h5>\n              <p className=\"text-gray-300\">دمشق، سوريا</p>\n            </div>\n          </div>\n        </div>\n\n        {/* حقوق النشر */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\n          </p>\n          <p className=\"text-gray-500 text-sm mt-2\">\n            تم التطوير بـ ❤️ في سوريا\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAmD;;;;;;;;;;;sDAI1F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;wCAAgB,KAAI;kDAAM;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/types/index.ts"], "sourcesContent": ["// أنواع البيانات الأساسية للتطبيق\n\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  role: \"user\" | \"admin\";\n  avatar?: string;\n  isActive: boolean;\n  freeAdsCount: number;\n  freeAdsExpiresAt?: Date;\n  paidAdsCount: number;\n  paidAdsExpiresAt?: Date;\n  ratingAverage: number;\n  ratingCount: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Ad {\n  id: string;\n  title: string;\n  description: string;\n  price: number;\n  category: string;\n  subCategory?: string;\n  condition: \"جديد\" | \"مستعمل\";\n  city: string;\n  region?: string;\n  addressDetail?: string;\n  imageUrls: string[];\n  specifications?: Record<string, any>;\n  views: number;\n  isActive: boolean;\n  isFreeAd: boolean;\n  isPromoted: boolean;\n  expiresAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  userId: string;\n  user?: User;\n}\n\nexport interface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: Date;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from?: User;\n  to?: User;\n  ad?: Ad;\n}\n\nexport interface Favorite {\n  id: string;\n  userId: string;\n  adId: string;\n  createdAt: Date;\n  user?: User;\n  ad?: Ad;\n}\n\nexport interface Rating {\n  id: string;\n  rating: number;\n  comment?: string;\n  createdAt: Date;\n  userId: string;\n  sellerId: string;\n  giver?: User;\n  receiver?: User;\n}\n\nexport interface AdPackage {\n  id: string;\n  name: string;\n  adsCount: number;\n  price: number;\n  duration: number;\n  isActive: boolean;\n  createdAt: Date;\n  userId: string;\n  user?: User;\n}\n\n// أنواع الفئات\nexport const CATEGORIES = {\n  عقارات: {\n    name: \"عقارات\",\n    icon: \"🏠\",\n    subCategories: [\"شقة\", \"فيلا\", \"أرض\", \"محل تجاري\", \"مكتب\"],\n  },\n  سيارات: {\n    name: \"سيارات\",\n    icon: \"🚗\",\n    subCategories: [\n      \"تويوتا\",\n      \"نيسان\",\n      \"هيونداي\",\n      \"كيا\",\n      \"مرسيدس\",\n      \"BMW\",\n      \"أخرى\",\n    ],\n  },\n  إلكترونيات: {\n    name: \"إلكترونيات\",\n    icon: \"📱\",\n    subCategories: [\"هاتف ذكي\", \"لابتوب\", \"تلفاز\", \"كاميرا\", \"أجهزة منزلية\"],\n  },\n  أثاث: {\n    name: \"أثاث\",\n    icon: \"🪑\",\n    subCategories: [\"غرفة نوم\", \"غرفة جلوس\", \"مطبخ\", \"مكتب\", \"ديكور\"],\n  },\n  ملابس: {\n    name: \"ملابس\",\n    icon: \"👕\",\n    subCategories: [\"رجالي\", \"نسائي\", \"أطفال\", \"أحذية\", \"إكسسوارات\"],\n  },\n  رياضة: {\n    name: \"رياضة\",\n    icon: \"⚽\",\n    subCategories: [\"كرة قدم\", \"كرة سلة\", \"جيم\", \"دراجات\", \"أخرى\"],\n  },\n} as const;\n\nexport type CategoryKey = keyof typeof CATEGORIES;\n\n// أنواع المدن السورية\nexport const SYRIAN_CITIES = [\n  \"دمشق\",\n  \"حلب\",\n  \"حمص\",\n  \"حماة\",\n  \"اللاذقية\",\n  \"طرطوس\",\n  \"درعا\",\n  \"السويداء\",\n  \"القنيطرة\",\n  \"دير الزور\",\n  \"الرقة\",\n  \"الحسكة\",\n  \"إدلب\",\n  \"ريف دمشق\",\n] as const;\n\nexport type SyrianCity = (typeof SYRIAN_CITIES)[number];\n\n// أنواع الاستعلامات\nexport interface SearchFilters {\n  keyword?: string;\n  category?: CategoryKey;\n  subCategory?: string;\n  city?: SyrianCity;\n  minPrice?: number;\n  maxPrice?: number;\n  condition?: \"جديد\" | \"مستعمل\";\n  sortBy?: \"newest\" | \"oldest\" | \"price_low\" | \"price_high\";\n}\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> extends ApiResponse<T[]> {\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AA0F3B,MAAM,aAAa;IACxB,QAAQ;QACN,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAO;YAAQ;YAAO;YAAa;SAAO;IAC5D;IACA,QAAQ;QACN,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,YAAY;QACV,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAU;YAAS;YAAU;SAAe;IAC1E;IACA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAa;YAAQ;YAAQ;SAAQ;IACnE;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAS;YAAS;YAAS;YAAS;SAAY;IAClE;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAW;YAAW;YAAO;YAAU;SAAO;IAChE;AACF;AAKO,MAAM,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/ads/new/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Header } from '@/components/layout/Header'\nimport { Footer } from '@/components/layout/Footer'\nimport { CATEGORIES, SYRIAN_CITIES } from '@/types'\nimport { ChevronRightIcon, PhotoIcon } from '@heroicons/react/24/outline'\n\nexport default function NewAdPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  \n  const [currentStep, setCurrentStep] = useState(1)\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    price: '',\n    category: '',\n    subCategory: '',\n    condition: 'مستعمل',\n    city: '',\n    region: '',\n    addressDetail: '',\n    images: [] as File[],\n    specifications: {}\n  })\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول\n  if (status === 'loading') {\n    return <div className=\"min-h-screen flex items-center justify-center\">جاري التحميل...</div>\n  }\n\n  if (status === 'unauthenticated') {\n    router.push('/auth/login')\n    return null\n  }\n\n  const steps = [\n    { id: 1, name: 'اختيار الفئة', description: 'حدد فئة المنتج' },\n    { id: 2, name: 'تفاصيل الإعلان', description: 'أضف العنوان والوصف والسعر' },\n    { id: 3, name: 'الصور', description: 'أضف صور المنتج' },\n    { id: 4, name: 'الموقع', description: 'حدد موقع المنتج' },\n    { id: 5, name: 'المراجعة والنشر', description: 'راجع الإعلان واتشره' }\n  ]\n\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1)\n    }\n  }\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(e.target.files || [])\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, ...files].slice(0, 10) // حد أقصى 10 صور\n    }))\n  }\n\n  const removeImage = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter((_, i) => i !== index)\n    }))\n  }\n\n  const handleSubmit = async () => {\n    setIsLoading(true)\n    setError('')\n\n    try {\n      // هنا سيتم رفع الصور إلى Cloudinary وإنشاء الإعلان\n      // للآن سنعرض رسالة نجاح\n      alert('تم نشر الإعلان بنجاح!')\n      router.push('/')\n    } catch (error) {\n      setError('حدث خطأ أثناء نشر الإعلان')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-dark-800\">اختر فئة المنتج</h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n              {Object.entries(CATEGORIES).map(([key, category]) => (\n                <button\n                  key={key}\n                  onClick={() => setFormData(prev => ({ ...prev, category: key, subCategory: '' }))}\n                  className={`p-4 border-2 rounded-lg text-center transition-colors ${\n                    formData.category === key\n                      ? 'border-primary-500 bg-primary-50 text-primary-700'\n                      : 'border-gray-200 hover:border-primary-300'\n                  }`}\n                >\n                  <div className=\"text-2xl mb-2\">{category.icon}</div>\n                  <div className=\"font-medium\">{category.name}</div>\n                </button>\n              ))}\n            </div>\n\n            {formData.category && (\n              <div className=\"mt-6\">\n                <h4 className=\"text-md font-medium text-dark-700 mb-3\">اختر الفئة الفرعية</h4>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n                  {CATEGORIES[formData.category as keyof typeof CATEGORIES].subCategories.map((subCat) => (\n                    <button\n                      key={subCat}\n                      onClick={() => setFormData(prev => ({ ...prev, subCategory: subCat }))}\n                      className={`p-3 border rounded-lg text-sm transition-colors ${\n                        formData.subCategory === subCat\n                          ? 'border-primary-500 bg-primary-50 text-primary-700'\n                          : 'border-gray-200 hover:border-primary-300'\n                      }`}\n                    >\n                      {subCat}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-dark-800\">تفاصيل الإعلان</h3>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                عنوان الإعلان *\n              </label>\n              <input\n                type=\"text\"\n                name=\"title\"\n                value={formData.title}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                placeholder=\"مثال: تويوتا كامري 2018 فل كامل\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                الوصف *\n              </label>\n              <textarea\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                placeholder=\"اكتب وصفاً مفصلاً عن المنتج...\"\n                required\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                  السعر (ل.س) *\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"price\"\n                  value={formData.price}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  placeholder=\"0\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                  حالة المنتج *\n                </label>\n                <select\n                  name=\"condition\"\n                  value={formData.condition}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                >\n                  <option value=\"جديد\">جديد</option>\n                  <option value=\"مستعمل\">مستعمل</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        )\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-dark-800\">صور المنتج</h3>\n            \n            <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n              <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <div className=\"mt-4\">\n                <label htmlFor=\"images\" className=\"cursor-pointer\">\n                  <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                    اضغط لرفع الصور أو اسحبها هنا\n                  </span>\n                  <input\n                    id=\"images\"\n                    name=\"images\"\n                    type=\"file\"\n                    multiple\n                    accept=\"image/*\"\n                    onChange={handleImageUpload}\n                    className=\"sr-only\"\n                  />\n                </label>\n                <p className=\"mt-1 text-xs text-gray-500\">\n                  PNG, JPG, GIF حتى 10 صور\n                </p>\n              </div>\n            </div>\n\n            {formData.images.length > 0 && (\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                {formData.images.map((image, index) => (\n                  <div key={index} className=\"relative\">\n                    <img\n                      src={URL.createObjectURL(image)}\n                      alt={`صورة ${index + 1}`}\n                      className=\"w-full h-24 object-cover rounded-lg\"\n                    />\n                    <button\n                      onClick={() => removeImage(index)}\n                      className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs\"\n                    >\n                      ×\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-dark-800\">موقع المنتج</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                  المحافظة *\n                </label>\n                <select\n                  name=\"city\"\n                  value={formData.city}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  required\n                >\n                  <option value=\"\">اختر المحافظة</option>\n                  {SYRIAN_CITIES.map((city) => (\n                    <option key={city} value={city}>\n                      {city}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                  المنطقة\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"region\"\n                  value={formData.region}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  placeholder=\"مثال: المزة، الشعلان، إلخ\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                تفاصيل العنوان (اختياري)\n              </label>\n              <textarea\n                name=\"addressDetail\"\n                value={formData.addressDetail}\n                onChange={handleInputChange}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                placeholder=\"أضف تفاصيل إضافية عن الموقع...\"\n              />\n            </div>\n          </div>\n        )\n\n      case 5:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-dark-800\">مراجعة الإعلان</h3>\n            \n            <div className=\"bg-gray-50 rounded-lg p-6 space-y-4\">\n              <div>\n                <span className=\"font-medium text-dark-700\">العنوان: </span>\n                <span className=\"text-dark-600\">{formData.title}</span>\n              </div>\n              <div>\n                <span className=\"font-medium text-dark-700\">الفئة: </span>\n                <span className=\"text-dark-600\">{formData.category} - {formData.subCategory}</span>\n              </div>\n              <div>\n                <span className=\"font-medium text-dark-700\">السعر: </span>\n                <span className=\"text-primary-600 font-bold\">{formData.price} ل.س</span>\n              </div>\n              <div>\n                <span className=\"font-medium text-dark-700\">الحالة: </span>\n                <span className=\"text-dark-600\">{formData.condition}</span>\n              </div>\n              <div>\n                <span className=\"font-medium text-dark-700\">الموقع: </span>\n                <span className=\"text-dark-600\">{formData.city} - {formData.region}</span>\n              </div>\n              <div>\n                <span className=\"font-medium text-dark-700\">عدد الصور: </span>\n                <span className=\"text-dark-600\">{formData.images.length}</span>\n              </div>\n            </div>\n\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <p className=\"text-sm text-yellow-800\">\n                <strong>ملاحظة:</strong> بعد نشر الإعلان، سيكون متاحاً للجميع ويمكن للمهتمين التواصل معك عبر نظام الرسائل الداخلي.\n              </p>\n            </div>\n          </div>\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      \n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* مؤشر التقدم */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            {steps.map((step, index) => (\n              <div key={step.id} className=\"flex items-center\">\n                <div\n                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                    currentStep >= step.id\n                      ? 'bg-primary-500 text-white'\n                      : 'bg-gray-200 text-gray-600'\n                  }`}\n                >\n                  {step.id}\n                </div>\n                <div className=\"mr-3 hidden sm:block\">\n                  <div className={`text-sm font-medium ${\n                    currentStep >= step.id ? 'text-primary-600' : 'text-gray-500'\n                  }`}>\n                    {step.name}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">{step.description}</div>\n                </div>\n                {index < steps.length - 1 && (\n                  <ChevronRightIcon className=\"w-5 h-5 text-gray-400 mx-4\" />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* محتوى الخطوة */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          {renderStepContent()}\n        </div>\n\n        {/* أزرار التنقل */}\n        <div className=\"flex justify-between\">\n          <button\n            onClick={handlePrevious}\n            disabled={currentStep === 1}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            السابق\n          </button>\n          \n          {currentStep < steps.length ? (\n            <button\n              onClick={handleNext}\n              disabled={\n                (currentStep === 1 && (!formData.category || !formData.subCategory)) ||\n                (currentStep === 2 && (!formData.title || !formData.description || !formData.price)) ||\n                (currentStep === 4 && !formData.city)\n              }\n              className=\"px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              التالي\n            </button>\n          ) : (\n            <button\n              onClick={handleSubmit}\n              disabled={isLoading}\n              className=\"px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'جاري النشر...' : 'نشر الإعلان'}\n            </button>\n          )}\n        </div>\n\n        {error && (\n          <div className=\"mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n            {error}\n          </div>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,eAAe;QACf,QAAQ,EAAE;QACV,gBAAgB,CAAC;IACnB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,4CAA4C;IAC5C,IAAI,WAAW,WAAW;QACxB,qBAAO,8OAAC;YAAI,WAAU;sBAAgD;;;;;;IACxE;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO,IAAI,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,MAAM;YAAgB,aAAa;QAAiB;QAC7D;YAAE,IAAI;YAAG,MAAM;YAAkB,aAAa;QAA4B;QAC1E;YAAE,IAAI;YAAG,MAAM;YAAS,aAAa;QAAiB;QACtD;YAAE,IAAI;YAAG,MAAM;YAAU,aAAa;QAAkB;QACxD;YAAE,IAAI;YAAG,MAAM;YAAmB,aAAa;QAAsB;KACtE;IAED,MAAM,aAAa;QACjB,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAC7C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;uBAAI,KAAK,MAAM;uBAAK;iBAAM,CAAC,KAAK,CAAC,GAAG,IAAI,iBAAiB;YACnE,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,mDAAmD;YACnD,wBAAwB;YACxB,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,qHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBAC9C,8OAAC;oCAEC,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU;gDAAK,aAAa;4CAAG,CAAC;oCAC/E,WAAW,CAAC,sDAAsD,EAChE,SAAS,QAAQ,KAAK,MAClB,sDACA,4CACJ;;sDAEF,8OAAC;4CAAI,WAAU;sDAAiB,SAAS,IAAI;;;;;;sDAC7C,8OAAC;4CAAI,WAAU;sDAAe,SAAS,IAAI;;;;;;;mCATtC;;;;;;;;;;wBAcV,SAAS,QAAQ,kBAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CACZ,qHAAA,CAAA,aAAU,CAAC,SAAS,QAAQ,CAA4B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,uBAC3E,8OAAC;4CAEC,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa;oDAAO,CAAC;4CACpE,WAAW,CAAC,gDAAgD,EAC1D,SAAS,WAAW,KAAK,SACrB,sDACA,4CACJ;sDAED;2CARI;;;;;;;;;;;;;;;;;;;;;;YAiBrB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOnC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAS,WAAU;;8DAChC,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;8DAG/D,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAGd,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;wBAM7C,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,8OAAC;4BAAI,WAAU;sCACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CACC,KAAK,IAAI,eAAe,CAAC;4CACzB,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;4CACxB,WAAU;;;;;;sDAEZ,8OAAC;4CACC,SAAS,IAAM,YAAY;4CAC3B,WAAU;sDACX;;;;;;;mCATO;;;;;;;;;;;;;;;;YAmBtB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAU;4CACV,QAAQ;;8DAER,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,qHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;wDAAkB,OAAO;kEACvB;uDADU;;;;;;;;;;;;;;;;;8CAOnB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAKlB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,aAAa;oCAC7B,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;;YAMtB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;sDAAiB,SAAS,KAAK;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;;gDAAiB,SAAS,QAAQ;gDAAC;gDAAI,SAAS,WAAW;;;;;;;;;;;;;8CAE7E,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;;gDAA8B,SAAS,KAAK;gDAAC;;;;;;;;;;;;;8CAE/D,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;sDAAiB,SAAS,SAAS;;;;;;;;;;;;8CAErD,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;;gDAAiB,SAAS,IAAI;gDAAC;gDAAI,SAAS,MAAM;;;;;;;;;;;;;8CAEpE,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;sDAAiB,SAAS,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;sCAI3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;oCAAgB;;;;;;;;;;;;;;;;;;YAMlC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CACC,WAAW,CAAC,0EAA0E,EACpF,eAAe,KAAK,EAAE,GAClB,8BACA,6BACJ;sDAED,KAAK,EAAE;;;;;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,oBAAoB,EACnC,eAAe,KAAK,EAAE,GAAG,qBAAqB,iBAC9C;8DACC,KAAK,IAAI;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DAAyB,KAAK,WAAW;;;;;;;;;;;;wCAEzD,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC,+NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;mCAnBtB,KAAK,EAAE;;;;;;;;;;;;;;;kCA2BvB,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;4BAIA,cAAc,MAAM,MAAM,iBACzB,8OAAC;gCACC,SAAS;gCACT,UACE,AAAC,gBAAgB,KAAK,CAAC,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,WAAW,KACjE,gBAAgB,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,KAAK,KACjF,gBAAgB,KAAK,CAAC,SAAS,IAAI;gCAEtC,WAAU;0CACX;;;;;qDAID,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,YAAY,kBAAkB;;;;;;;;;;;;oBAKpC,uBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAKP,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}