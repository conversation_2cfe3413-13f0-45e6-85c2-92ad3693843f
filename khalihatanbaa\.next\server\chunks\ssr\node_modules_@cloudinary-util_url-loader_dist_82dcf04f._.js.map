{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/node_modules/%40cloudinary-util/url-loader/dist/chunk-L3YIXMGG.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/constants/qualifiers.ts\nimport { convertColorHexToRgb, testColorIsHex } from \"@cloudinary-util/util\";\nimport { z as z2 } from \"zod\";\n\n// src/constants/parameters.ts\nvar parameters_exports = {};\n__export(parameters_exports, {\n  angle: () => angle,\n  aspectRatio: () => aspectRatio,\n  aspectRatioModesEnum: () => aspectRatioModesEnum,\n  crop: () => crop,\n  cropModesEnum: () => cropModesEnum,\n  extractMode: () => extractMode,\n  extractModesEnum: () => extractModesEnum,\n  flags: () => flags,\n  flagsEnum: () => flagsEnum,\n  format: () => format,\n  gravity: () => gravity,\n  height: () => height,\n  multiple: () => multiple,\n  prompt: () => prompt,\n  width: () => width,\n  x: () => x,\n  y: () => y,\n  zoom: () => zoom\n});\nimport { z } from \"zod\";\nvar cropModesEnum = z.enum([\n  \"auto\",\n  \"crop\",\n  \"fill\",\n  \"fill_pad\",\n  \"fit\",\n  \"imagga_crop\",\n  \"imagga_scale\",\n  \"lfill\",\n  \"limit\",\n  \"lpad\",\n  \"mfit\",\n  \"mpad\",\n  \"pad\",\n  \"scale\",\n  \"thumb\"\n]);\nvar extractModesEnum = z.enum([\n  \"content\",\n  \"mask\"\n]);\nvar flagsEnum = z.enum([\n  \"animated\",\n  \"any_format\",\n  \"apng\",\n  \"attachment\",\n  \"awebp\",\n  \"clip\",\n  \"clip_evenodd\",\n  \"cutter\",\n  \"force_icc\",\n  \"force_strip\",\n  \"getinfo\",\n  \"group4\",\n  \"hlsv3\",\n  \"ignore_aspect_ratio\",\n  \"ignore_mask_channels\",\n  \"immutable_cache\",\n  \"keep_attribution\",\n  \"keep_dar\",\n  \"keep_iptc\",\n  \"layer_apply\",\n  \"lossy\",\n  \"mono\",\n  \"no_overflow\",\n  \"no_stream\",\n  \"png8_fl_png24_fl_png32\",\n  \"preserve_transparency\",\n  \"progressive\",\n  \"rasterize\",\n  \"region_relative\",\n  \"relative\",\n  \"replace_image\",\n  \"sanitize\",\n  \"splice\",\n  \"streaming_attachment\",\n  \"strip_profile\",\n  \"text_disallow_overflow\",\n  \"text_no_trim\",\n  \"tiff8_lzw\",\n  \"tiled\",\n  \"truncate_ts\",\n  \"waveform\"\n]);\nvar angle = {\n  qualifier: \"a\",\n  schema: z.union([z.string(), z.number()]).describe(\n    JSON.stringify({\n      text: \"Rotates or flips an asset by the specified number of degrees or automatically according to its orientation or available metadata.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#a_angle\"\n    })\n  )\n};\nvar aspectRatioModesEnum = z.enum([\n  \"vflip\",\n  \"hflip\",\n  \"ignore\",\n  \"auto_right\",\n  \"auto_left\"\n]);\nvar aspectRatioSchema = z.union([\n  z.number(),\n  aspectRatioModesEnum,\n  z.intersection(z.string(), z.object({}))\n  // Quirk to allow enum + string\n]);\nvar aspectRatio = {\n  qualifier: \"ar\",\n  schema: aspectRatioSchema.describe(\n    JSON.stringify({\n      text: \"Crops or resizes the asset to a new aspect ratio.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#ar_aspect_ratio\"\n    })\n  )\n};\nvar cropSchema = cropModesEnum;\nvar crop = {\n  qualifier: \"c\",\n  schema: cropSchema.describe(\n    JSON.stringify({\n      text: \"Mode to use when cropping an asset.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#c_crop_resize\"\n    })\n  )\n};\nvar extractModeSchema = extractModesEnum;\nvar extractMode = {\n  schema: extractModeSchema.default(\"content\").describe(\n    JSON.stringify({\n      text: \"Whether to keep the content of the extracted area, or to replace it with a mask.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_extract\"\n    })\n  )\n};\nvar flags = {\n  qualifier: \"fl\",\n  schema: z.union([flagsEnum, z.array(flagsEnum)]).describe(\n    JSON.stringify({\n      text: \"Alters the regular behavior of another transformation or the overall delivery behavior.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#fl_flag\"\n    })\n  )\n};\nvar format = {\n  qualifier: \"f\",\n  // @TODO: enum\n  schema: z.string().describe(\n    JSON.stringify({\n      text: \"Converts (if necessary) and delivers an asset in the specified format regardless of the file extension used in the delivery URL.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#f_format\"\n    })\n  )\n};\nvar gravitySchema = z.union([\n  z.enum([\n    \"auto\",\n    \"auto_content_aware\",\n    \"center\",\n    \"custom\",\n    \"east\",\n    \"face\",\n    \"face_center\",\n    \"multi_face\",\n    \"north\",\n    \"north_east\",\n    \"north_west\",\n    \"south\",\n    \"south_east\",\n    \"south_west\",\n    \"west\"\n  ]),\n  // Quirk to allow enum + string\n  z.intersection(z.string(), z.object({}))\n]);\nvar gravity = {\n  qualifier: \"g\",\n  schema: gravitySchema.describe(\n    JSON.stringify({\n      text: \"Determines which part of an asset to focus on. Note: Default of auto is applied for supported crop modes only.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#g_gravity\"\n    })\n  )\n};\nvar heightSchema = z.union([z.number(), z.string()]);\nvar height = {\n  qualifier: \"h\",\n  schema: heightSchema.describe(\n    JSON.stringify({\n      text: \"A qualifier that determines the height of a transformed asset or an overlay.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#h_height\"\n    })\n  )\n};\nvar multipleSchema = z.boolean();\nvar multiple = {\n  schema: multipleSchema.describe(\n    JSON.stringify({\n      text: \"Should generative AI features detect multiple instances.\"\n    })\n  )\n};\nvar prompt = {\n  schema: z.string().describe(\n    JSON.stringify({\n      text: \"Natural language descriptions used for generative AI capabilities.\"\n    })\n  )\n};\nvar widthSchema = z.union([z.number(), z.string()]);\nvar width = {\n  qualifier: \"w\",\n  schema: widthSchema.describe(\n    JSON.stringify({\n      text: \"A qualifier that sets the desired width of an asset using a specified value, or automatically based on the available width.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#w_width\"\n    })\n  )\n};\nvar x = {\n  qualifier: \"x\",\n  schema: z.union([z.string(), z.number()]).describe(\n    JSON.stringify({\n      text: \"Adjusts the starting location or offset of the x axis.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#x_y_coordinates\"\n    })\n  )\n};\nvar y = {\n  qualifier: \"y\",\n  schema: z.union([z.string(), z.number()]).describe(\n    JSON.stringify({\n      text: \"Adjusts the starting location or offset of the y axis.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#x_y_coordinates\"\n    })\n  )\n};\nvar zoomSchema = z.string();\nvar zoom = {\n  schema: zoomSchema.describe(\n    JSON.stringify({\n      text: \"Controls how close to crop to the detected coordinates when using face-detection, custom-coordinate, or object-specific gravity.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#z_zoom\"\n    })\n  )\n};\n\n// src/constants/qualifiers.ts\nvar convertersColors = [\n  {\n    test: testColorIsHex,\n    convert: convertColorHexToRgb\n  }\n];\nvar primary = {\n  aspectRatio,\n  crop,\n  gravity,\n  height,\n  width\n};\nvar position = {\n  angle,\n  gravity,\n  x,\n  y\n};\nvar text = {\n  alignment: {\n    qualifier: false,\n    order: 6\n  },\n  antialias: {\n    qualifier: \"antialias\"\n  },\n  border: {\n    qualifier: \"bo\",\n    location: \"primary\"\n  },\n  color: {\n    qualifier: \"co\",\n    location: \"primary\",\n    converters: convertersColors\n  },\n  fontFamily: {\n    qualifier: false,\n    order: 1\n  },\n  fontSize: {\n    qualifier: false,\n    order: 2\n  },\n  fontStyle: {\n    qualifier: false,\n    order: 4\n  },\n  fontWeight: {\n    qualifier: false,\n    order: 3\n  },\n  hinting: {\n    qualifier: \"hinting\"\n  },\n  letterSpacing: {\n    qualifier: \"letter_spacing\"\n  },\n  lineSpacing: {\n    qualifier: \"line_spacing\"\n  },\n  stroke: {\n    qualifier: \"self\",\n    order: 7\n  },\n  textDecoration: {\n    qualifier: false,\n    order: 5\n  }\n};\nvar effects = {\n  angle,\n  art: {\n    prefix: \"e\",\n    qualifier: \"art\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Applies the selected artistic filter.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_art\"\n      })\n    )\n  },\n  autoBrightness: {\n    prefix: \"e\",\n    qualifier: \"auto_brightness\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically adjusts the image brightness and blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_auto_brightness\"\n      })\n    )\n  },\n  autoColor: {\n    prefix: \"e\",\n    qualifier: \"auto_color\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically adjusts the image color balance and blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_auto_color\"\n      })\n    )\n  },\n  autoContrast: {\n    prefix: \"e\",\n    qualifier: \"auto_contrast\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically adjusts the image contrast and blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_auto_contrast\"\n      })\n    )\n  },\n  assistColorblind: {\n    prefix: \"e\",\n    qualifier: \"assist_colorblind\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies stripes or color adjustment to help people with common color blind conditions to differentiate between colors that are similar for them.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_assist_colorblind\"\n      })\n    )\n  },\n  background: {\n    qualifier: \"b\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Applies a background to empty or transparent areas.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#b_background\"\n      })\n    )\n  },\n  blackwhite: {\n    prefix: \"e\",\n    qualifier: \"blackwhite\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Converts an image to black and white.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blackwhite\"\n      })\n    )\n  },\n  blur: {\n    prefix: \"e\",\n    qualifier: \"blur\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a blurring filter to an asset.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blur\"\n      })\n    )\n  },\n  blurFaces: {\n    prefix: \"e\",\n    qualifier: \"blur_faces\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Blurs all detected faces in an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blur_faces\"\n      })\n    )\n  },\n  blurRegion: {\n    prefix: \"e\",\n    qualifier: \"blur_region\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a blurring filter to the region of an image specified by x, y, width and height, or an area of text. If no region is specified, the whole image is blurred.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_blur_region\"\n      })\n    )\n  },\n  border: {\n    qualifier: \"bo\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Adds a solid border around an image or video.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#bo_border\"\n      })\n    )\n  },\n  brightness: {\n    prefix: \"e\",\n    qualifier: \"brightness\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the image or video brightness.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_brightness\"\n      })\n    )\n  },\n  brightnessHSB: {\n    prefix: \"e\",\n    qualifier: \"brightness_hsb\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts image brightness modulation in HSB to prevent artifacts in some images.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_brightness_hsb\"\n      })\n    )\n  },\n  cartoonify: {\n    prefix: \"e\",\n    qualifier: \"cartoonify\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a cartoon effect to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_cartoonify\"\n      })\n    )\n  },\n  color: {\n    qualifier: \"co\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"A qualifier that specifies the color to use with the corresponding transformation.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#co_color\"\n      })\n    ),\n    converters: convertersColors\n  },\n  colorize: {\n    prefix: \"e\",\n    qualifier: \"colorize\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Colorizes an image. By default, gray is used for colorization. You can specify a different color using the color qualifier.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_colorize\"\n      })\n    )\n  },\n  contrast: {\n    prefix: \"e\",\n    qualifier: \"contrast\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image or video contrast.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_contrast\"\n      })\n    )\n  },\n  displace: {\n    prefix: \"e\",\n    qualifier: \"distort\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Displaces the pixels in an image according to the color channels of the pixels in another specified image (a gradient map specified with the overlay parameter).\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_displace\"\n      })\n    )\n  },\n  distort: {\n    prefix: \"e\",\n    qualifier: \"distort\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Distorts an image to a new shape by either adjusting its corners or by warping it into an arc.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_distort\"\n      })\n    )\n  },\n  fillLight: {\n    prefix: \"e\",\n    qualifier: \"fill_light\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the fill light and optionally blends the result with the original image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_fill_light\"\n      })\n    )\n  },\n  gamma: {\n    prefix: \"e\",\n    qualifier: \"gamma\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the image or video gamma level.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_gamma\"\n      })\n    )\n  },\n  gradientFade: {\n    prefix: \"e\",\n    qualifier: \"gradient_fade\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a gradient fade effect from the edge of an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_gradient_fade\"\n      })\n    )\n  },\n  grayscale: {\n    prefix: \"e\",\n    qualifier: \"grayscale\",\n    schema: z2.boolean().describe(\n      JSON.stringify({\n        text: \"Converts an image to grayscale (multiple shades of gray).\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_grayscale\"\n      })\n    )\n  },\n  hue: {\n    prefix: \"e\",\n    qualifier: \"hue\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image's hue.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_hue\"\n      })\n    )\n  },\n  improve: {\n    prefix: \"e\",\n    qualifier: \"improve\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image's colors, contrast and brightness to improve its appearance.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_improve\"\n      })\n    )\n  },\n  loop: {\n    prefix: \"e\",\n    qualifier: \"loop\",\n    schema: z2.union([z2.boolean(), z2.number(), z2.string()]).describe(\n      JSON.stringify({\n        text: \"Loops a video or animated image the specified number of times.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_loop\"\n      })\n    )\n  },\n  multiply: {\n    prefix: \"e\",\n    qualifier: \"multiply\",\n    schema: z2.boolean().describe(\n      JSON.stringify({\n        text: \"A qualifier that blends image layers using the multiply blend mode\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_multiply\"\n      })\n    )\n  },\n  negate: {\n    prefix: \"e\",\n    qualifier: \"negate\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"https://cloudinary.com/documentation/transformation_reference#e_negate\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_negate\"\n      })\n    )\n  },\n  noise: {\n    prefix: \"e\",\n    qualifier: \"noise\",\n    schema: z2.boolean().describe(\n      JSON.stringify({\n        text: \"https://cloudinary.com/documentation/transformation_reference#e_noise\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_noise\"\n      })\n    )\n  },\n  oilPaint: {\n    prefix: \"e\",\n    qualifier: \"oil_paint\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"https://cloudinary.com/documentation/transformation_reference#e_oil_paint\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_oil_paint\"\n      })\n    )\n  },\n  opacity: {\n    qualifier: \"o\",\n    schema: z2.union([z2.string(), z2.number()]).describe(\n      JSON.stringify({\n        text: \"Adjusts the opacity of an asset and makes it semi-transparent.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#o_opacity\"\n      })\n    )\n  },\n  outline: {\n    prefix: \"e\",\n    qualifier: \"outline\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adds an outline effect to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_outline\"\n      })\n    )\n  },\n  pixelate: {\n    prefix: \"e\",\n    qualifier: \"pixelate\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a pixelation effect.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_pixelate\"\n      })\n    )\n  },\n  pixelateFaces: {\n    prefix: \"e\",\n    qualifier: \"pixelate_faces\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Pixelates all detected faces in an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_pixelate_faces\"\n      })\n    )\n  },\n  pixelateRegion: {\n    prefix: \"e\",\n    qualifier: \"pixelate_region\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Pixelates the region of an image specified by x, y, width and height, or an area of text.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_pixelate_region\"\n      })\n    )\n  },\n  radius: {\n    qualifier: \"r\",\n    schema: z2.union([z2.string(), z2.number()]).describe(\n      JSON.stringify({\n        text: \"Rounds the corners of an image or video.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#r_round_corners\"\n      })\n    )\n  },\n  redeye: {\n    prefix: \"e\",\n    qualifier: \"redeye\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Automatically removes red eyes in an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_redeye\"\n      })\n    )\n  },\n  replaceColor: {\n    prefix: \"e\",\n    qualifier: \"replace_color\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Maps an input color and those similar to the input color to corresponding shades of a specified output color.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_replace_color\"\n      })\n    )\n  },\n  saturation: {\n    prefix: \"e\",\n    qualifier: \"saturation\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adjusts an image or video saturation level.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_saturation\"\n      })\n    )\n  },\n  screen: {\n    prefix: \"e\",\n    qualifier: \"screen\",\n    schema: z2.boolean().describe(\n      JSON.stringify({\n        text: \"A qualifier that blends image layers using the screen blend mode.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_screen\"\n      })\n    )\n  },\n  sepia: {\n    prefix: \"e\",\n    qualifier: \"sepia\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Changes the color scheme of an image to sepia.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_sepia\"\n      })\n    )\n  },\n  shadow: {\n    prefix: \"e\",\n    qualifier: \"shadow\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Adds a gray shadow to the bottom right of an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_shadow\"\n      })\n    )\n  },\n  sharpen: {\n    prefix: \"e\",\n    qualifier: \"sharpen\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a sharpening filter.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_sharpen\"\n      })\n    )\n  },\n  shear: {\n    prefix: \"e\",\n    qualifier: \"shear\",\n    schema: z2.string().describe(\n      JSON.stringify({\n        text: \"Skews an image according to the two specified values in degrees.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_shear\"\n      })\n    )\n  },\n  simulateColorblind: {\n    prefix: \"e\",\n    qualifier: \"simulate_colorblind\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Simulates the way an image would appear to someone with the specified color blind condition.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_simulate_colorblind\"\n      })\n    )\n  },\n  tint: {\n    prefix: \"e\",\n    qualifier: \"tint\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Blends an image with one or more tint colors at a specified intensity.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_tint\"\n      })\n    )\n  },\n  trim: {\n    prefix: \"e\",\n    qualifier: \"trim\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Detects and removes image edges whose color is similar to the corner pixels.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_trim\"\n      })\n    )\n  },\n  unsharpMask: {\n    prefix: \"e\",\n    qualifier: \"unsharp_mask\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies an unsharp mask filter to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_unsharp_mask\"\n      })\n    )\n  },\n  vectorize: {\n    prefix: \"e\",\n    qualifier: \"vectorize\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Vectorizes an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_vectorize\"\n      })\n    )\n  },\n  vibrance: {\n    prefix: \"e\",\n    qualifier: \"vibrance\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a vibrance filter to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_vibrance\"\n      })\n    )\n  },\n  vignette: {\n    prefix: \"e\",\n    qualifier: \"vignette\",\n    schema: z2.union([z2.string(), z2.boolean()]).describe(\n      JSON.stringify({\n        text: \"Applies a vignette effect to an image.\",\n        url: \"https://cloudinary.com/documentation/transformation_reference#e_vignette\"\n      })\n    )\n  }\n};\n\n// src/types/asset.ts\nimport { z as z14 } from \"zod\";\n\n// src/plugins/cropping.ts\nimport { z as z3 } from \"zod\";\n\n// src/lib/transformations.ts\nfunction constructTransformation({\n  prefix,\n  qualifier,\n  value,\n  converters\n}) {\n  let transformation = \"\";\n  if (prefix) {\n    transformation = `${prefix}_`;\n  }\n  let transformationValue = value;\n  converters?.forEach(({ test, convert }) => {\n    if (!test(transformationValue)) return;\n    transformationValue = convert(transformationValue);\n  });\n  if (transformationValue === true || transformationValue === \"true\") {\n    return `${transformation}${qualifier}`;\n  }\n  if (typeof transformationValue === \"string\" || typeof transformationValue === \"number\") {\n    if (prefix) {\n      return `${transformation}${qualifier}:${transformationValue}`;\n    } else {\n      return `${qualifier}_${transformationValue}`;\n    }\n  }\n}\nfunction promptArrayToString(promptArray) {\n  return `(${promptArray.join(\";\")})`;\n}\nfunction normalizeNumberParameter(param) {\n  if (typeof param !== \"string\") return param;\n  return parseInt(param);\n}\n\n// src/plugins/cropping.ts\nvar cropsAspectRatio = [\"auto\", \"crop\", \"fill\", \"lfill\", \"fill_pad\", \"thumb\"];\nvar cropsGravityAuto = [\"auto\", \"crop\", \"fill\", \"lfill\", \"fill_pad\", \"thumb\"];\nvar cropsWithZoom = [\"crop\", \"thumb\"];\nvar DEFAULT_CROP = \"limit\";\nvar cropOptionsSchema = z3.object({\n  aspectRatio: aspectRatio.schema.optional(),\n  type: crop.schema,\n  gravity: gravity.schema.optional(),\n  height: height.schema.optional(),\n  width: width.schema.optional(),\n  x: x.schema.optional(),\n  y: y.schema.optional(),\n  zoom: zoom.schema.optional(),\n  source: z3.boolean().optional()\n});\nvar croppingProps = {\n  aspectRatio: aspectRatio.schema.optional(),\n  crop: z3.union([\n    crop.schema,\n    cropOptionsSchema,\n    z3.array(cropOptionsSchema)\n  ]).default(DEFAULT_CROP).optional(),\n  gravity: gravity.schema.optional(),\n  zoom: zoom.schema.optional()\n};\nvar croppingPlugin = {\n  props: croppingProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    let crops = [];\n    if (typeof options.crop === \"string\" || typeof options.crop === \"undefined\") {\n      crops.push({\n        aspectRatio: options.aspectRatio,\n        height: options.height,\n        gravity: options.gravity,\n        type: options.crop || DEFAULT_CROP,\n        width: options.width,\n        zoom: options.zoom\n      });\n    } else if (typeof options.crop === \"object\" && !Array.isArray(options.crop)) {\n      crops.push(options.crop);\n    } else if (Array.isArray(options.crop)) {\n      crops = options.crop;\n    }\n    if (crops.length === 1 && crops[0].source === true) {\n      crops.push({\n        aspectRatio: options.aspectRatio,\n        width: options.width,\n        height: options.height,\n        gravity: options.gravity,\n        type: DEFAULT_CROP,\n        zoom: options.zoom\n      });\n    }\n    const finalTransformations = [];\n    const sourceTransformations = [];\n    for (const crop2 of crops) {\n      const cropDimensions = {\n        width: crop2.width,\n        height: crop2.height\n      };\n      if (typeof cropDimensions.width === \"undefined\" && typeof crop2.aspectRatio === \"undefined\") {\n        cropDimensions.width = options.width;\n        if (typeof cropDimensions.height === \"undefined\") {\n          cropDimensions.height = options.height;\n        }\n      }\n      const transformations = collectTransformations({\n        aspectRatio: crop2.aspectRatio,\n        gravity: crop2.gravity,\n        type: crop2.type || DEFAULT_CROP,\n        x: crop2.x,\n        y: crop2.y,\n        zoom: crop2.zoom,\n        ...cropDimensions\n      });\n      if (transformations.length > 0) {\n        if (crop2.source === true) {\n          sourceTransformations.push(transformations);\n        } else {\n          finalTransformations.push(transformations);\n        }\n      }\n    }\n    sourceTransformations.forEach((transformation) => {\n      if (transformation.length > 0) {\n        cldAsset.addTransformation(transformation.join(\",\"));\n      }\n    });\n    const results = {\n      options: {}\n    };\n    if (results.options && finalTransformations.length > 0) {\n      results.options.resize = finalTransformations.map((transformation) => transformation.join(\",\")).join(\"/\");\n    }\n    return results;\n  }\n};\nfunction collectTransformations(collectOptions) {\n  const { aspectRatio: aspectRatio2, type: crop2, x: x2, y: y2, zoom: zoom2 } = collectOptions;\n  let gravity2 = collectOptions.gravity;\n  const height2 = normalizeNumberParameter(collectOptions.height);\n  const width2 = normalizeNumberParameter(collectOptions.width);\n  const transformations = [];\n  const hasDefinedDimensions = height2 || width2;\n  const hasValidAspectRatio = aspectRatio2 && cropsAspectRatio.includes(crop2);\n  const hasXCoordinate = typeof x2 === \"number\" || typeof x2 === \"string\";\n  const hasYCoordinate = typeof y2 === \"number\" || typeof y2 === \"string\";\n  const hasDefinedCoordinates = hasXCoordinate || hasYCoordinate;\n  if (crop2 && (hasDefinedDimensions || hasValidAspectRatio || hasDefinedCoordinates)) {\n    transformations.push(`c_${crop2}`);\n  }\n  if (hasValidAspectRatio) {\n    transformations.push(`ar_${aspectRatio2}`);\n  }\n  if (width2) {\n    transformations.push(`w_${width2}`);\n  }\n  if (![\"limit\"].includes(crop2) && typeof height2 === \"number\") {\n    transformations.push(`h_${height2}`);\n  }\n  if (hasXCoordinate) {\n    transformations.push(`x_${x2}`);\n  }\n  if (hasYCoordinate) {\n    transformations.push(`y_${y2}`);\n  }\n  if (!gravity2 && cropsGravityAuto.includes(crop2) && !hasDefinedCoordinates) {\n    gravity2 = \"auto\";\n  }\n  if (gravity2) {\n    if (gravity2 === \"auto\" && !cropsGravityAuto.includes(crop2)) {\n      console.warn(\n        `Auto gravity can only be used with crop modes: ${cropsGravityAuto.join(\n          \", \"\n        )}. Not applying gravity.`\n      );\n    } else {\n      transformations.push(`g_${gravity2}`);\n    }\n  }\n  if (zoom2) {\n    if (zoom2 === \"auto\" && !cropsWithZoom.includes(crop2)) {\n      console.warn(\n        `Zoom can only be used with crop modes: ${cropsWithZoom.join(\n          \", \"\n        )}. Not applying zoom.`\n      );\n    } else {\n      transformations.push(`z_${zoom2}`);\n    }\n  }\n  return transformations;\n}\n\n// src/plugins/effects.ts\nimport { z as z4 } from \"zod\";\nvar effectProps = {\n  angle: effects.angle.schema.optional(),\n  art: effects.art.schema.optional(),\n  autoBrightness: effects.autoBrightness.schema.optional(),\n  autoColor: effects.autoColor.schema.optional(),\n  autoContrast: effects.autoContrast.schema.optional(),\n  assistColorblind: effects.assistColorblind.schema.optional(),\n  background: effects.background.schema.optional(),\n  blackwhite: effects.blackwhite.schema.optional(),\n  blur: effects.blur.schema.optional(),\n  blurFaces: effects.blurFaces.schema.optional(),\n  blurRegion: effects.blurRegion.schema.optional(),\n  border: effects.border.schema.optional(),\n  brightness: effects.brightness.schema.optional(),\n  brightnessHSB: effects.brightnessHSB.schema.optional(),\n  cartoonify: effects.cartoonify.schema.optional(),\n  color: effects.color.schema.optional(),\n  colorize: effects.colorize.schema.optional(),\n  contrast: effects.contrast.schema.optional(),\n  distort: effects.distort.schema.optional(),\n  fillLight: effects.fillLight.schema.optional(),\n  gamma: effects.gamma.schema.optional(),\n  gradientFade: effects.gradientFade.schema.optional(),\n  grayscale: effects.grayscale.schema.optional(),\n  improve: effects.improve.schema.optional(),\n  loop: effects.loop.schema.optional(),\n  multiply: effects.multiply.schema.optional(),\n  negate: effects.negate.schema.optional(),\n  oilPaint: effects.oilPaint.schema.optional(),\n  opacity: effects.opacity.schema.optional(),\n  outline: effects.outline.schema.optional(),\n  pixelate: effects.pixelate.schema.optional(),\n  pixelateFaces: effects.pixelateFaces.schema.optional(),\n  pixelateRegion: effects.pixelateRegion.schema.optional(),\n  radius: effects.radius.schema.optional(),\n  redeye: effects.redeye.schema.optional(),\n  replaceColor: effects.replaceColor.schema.optional(),\n  saturation: effects.saturation.schema.optional(),\n  screen: effects.screen.schema.optional(),\n  sepia: effects.sepia.schema.optional(),\n  shadow: effects.shadow.schema.optional(),\n  sharpen: effects.sharpen.schema.optional(),\n  shear: effects.shear.schema.optional(),\n  simulateColorblind: effects.simulateColorblind.schema.optional(),\n  tint: effects.tint.schema.optional(),\n  trim: effects.trim.schema.optional(),\n  unsharpMask: effects.unsharpMask.schema.optional(),\n  vectorize: effects.vectorize.schema.optional(),\n  vibrance: effects.vibrance.schema.optional(),\n  vignette: effects.vignette.schema.optional()\n};\nvar effectsProps = {\n  effects: z4.array(z4.object(effectProps)).describe(\n    JSON.stringify({\n      text: \"Array of objects specifying transformations to be applied to asset.\"\n    })\n  ).optional(),\n  ...effectProps\n};\nvar effectsPlugin = {\n  props: effectsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const transformationStrings = constructTransformationString({\n      effects,\n      options\n    });\n    transformationStrings.filter((t) => !!t).forEach((transformation) => cldAsset.effect(transformation));\n    if (Array.isArray(options?.effects)) {\n      options?.effects.forEach((effectsSet) => {\n        const transformationString = constructTransformationString({\n          effects,\n          options: effectsSet\n        }).filter((t) => !!t).join(\",\");\n        cldAsset.effect(transformationString);\n      });\n    }\n    function constructTransformationString({\n      effects: effects2,\n      options: options2\n    }) {\n      return Object.keys(effects2).map(\n        (key) => {\n          const { prefix, qualifier, converters } = effects2[key];\n          return constructTransformation({\n            qualifier,\n            prefix,\n            value: options2?.[key],\n            converters\n          });\n        }\n      );\n    }\n    return {};\n  }\n};\n\n// src/plugins/flags.ts\nvar { flagsEnum: flagsEnum2 } = parameters_exports;\nvar flagsProps = {\n  flags: flags.schema.optional()\n};\nvar flagsPlugin = {\n  props: flagsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { flags: flags2 = [] } = options;\n    if (Array.isArray(flags2) && flags2.length > 0) {\n      flags2.forEach((flag) => {\n        const { success } = flagsEnum2.safeParse(flag);\n        if (!success) {\n          if (process.env.NODE_ENV === \"development\") {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        cldAsset.addFlag(flag);\n      });\n    } else if (typeof flags2 === \"object\") {\n      Object.entries(flags2).forEach(([qualifier, value]) => {\n        const { success } = flagsEnum2.safeParse(qualifier);\n        if (!success) {\n          if (process.env.NODE_ENV === \"development\") {\n            console.warn(`Invalid flag ${qualifier}, not applying.`);\n          }\n          return;\n        }\n        cldAsset.addTransformation(`fl_${qualifier}:${value}`);\n      });\n    }\n    return {};\n  }\n};\n\n// src/plugins/named-transformations.ts\nimport { z as z5 } from \"zod\";\nvar NamedTransformationSchema = z5.string();\nvar namedTransformationsProps = {\n  namedTransformations: z5.union([NamedTransformationSchema, z5.array(NamedTransformationSchema)]).describe(\n    JSON.stringify({\n      text: \"Named transformations to apply to asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#named_transformations\"\n    })\n  ).optional(),\n  /**\n   * @deprecated use {@link `namedTransformations`} instead\n   */\n  transformations: z5.union([NamedTransformationSchema, z5.array(NamedTransformationSchema)]).describe(\n    JSON.stringify({\n      text: \"Deprecated: use namedTransformations instead\",\n      url: \"https://cloudinary.com/documentation/image_transformations#named_transformations\"\n    })\n  ).optional()\n};\nvar namedTransformationsPlugin = {\n  props: namedTransformationsProps,\n  strict: true,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { transformations, namedTransformations } = options;\n    if (transformations && process.env.NODE_ENVIRONMENT === \"development\") {\n      console.warn(\n        \"The transformations prop is deprecated. Please use namedTransformations instead.\"\n      );\n    }\n    let _namedTransformations = namedTransformations || transformations || [];\n    if (!Array.isArray(_namedTransformations)) {\n      _namedTransformations = [_namedTransformations];\n    }\n    _namedTransformations.forEach((transformation) => {\n      cldAsset.addTransformation(`t_${transformation}`);\n    });\n    return {};\n  }\n};\n\n// src/plugins/overlays.ts\nimport { encodeBase64, objectHasKey, sortByKey } from \"@cloudinary-util/util\";\nimport { z as z6 } from \"zod\";\nvar overlayTextSchema = z6.object({\n  alignment: z6.string().optional(),\n  antialias: z6.string().optional(),\n  border: z6.string().optional(),\n  color: z6.string().optional(),\n  fontFamily: z6.string().optional(),\n  fontSize: z6.number().optional(),\n  fontStyle: z6.union([z6.string(), z6.number()]).optional(),\n  fontWeight: z6.string().optional(),\n  hinting: z6.union([z6.string(), z6.number()]).optional(),\n  letterSpacing: z6.union([z6.string(), z6.number()]).optional(),\n  lineSpacing: z6.union([z6.string(), z6.number()]).optional(),\n  stroke: z6.string().optional(),\n  text: z6.string()\n  // Required if using object format\n});\nvar overlayPositionSchema = z6.object({\n  angle: angle.schema.optional(),\n  gravity: gravity.schema.optional(),\n  x: x.schema.optional(),\n  y: y.schema.optional()\n});\nvar overlaySchema = z6.object({\n  appliedEffects: z6.array(z6.object({})).optional(),\n  appliedFlags: flags.schema.optional(),\n  effects: z6.array(z6.object({})).optional(),\n  crop: crop.schema.optional(),\n  flags: flags.schema.optional(),\n  height: height.schema.optional(),\n  position: overlayPositionSchema.optional(),\n  publicId: z6.string().optional(),\n  text: z6.union([z6.string(), overlayTextSchema]).optional(),\n  url: z6.string().optional(),\n  width: width.schema.optional()\n});\nvar DEFAULT_TEXT_OPTIONS = {\n  color: \"black\",\n  fontFamily: \"Arial\",\n  fontSize: 200,\n  fontWeight: \"bold\"\n};\nvar overlaysProps = {\n  overlay: overlaySchema.describe(\n    JSON.stringify({\n      text: \"Image or text layer that is applied on top of the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional(),\n  overlays: z6.array(overlaySchema).describe(\n    JSON.stringify({\n      text: \"Image or text layers that are applied on top of the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional(),\n  text: z6.string().describe(\n    JSON.stringify({\n      text: \"Text to be overlaid on asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#transformation_url_structure\"\n    })\n  ).optional()\n};\nvar overlaysPlugin = {\n  props: overlaysProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { text: text2, overlays = [] } = options;\n    const type = \"overlay\";\n    const typeQualifier = \"l\";\n    if (Array.isArray(overlays)) {\n      overlays.forEach(applyOverlay);\n    }\n    if (typeof text2 === \"string\") {\n      applyOverlay({\n        text: Object.assign({}, DEFAULT_TEXT_OPTIONS, {\n          text: text2\n        })\n      });\n    } else if (typeof text2 === \"object\") {\n      applyOverlay({\n        text: Object.assign({}, DEFAULT_TEXT_OPTIONS, text2)\n      });\n    }\n    function applyOverlay({\n      publicId,\n      url,\n      position: position2,\n      text: text3,\n      effects: layerEffects = [],\n      appliedEffects = [],\n      flags: layerFlags = [],\n      appliedFlags = [],\n      ...options2\n    }) {\n      const hasPublicId = typeof publicId === \"string\";\n      const hasUrl = typeof url === \"string\";\n      const hasText = typeof text3 === \"object\" || typeof text3 === \"string\";\n      const hasPosition = typeof position2 === \"object\";\n      if (!hasPublicId && !hasUrl && !hasText) {\n        console.warn(`An ${type} is missing Public ID, URL, or Text`);\n        return;\n      }\n      let layerTransformation;\n      if (hasText) {\n        layerTransformation = `${typeQualifier}_text`;\n      } else if (hasPublicId) {\n        layerTransformation = `${typeQualifier}_${publicId.replace(\n          /\\//g,\n          \":\"\n        )}`;\n      } else if (hasUrl) {\n        layerTransformation = `${typeQualifier}_fetch:${encodeBase64(url)}`;\n      }\n      const primary2 = [];\n      const applied = [];\n      Object.keys(options2).forEach((key) => {\n        if (!objectHasKey(primary, key)) return;\n        const { qualifier, converters } = primary[key];\n        const transformation = constructTransformation({\n          qualifier,\n          value: options2[key],\n          converters\n        });\n        if (transformation) {\n          primary2.push(transformation);\n        }\n      });\n      layerEffects.forEach((effect) => {\n        Object.keys(effect).forEach((key) => {\n          const effectQualifier = primary[key] || effects[key] || position[key];\n          if (!effectQualifier) return;\n          const { qualifier, prefix, converters } = effectQualifier;\n          const transformation = constructTransformation({\n            qualifier,\n            prefix,\n            value: effect[key],\n            converters\n          });\n          if (transformation) {\n            primary2.push(transformation);\n          }\n        });\n      });\n      appliedEffects.forEach((effect) => {\n        Object.keys(effect).forEach((key) => {\n          const effectQualifier = primary[key] || effects[key] || position[key];\n          if (!effectQualifier) return;\n          const { qualifier, prefix, converters } = effectQualifier;\n          const transformation = constructTransformation({\n            qualifier,\n            prefix,\n            value: effect[key],\n            converters\n          });\n          if (transformation) {\n            applied.push(transformation);\n          }\n        });\n      });\n      const activeLayerFlags = Array.isArray(layerFlags) ? layerFlags : [layerFlags];\n      activeLayerFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (process.env.NODE_ENV === \"development\") {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        primary2.push(`fl_${flag}`);\n      });\n      const activeAppliedFlags = Array.isArray(appliedFlags) ? appliedFlags : [appliedFlags];\n      activeAppliedFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (process.env.NODE_ENV === \"development\") {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        applied.push(`fl_${flag}`);\n      });\n      if (hasText) {\n        if (typeof text3 === \"string\") {\n          text3 = {\n            ...DEFAULT_TEXT_OPTIONS,\n            text: text3\n          };\n        }\n        const textTransformations = [];\n        if (typeof text3 === \"object\") {\n          const textOptions = Object.keys(text3).filter((key) => objectHasKey(text, key)).map((key) => {\n            const value = text3 && objectHasKey(text3, key) && text3[key];\n            return {\n              ...text[key],\n              key,\n              value,\n              order: text[key].order || 99\n            };\n          });\n          const sortedTextOptions = sortByKey(textOptions, \"order\");\n          for (const textOption of sortedTextOptions) {\n            const { key, value, qualifier, location, converters } = textOption;\n            let textValue = value;\n            converters?.forEach(({ test, convert }) => {\n              if (!test(value)) return;\n              textValue = convert(value);\n            });\n            if (location === \"primary\") {\n              primary2.push(`${qualifier}_${textValue}`);\n            } else if (qualifier === \"self\") {\n              textTransformations.push(key);\n            } else if (qualifier) {\n              textTransformations.push(`${qualifier}_${textValue}`);\n            } else {\n              textTransformations.push(textValue);\n            }\n          }\n        }\n        const specialCharacters = {\n          \".\": \"%2E\",\n          \",\": \"%2C\",\n          \"/\": \"%2F\"\n        };\n        let layerText = text3?.text || \"\";\n        if (typeof layerText === \"string\") {\n          Object.keys(specialCharacters)?.forEach((character) => {\n            layerText = layerText?.replace(\n              character,\n              specialCharacters[character]\n            );\n          });\n        }\n        layerTransformation = `${layerTransformation}:${textTransformations.join(\n          \"_\"\n        )}:${layerText}`;\n      }\n      if (hasPosition) {\n        Object.keys(position2).forEach((key) => {\n          if (!objectHasKey(position, key) || !objectHasKey(position2, key))\n            return;\n          const { qualifier, converters } = position[key];\n          const transformation = constructTransformation({\n            qualifier,\n            value: position2[key],\n            converters\n          });\n          if (transformation) {\n            applied.push(transformation);\n          }\n        });\n      }\n      if (primary2.length > 0) {\n        layerTransformation = `${layerTransformation},${primary2.join(\",\")}`;\n      }\n      layerTransformation = `${layerTransformation}/fl_layer_apply,fl_no_overflow`;\n      if (applied.length > 0) {\n        layerTransformation = `${layerTransformation},${applied.join(\",\")}`;\n      }\n      cldAsset.addTransformation(layerTransformation);\n    }\n    return {};\n  }\n};\n\n// src/plugins/preserve-transformations.ts\nimport { getTransformations } from \"@cloudinary-util/util\";\nimport { z as z7 } from \"zod\";\nvar preserveTransformationsProps = {\n  preserveTransformations: z7.boolean().describe(\n    JSON.stringify({\n      text: \"Preserves transformations from a Cloudinary URL when using using a Cloudinary URL as the asset source (src).\"\n    })\n  ).optional()\n};\nvar preserveTransformationsPlugin = {\n  props: preserveTransformationsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { preserveTransformations = false } = options;\n    if (preserveTransformations) {\n      try {\n        const transformations = getTransformations(options.src).map((t) => t.join(\",\"));\n        transformations.flat().forEach((transformation) => {\n          cldAsset.addTransformation(transformation);\n        });\n      } catch (e) {\n        console.warn(`Failed to preserve transformations: ${e.message}`);\n      }\n    }\n    return {};\n  }\n};\n\n// src/plugins/raw-transformations.ts\nimport { z as z8 } from \"zod\";\nvar RawTransformationSchema = z8.string();\nvar rawTransformationsProps = {\n  rawTransformations: z8.union([RawTransformationSchema, z8.array(RawTransformationSchema)]).describe(\n    JSON.stringify({\n      text: \"Array of transformation parameters using the Cloudinary URL API to apply to an asset.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference\"\n    })\n  ).optional()\n};\nvar rawTransformationsPlugin = {\n  props: rawTransformationsProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    let { rawTransformations = [] } = options;\n    if (!Array.isArray(rawTransformations)) {\n      rawTransformations = [rawTransformations];\n    }\n    rawTransformations.forEach((transformation) => {\n      cldAsset.addTransformation(transformation);\n    });\n    return {};\n  }\n};\n\n// src/plugins/remove-background.ts\nimport { z as z9 } from \"zod\";\nvar removeBackgroundProps = {\n  removeBackground: z9.boolean().describe(\n    JSON.stringify({\n      text: \"Removes the background of an image using the Cloudinary AI Background Removal Add-On (Required).\",\n      url: \"https://cloudinary.com/documentation/cloudinary_ai_background_removal_addon\"\n    })\n  ).optional()\n};\nvar removeBackgroundPlugin = {\n  props: removeBackgroundProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { removeBackground = false } = options;\n    if (removeBackground) {\n      cldAsset.effect(\"e_background_removal\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/sanitize.ts\nimport { z as z10 } from \"zod\";\nvar sanitizeProps = {\n  sanitize: z10.boolean().describe(\n    JSON.stringify({\n      text: \"Runs a sanitizer on SVG images.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#fl_sanitize\"\n    })\n  ).optional()\n};\nvar sanitizePlugin = {\n  props: sanitizeProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { sanitize = true } = options;\n    const shouldApplySanitizer = sanitize && (options.format === \"svg\" || cldAsset.publicID.endsWith(\".svg\"));\n    if (shouldApplySanitizer) {\n      cldAsset.effect(\"fl_sanitize\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/seo.ts\nimport { z as z11 } from \"zod\";\nvar seoProps = {\n  seoSuffix: z11.string().describe(\n    JSON.stringify({\n      text: \"Configures the URL to include an SEO-friendly suffix in the URL\",\n      url: \"https://cloudinary.com/documentation/advanced_url_delivery_options#seo_friendly_media_asset_urls\"\n    })\n  ).optional()\n};\nvar seoPlugin = {\n  props: seoProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { seoSuffix } = options;\n    if (typeof seoSuffix === \"string\") {\n      if (options.deliveryType === \"fetch\") {\n        console.warn(\n          \"SEO suffix is not supported with a delivery type of fetch\"\n        );\n      } else {\n        cldAsset.setSuffix(seoSuffix);\n      }\n    }\n    return {};\n  }\n};\n\n// src/plugins/underlays.ts\nimport { objectHasKey as objectHasKey2 } from \"@cloudinary-util/util\";\nimport { z as z12 } from \"zod\";\nvar underlayPositionSchema = z12.object({\n  angle: angle.schema.optional(),\n  gravity: gravity.schema.optional(),\n  x: x.schema.optional(),\n  y: y.schema.optional()\n});\nvar underlaySchema = z12.object({\n  appliedEffects: z12.array(z12.object({})).optional(),\n  appliedFlags: flags.schema.optional(),\n  effects: z12.array(z12.object({})).optional(),\n  crop: crop.schema.optional(),\n  flags: flags.schema.optional(),\n  height: height.schema.optional(),\n  position: underlayPositionSchema.optional(),\n  publicId: z12.string().optional(),\n  type: z12.string().optional(),\n  url: z12.string().optional(),\n  width: width.schema.optional()\n});\nvar underlaysProps = {\n  underlay: z12.string().describe(\n    JSON.stringify({\n      text: \"Public ID of image that is applied under the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional(),\n  underlays: z12.array(underlaySchema).describe(\n    JSON.stringify({\n      text: \"Image layers that are applied under the base image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#l_layer\"\n    })\n  ).optional()\n};\nvar underlaysPlugin = {\n  props: underlaysProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { underlay, underlays = [] } = options;\n    const typeQualifier = \"u\";\n    if (Array.isArray(underlays)) {\n      underlays.forEach(applyUnderlay);\n    }\n    if (typeof underlay === \"string\") {\n      const underlayOptions = {\n        publicId: underlay,\n        crop: \"fill\",\n        width: \"1.0\",\n        height: \"1.0\",\n        flags: [\"relative\"]\n      };\n      applyUnderlay(underlayOptions);\n    }\n    function applyUnderlay({\n      publicId,\n      type,\n      position: position2,\n      effects: layerEffects = [],\n      flags: layerFlags = [],\n      appliedFlags = [],\n      ...options2\n    }) {\n      const hasPublicId = typeof publicId === \"string\";\n      const hasPosition = typeof position2 === \"object\";\n      if (!hasPublicId) {\n        console.warn(`An ${type} is missing a Public ID`);\n        return;\n      }\n      let layerTransformation = `${typeQualifier}_${publicId.replace(\n        /\\//g,\n        \":\"\n      )}`;\n      const primary2 = [];\n      const applied = [];\n      Object.keys(options2).forEach((key) => {\n        if (!objectHasKey2(primary, key)) return;\n        const { qualifier } = primary[key];\n        primary2.push(`${qualifier}_${options2[key]}`);\n      });\n      layerEffects.forEach((effect) => {\n        Object.keys(effect).forEach((key) => {\n          if (!objectHasKey2(primary, key)) return;\n          const { qualifier } = primary[key];\n          primary2.push(`${qualifier}_${effect[key]}`);\n        });\n      });\n      if (hasPosition) {\n        Object.keys(position2).forEach(\n          (key) => {\n            if (!objectHasKey2(position, key)) return;\n            const { qualifier } = position[key];\n            applied.push(`${qualifier}_${position2[key]}`);\n          }\n        );\n      }\n      const activeLayerFlags = Array.isArray(layerFlags) ? layerFlags : [layerFlags];\n      activeLayerFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (process.env.NODE_ENV === \"development\") {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        primary2.push(`fl_${flag}`);\n      });\n      const activeAppliedFlags = Array.isArray(appliedFlags) ? appliedFlags : [appliedFlags];\n      activeAppliedFlags.forEach((flag) => {\n        const { success } = flagsEnum.safeParse(flag);\n        if (!success) {\n          if (process.env.NODE_ENV === \"development\") {\n            console.warn(`Invalid flag ${flag}, not applying.`);\n          }\n          return;\n        }\n        applied.push(`fl_${flag}`);\n      });\n      layerTransformation = `${layerTransformation},${primary2.join(\",\")}`;\n      layerTransformation = `${layerTransformation}/fl_layer_apply,fl_no_overflow`;\n      if (applied.length > 0) {\n        layerTransformation = `${layerTransformation},${applied.join(\",\")}`;\n      }\n      cldAsset.addTransformation(layerTransformation);\n    }\n    return {};\n  }\n};\n\n// src/plugins/version.ts\nimport { z as z13 } from \"zod\";\nvar versionProps = {\n  version: z13.union([z13.number(), z13.string()]).describe(\n    JSON.stringify({\n      text: \"Custom version number to apply to asset URL.\",\n      url: \"https://cloudinary.com/documentation/advanced_url_delivery_options#asset_versions\"\n    })\n  ).optional()\n};\nvar versionPlugin = {\n  props: versionProps,\n  assetTypes: [\"image\", \"images\", \"video\", \"videos\"],\n  plugin: ({ cldAsset, options }) => {\n    const { version } = options;\n    if (typeof version === \"string\" || typeof version === \"number\") {\n      cldAsset.setVersion(`${version}`.replace(\"v\", \"\"));\n    }\n    return {};\n  }\n};\n\n// src/types/asset.ts\nvar assetOptionsSchema = z14.object({\n  assetType: z14.string().default(\"image\").describe(\n    JSON.stringify({\n      text: \"The type of asset to deliver.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#transformation_url_structure\"\n    })\n  ).optional(),\n  deliveryType: z14.string().default(\"upload\").describe(\n    JSON.stringify({\n      text: \"Delivery method of the asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#delivery_types\"\n    })\n  ).optional(),\n  dpr: z14.union([z14.string(), z14.number()]).describe(\n    JSON.stringify({\n      text: \"Delivery method of the asset.\",\n      url: \"https://cloudinary.com/documentation/image_transformations#delivery_types\"\n    })\n  ).optional(),\n  format: z14.string().default(\"auto\").describe(\n    JSON.stringify({\n      text: \"Converts (if necessary) and delivers an asset in the specified format.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#f_format\"\n    })\n  ).optional(),\n  height: z14.union([z14.string(), z14.number()]).describe(\n    JSON.stringify({\n      text: \"Height of the given asset.\"\n    })\n  ).optional(),\n  quality: z14.union([z14.string(), z14.number(), z14.string()]).default(\"auto\").describe(\n    JSON.stringify({\n      text: \"Quality of the delivered asset\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#q_quality\"\n    })\n  ).optional(),\n  src: z14.string().describe(\n    JSON.stringify({\n      text: \"Cloudinary Public ID or versioned Cloudinary URL (/v1234/)\"\n    })\n  ),\n  strictTransformations: z14.boolean().describe(\n    JSON.stringify({\n      text: \"Gives you the ability to have more control over what transformations are permitted to be used from your Cloudinary account.\",\n      url: \"https://cloudinary.com/documentation/control_access_to_media#strict_transformations\"\n    })\n  ).optional(),\n  width: z14.union([z14.string(), z14.number()]).describe(\n    JSON.stringify({\n      text: \"Width of the given asset.\"\n    })\n  ).optional(),\n  // Spreading plugins instead of extend or merge to avoid excessive schema warning\n  // https://github.com/microsoft/TypeScript/issues/34933#issuecomment-**********\n  ...croppingProps,\n  ...effectsProps,\n  ...flagsProps,\n  ...namedTransformationsProps,\n  ...overlaysProps,\n  ...preserveTransformationsProps,\n  ...rawTransformationsProps,\n  ...removeBackgroundProps,\n  ...sanitizeProps,\n  ...seoProps,\n  ...underlaysProps,\n  ...versionProps\n});\n\n// src/types/image.ts\nimport { z as z25 } from \"zod\";\n\n// src/plugins/default-image.ts\nimport { z as z15 } from \"zod\";\nimport { getFormat } from \"@cloudinary-util/util\";\nvar defaultImageProps = {\n  defaultImage: z15.string().describe(\n    JSON.stringify({\n      text: \"Configures the default image to use in case the given public ID is not available. Must include file extension.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#d_default_image\"\n    })\n  ).optional()\n};\nvar defaultImagePlugin = {\n  props: defaultImageProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { defaultImage } = options;\n    if (typeof defaultImage === \"string\") {\n      if (!getFormat(defaultImage)) {\n        console.warn(\n          `The defaultImage prop may be missing a format and must include it along with the public ID. (Ex: myimage.jpg)`\n        );\n      }\n      const defaultImageId = defaultImage.replace(/\\//g, \":\");\n      cldAsset.addTransformation(`d_${defaultImageId}`);\n    }\n    return {};\n  }\n};\n\n// src/plugins/enhance.ts\nimport { z as z16 } from \"zod\";\nvar enhanceProps = {\n  enhance: z16.boolean().describe(\n    JSON.stringify({\n      text: \"Uses AI to analyze an image and make adjustments to enhance the appeal of the image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_enhance\"\n    })\n  ).optional()\n};\nvar enhancePlugin = {\n  props: enhanceProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { enhance = false } = options;\n    if (enhance) {\n      cldAsset.effect(\"e_enhance\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/extract.ts\nimport { z as z17 } from \"zod\";\nvar extractProps = {\n  extract: z17.union([\n    prompt.schema.optional(),\n    z17.array(prompt.schema).optional(),\n    z17.object({\n      invert: z17.boolean().default(false).optional(),\n      mode: extractMode.schema.optional(),\n      multiple: multiple.schema.default(false).optional(),\n      prompt: z17.union([prompt.schema, z17.array(prompt.schema)]).optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Extracts an area or multiple areas of an image, described in natural language.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_extract\"\n    })\n  ).optional()\n};\nvar extractPlugin = {\n  props: extractProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { extract } = options;\n    if (!extract || typeof extract === \"undefined\") return {};\n    const properties = [];\n    if (typeof extract === \"string\") {\n      properties.push(`prompt_${extract}`);\n    } else if (Array.isArray(extract)) {\n      properties.push(`prompt_${formatPrompts(extract)}`);\n    } else if (typeof extract === \"object\" && !Array.isArray(extract)) {\n      const prompt2 = formatPrompts(extract.prompt);\n      if (prompt2) {\n        properties.push(`prompt_${prompt2}`);\n      }\n      if (extract.invert === true) {\n        properties.push(\"invert_true\");\n      }\n      if (typeof extract.mode === \"string\") {\n        properties.push(`mode_${extract.mode}`);\n      }\n      if (extract.multiple === true) {\n        properties.push(\"multiple_true\");\n      }\n    }\n    if (properties.length > 0) {\n      const transformation = `e_extract:${properties.join(\";\")}`;\n      cldAsset.addTransformation(transformation);\n    }\n    return {};\n  }\n};\nfunction formatPrompts(prompt2) {\n  if (typeof prompt2 === \"string\") return prompt2;\n  if (Array.isArray(prompt2)) {\n    return `(${prompt2.filter((prompt3) => typeof prompt3 === \"string\").join(\";\")})`;\n  }\n  return void 0;\n}\n\n// src/plugins/fill-background.ts\nimport { z as z18 } from \"zod\";\nvar defaultCrop = \"pad\";\nvar fillBackgroundProps = {\n  fillBackground: z18.union([\n    z18.boolean(),\n    z18.object({\n      crop: crop.schema.optional(),\n      gravity: gravity.schema.optional(),\n      prompt: z18.string().optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Uses Generative Fill to extended padded image with AI\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#b_gen_fill\"\n    })\n  ).optional()\n};\nvar fillBackgroundPlugin = {\n  props: fillBackgroundProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { fillBackground } = options;\n    if (typeof fillBackground === \"undefined\") return {};\n    const width2 = normalizeNumberParameter(options.width);\n    const height2 = normalizeNumberParameter(options.height);\n    const hasDefinedDimensions = typeof height2 === \"number\" && typeof width2 === \"number\";\n    let aspectRatio2 = options.aspectRatio;\n    if (!aspectRatio2 && hasDefinedDimensions) {\n      aspectRatio2 = `${width2}:${height2}`;\n    }\n    if (!aspectRatio2) {\n      if (process.env.NODE_ENV === \"development\") {\n        console.warn(\n          `Could not determine aspect ratio based on available options to use fillBackground. Please specify width and height or an aspect ratio.`\n        );\n      }\n      return {};\n    }\n    if (fillBackground === true) {\n      const properties = [\n        \"b_gen_fill\",\n        `ar_${aspectRatio2}`,\n        `c_${defaultCrop}`\n      ];\n      cldAsset.addTransformation(properties.join(\",\"));\n    } else if (typeof fillBackground === \"object\") {\n      const { crop: crop2 = defaultCrop, gravity: gravity2, prompt: prompt2 } = fillBackground;\n      const properties = [`ar_${aspectRatio2}`, `c_${crop2}`];\n      if (typeof prompt2 === \"string\") {\n        properties.unshift(`b_gen_fill:${prompt2}`);\n      } else {\n        properties.unshift(`b_gen_fill`);\n      }\n      if (typeof gravity2 === \"string\") {\n        properties.push(`g_${gravity2}`);\n      }\n      cldAsset.addTransformation(properties.join(\",\"));\n    }\n    return {};\n  }\n};\n\n// src/plugins/recolor.ts\nimport { z as z19 } from \"zod\";\nvar imageOptionsRecolorPromptSchema = z19.union([\n  z19.string(),\n  z19.array(z19.string())\n]);\nvar imageOptionsRecolorSchema = z19.object({\n  prompt: imageOptionsRecolorPromptSchema.optional(),\n  to: z19.string().optional(),\n  multiple: z19.boolean().optional()\n});\nvar recolorProps = {\n  recolor: z19.union([imageOptionsRecolorPromptSchema, imageOptionsRecolorSchema]).describe(\n    JSON.stringify({\n      text: \"Uses generative AI to recolor parts of your image, maintaining the relative shading.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_recolor\"\n    })\n  ).optional()\n};\nvar recolorPlugin = {\n  props: recolorProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { recolor } = options;\n    const recolorOptions = {\n      prompt: void 0,\n      \"to-color\": void 0,\n      multiple: void 0\n    };\n    if (Array.isArray(recolor)) {\n      if (Array.isArray(recolor[0])) {\n        recolorOptions.prompt = promptArrayToString(recolor[0]);\n      } else {\n        recolorOptions.prompt = recolor[0];\n      }\n      if (typeof recolor[1] === \"string\") {\n        recolorOptions[\"to-color\"] = recolor[1];\n      }\n    } else if (typeof recolor === \"object\") {\n      if (typeof recolor.prompt === \"string\") {\n        recolorOptions.prompt = recolor.prompt;\n      } else if (Array.isArray(recolor.prompt)) {\n        recolorOptions.prompt = promptArrayToString(recolor.prompt);\n      }\n      if (typeof recolor.to === \"string\") {\n        recolorOptions[\"to-color\"] = recolor.to;\n      }\n      if (recolor.multiple === true) {\n        recolorOptions.multiple = `true`;\n      }\n    }\n    const transformation = Object.entries(recolorOptions).filter(([, value]) => !!value).map(([key, value]) => `${key}_${value}`).join(\";\");\n    if (transformation) {\n      cldAsset.addTransformation(`e_gen_recolor:${transformation}`);\n    }\n    return {};\n  }\n};\n\n// src/plugins/remove.ts\nimport { z as z20 } from \"zod\";\nvar imageOptionsRemovePromptSchema = z20.union([\n  z20.string(),\n  z20.array(z20.string())\n]);\nvar imageOptionsRemoveSchema = z20.object({\n  prompt: imageOptionsRemovePromptSchema.optional(),\n  region: z20.union([z20.array(z20.number()), z20.array(z20.array(z20.number()))]).optional(),\n  multiple: z20.boolean().optional(),\n  removeShadow: z20.boolean().optional()\n});\nvar removeProps = {\n  remove: z20.union([imageOptionsRemovePromptSchema, imageOptionsRemoveSchema]).describe(\n    JSON.stringify({\n      text: \"Applies zooming and/or panning to an image, resulting in a video or animated image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_zoompan\"\n    })\n  ).optional()\n};\nvar removePlugin = {\n  props: removeProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { remove } = options;\n    const removeOptions = {\n      prompt: void 0,\n      region: void 0,\n      multiple: void 0,\n      \"remove-shadow\": void 0\n    };\n    if (typeof remove === \"string\") {\n      removeOptions.prompt = remove;\n    } else if (Array.isArray(remove)) {\n      removeOptions.prompt = promptArrayToString(remove);\n    } else if (typeof remove === \"object\") {\n      const hasPrompt = typeof remove.prompt === \"string\" || Array.isArray(remove.prompt);\n      const hasRegion = Array.isArray(remove.region);\n      if (hasPrompt && hasRegion) {\n        throw new Error(\n          \"Invalid remove options: you can not have both a prompt and a region. More info: https://cloudinary.com/documentation/transformation_reference#e_gen_remove\"\n        );\n      }\n      if (typeof remove.prompt === \"string\") {\n        removeOptions.prompt = remove.prompt;\n      } else if (Array.isArray(remove.prompt)) {\n        removeOptions.prompt = promptArrayToString(remove.prompt);\n      }\n      if (Array.isArray(remove.region)) {\n        removeOptions.region = regionArrayToString(remove.region);\n      }\n      if (remove.multiple === true) {\n        removeOptions.multiple = `true`;\n      }\n      if (remove.removeShadow === true) {\n        removeOptions[\"remove-shadow\"] = `true`;\n      }\n    }\n    const transformation = Object.entries(removeOptions).filter(([, value]) => !!value).map(([key, value]) => `${key}_${value}`).join(\";\");\n    if (transformation) {\n      cldAsset.addTransformation(`e_gen_remove:${transformation}`);\n    }\n    return {};\n  }\n};\nfunction regionArrayToString(regionArray) {\n  const indexes = {\n    0: \"x\",\n    1: \"y\",\n    2: \"w\",\n    3: \"h\"\n  };\n  const regionString = regionArray.map((region, index) => {\n    if (Array.isArray(region)) {\n      return regionArrayToString(region);\n    }\n    const key = indexes[index];\n    return `${key}_${region}`;\n  }).join(\";\");\n  return `(${regionString})`;\n}\n\n// src/plugins/replace-background.ts\nimport { z as z21 } from \"zod\";\nvar replaceBackgroundProps = {\n  replaceBackground: z21.union([\n    z21.boolean(),\n    z21.string(),\n    z21.object({\n      seed: z21.number().optional(),\n      prompt: z21.string().optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Replaces the background of an image with an AI-generated background.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_background_replace\"\n    })\n  ).optional()\n};\nvar replaceBackgroundPlugin = {\n  props: replaceBackgroundProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { replaceBackground } = options;\n    if (!replaceBackground || typeof replaceBackground === \"undefined\") return {};\n    const properties = [];\n    if (typeof replaceBackground === \"object\") {\n      if (typeof replaceBackground.prompt !== \"undefined\") {\n        properties.push(`prompt_${replaceBackground.prompt}`);\n      }\n      if (typeof replaceBackground.seed === \"number\") {\n        properties.push(`seed_${replaceBackground.seed}`);\n      }\n    } else if (typeof replaceBackground === \"string\") {\n      properties.push(`prompt_${replaceBackground}`);\n    }\n    let transformation = \"e_gen_background_replace\";\n    if (properties.length > 0) {\n      transformation = `${transformation}:${properties.join(\";\")}`;\n    }\n    cldAsset.addTransformation(transformation);\n    return {};\n  }\n};\n\n// src/plugins/replace.ts\nimport { z as z22 } from \"zod\";\nvar replaceProps = {\n  replace: z22.union([\n    z22.array(z22.string()),\n    z22.array(z22.boolean()),\n    z22.object({\n      to: z22.string(),\n      from: z22.string(),\n      preserveGeometry: z22.boolean().optional()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Uses generative AI to replace parts of your image with something else.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_replace\"\n    })\n  ).optional()\n};\nvar replacePlugin = {\n  props: replaceProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { replace = null } = options;\n    if (replace) {\n      let from, to, preserveGeometry = false;\n      if (Array.isArray(replace)) {\n        from = replace[0];\n        to = replace[1];\n        preserveGeometry = replace[2] || false;\n      } else {\n        from = replace.from;\n        to = replace.to;\n        preserveGeometry = replace.preserveGeometry || false;\n      }\n      const properties = [`e_gen_replace:from_${from}`, `to_${to}`];\n      if (preserveGeometry) {\n        properties.push(`preserve-geometry_${preserveGeometry}`);\n      }\n      cldAsset.effect(properties.join(\";\"));\n    }\n    return {};\n  }\n};\n\n// src/plugins/restore.ts\nimport { z as z23 } from \"zod\";\nvar restoreProps = {\n  restore: z23.boolean().describe(\n    JSON.stringify({\n      text: \"Uses generative AI to restore details in poor quality images or images that may have become degraded through repeated processing and compression.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_gen_restore\"\n    })\n  ).optional()\n};\nvar restorePlugin = {\n  props: restoreProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { restore = false } = options;\n    if (restore) {\n      cldAsset.effect(\"e_gen_restore\");\n    }\n    return {};\n  }\n};\n\n// src/plugins/zoompan.ts\nimport { z as z24 } from \"zod\";\nvar zoompanProps = {\n  zoompan: z24.union([\n    z24.string(),\n    z24.boolean(),\n    z24.object({\n      loop: effects.loop.schema.optional(),\n      options: z24.string()\n    })\n  ]).describe(\n    JSON.stringify({\n      text: \"Applies zooming and/or panning to an image, resulting in a video or animated image.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#e_zoompan\"\n    })\n  ).optional()\n};\nvar zoompanPlugin = {\n  props: zoompanProps,\n  assetTypes: [\"image\", \"images\"],\n  plugin: ({ cldAsset, options }) => {\n    const { zoompan = false } = options;\n    const overrides = {\n      format: void 0\n    };\n    if (zoompan === true) {\n      cldAsset.effect(\"e_zoompan\");\n    } else if (typeof zoompan === \"string\") {\n      if (zoompan === \"loop\") {\n        cldAsset.effect(\"e_zoompan\");\n        cldAsset.effect(\"e_loop\");\n      } else {\n        cldAsset.effect(`e_zoompan:${zoompan}`);\n      }\n    } else if (typeof zoompan === \"object\") {\n      let zoompanEffect = \"e_zoompan\";\n      if (typeof zoompan.options === \"string\") {\n        zoompanEffect = `${zoompanEffect}:${zoompan.options}`;\n      }\n      cldAsset.effect(zoompanEffect);\n      let loopEffect;\n      if (zoompan.loop === true) {\n        loopEffect = \"e_loop\";\n      } else if (typeof zoompan.loop === \"string\" || typeof zoompan.loop === \"number\") {\n        loopEffect = `e_loop:${zoompan.loop}`;\n      }\n      if (loopEffect) {\n        cldAsset.effect(loopEffect);\n      }\n    }\n    if (zoompan !== false) {\n      overrides.format = \"auto:animated\";\n    }\n    return {\n      options: overrides\n    };\n  }\n};\n\n// src/types/image.ts\nvar imageOptionsSchema = assetOptionsSchema.merge(\n  z25.object({\n    // Spreading plugins instead of extend or merge to avoid excessive schema warning\n    // https://github.com/microsoft/TypeScript/issues/34933#issuecomment-**********\n    ...defaultImageProps,\n    ...enhanceProps,\n    ...extractProps,\n    ...fillBackgroundProps,\n    ...recolorProps,\n    ...removeProps,\n    ...replaceProps,\n    ...replaceBackgroundProps,\n    ...restoreProps,\n    ...zoompanProps\n  })\n);\n\n// src/types/video.ts\nimport { z as z27 } from \"zod\";\n\n// src/plugins/abr.ts\nimport { z as z26 } from \"zod\";\nvar abrProps = {\n  streamingProfile: z26.string().describe(\n    JSON.stringify({\n      text: \"The streaming profile to apply when delivering a video using adaptive bitrate streaming.\",\n      url: \"https://cloudinary.com/documentation/transformation_reference#sp_streaming_profile\"\n    })\n  ).optional()\n};\nvar abrPlugin = {\n  props: abrProps,\n  assetTypes: [\"video\", \"videos\"],\n  plugin: (settings) => {\n    const { cldAsset, options } = settings;\n    const { streamingProfile } = options;\n    if (typeof streamingProfile === \"string\") {\n      cldAsset.addTransformation(`sp_${streamingProfile}`);\n    }\n    return {};\n  }\n};\n\n// src/types/video.ts\nvar videoOptionsSchema = assetOptionsSchema.merge(\n  z27.object({\n    // Spreading plugins instead of extend or merge to avoid excessive schema warning\n    // https://github.com/microsoft/TypeScript/issues/34933#issuecomment-**********\n    ...abrProps\n  })\n);\n\n// src/lib/cloudinary.ts\nimport { objectHasKey as objectHasKey3, parseUrl } from \"@cloudinary-util/util\";\nimport {\n  Cloudinary\n} from \"@cloudinary/url-gen\";\nimport { z as z30 } from \"zod\";\n\n// src/types/analytics.ts\nimport { z as z28 } from \"zod\";\nvar analyticsOptionsSchema = z28.any();\n\n// src/types/config.ts\nimport { z as z29 } from \"zod\";\nvar configOptionsSchema = z29.any();\n\n// src/lib/cloudinary.ts\nvar transformationPlugins = [\n  // Some features *must* be the first transformation applied\n  // thus their plugins *must* come first in the chain\n  enhancePlugin,\n  extractPlugin,\n  recolorPlugin,\n  removeBackgroundPlugin,\n  removePlugin,\n  replacePlugin,\n  replaceBackgroundPlugin,\n  restorePlugin,\n  // Cropping needs to be before any other general transformations\n  // as it provides the option of 2-step resizing where someone\n  // can resize the \"base\" canvas as well as the final resize\n  // mechanism commonly used for responsive resizing\n  croppingPlugin,\n  // Raw transformations should always come before\n  // other arguments to avoid conflicting with\n  // added options via the component\n  preserveTransformationsPlugin,\n  rawTransformationsPlugin,\n  abrPlugin,\n  defaultImagePlugin,\n  effectsPlugin,\n  fillBackgroundPlugin,\n  flagsPlugin,\n  overlaysPlugin,\n  sanitizePlugin,\n  namedTransformationsPlugin,\n  seoPlugin,\n  underlaysPlugin,\n  versionPlugin,\n  zoompanPlugin\n];\nvar constructUrlOptionsSchema = z30.union([imageOptionsSchema, videoOptionsSchema]).describe(\n  JSON.stringify({\n    text: \"Asset options (Image or Video) that define delivery URL including public ID and transformations.\",\n    path: \"/url-loader/assetoptions\"\n  })\n);\nvar constructUrlPropsSchema = z30.object({\n  analytics: z30.union([analyticsOptionsSchema, z30.boolean()]).describe(\n    JSON.stringify({\n      text: \"Tech, dependency, and feature identifiers for tracking SDK usage related to Cloudinary.\",\n      path: \"/url-loader/analyticsoptions\"\n    })\n  ).optional(),\n  config: configOptionsSchema.describe(\n    JSON.stringify({\n      text: \"Configuration parameters for environment and Cloudinary account.\",\n      url: \"https://cloudinary.com/documentation/cloudinary_sdks#configuration_parameters\",\n      path: \"/url-loader/analyticsoptions\"\n    })\n  ).optional(),\n  options: constructUrlOptionsSchema\n});\nfunction constructCloudinaryUrl({\n  options,\n  config = {},\n  analytics\n}) {\n  if (analytics === false) {\n    if (typeof config?.url === \"undefined\") {\n      config.url = {};\n    }\n    config.url.analytics = false;\n  }\n  const cld = new Cloudinary(config);\n  if (typeof options?.src !== \"string\") {\n    throw Error(\n      `Failed to construct Cloudinary URL: Missing source (src) in options.`\n    );\n  }\n  if (!options?.assetType) {\n    options.assetType = \"image\";\n  }\n  const propsCheck = [];\n  transformationPlugins.forEach(({ props }) => {\n    const pluginProps = Object.keys(props);\n    pluginProps.forEach((prop) => {\n      if (propsCheck.includes(prop)) {\n        throw new Error(`Option ${prop} already exists!`);\n      }\n      propsCheck.push(prop);\n    });\n  });\n  const parsedOptions = {};\n  let publicId;\n  if (typeof options.src === \"string\" && /^https?:\\/\\//.test(options.src)) {\n    try {\n      const parts = parseUrl(options.src);\n      publicId = parts?.publicId;\n      parsedOptions.seoSuffix = parts?.seoSuffix;\n      parsedOptions.version = parts?.version;\n    } catch (e) {\n    }\n  }\n  if (!publicId) {\n    publicId = options.src;\n  }\n  Object.keys(parsedOptions).forEach(\n    (key) => {\n      if (objectHasKey3(options, key)) return;\n      options[key] = parsedOptions[key];\n    }\n  );\n  options.version ?? (options.version = 1);\n  let cldAsset = void 0;\n  if ([\"image\", \"images\"].includes(options.assetType)) {\n    cldAsset = cld.image(publicId);\n  } else if ([\"video\", \"videos\"].includes(options.assetType)) {\n    cldAsset = cld.video(publicId);\n  }\n  if (typeof cldAsset === \"undefined\") {\n    throw new Error(\"Invalid asset type.\");\n  }\n  const pluginEffects = {};\n  transformationPlugins.forEach(\n    ({ plugin, assetTypes, props, strict }) => {\n      const supportedAssetType = options?.assetType !== void 0 && assetTypes.includes(options.assetType);\n      const pluginProps = Object.keys(props);\n      const optionsKeys = Object.keys(options);\n      const attemptedUse = pluginProps.map((prop) => optionsKeys.includes(prop)).filter((isUsed) => !!isUsed).length > 0;\n      if (!supportedAssetType) {\n        if (attemptedUse) {\n          console.warn(\n            `One of the following props [${pluginProps.join(\n              \", \"\n            )}] was used with an unsupported asset type [${options?.assetType}]`\n          );\n        }\n        return;\n      }\n      if (options.strictTransformations && !strict) {\n        if (attemptedUse) {\n          console.warn(\n            `One of the following props [${pluginProps.join(\n              \", \"\n            )}] was used that is not supported with Strict Transformations.`\n          );\n        }\n        return;\n      }\n      const results = plugin({\n        cldAsset,\n        options\n      });\n      const { options: pluginOptions } = results || { options: void 0 };\n      Object.assign(pluginEffects, pluginOptions);\n    }\n  );\n  if (typeof pluginEffects.resize === \"string\") {\n    cldAsset.addTransformation(pluginEffects.resize);\n  }\n  cldAsset.setDeliveryType(options?.deliveryType || \"upload\");\n  if (!options.strictTransformations) {\n    if (options?.dpr) {\n      let dpr = options.dpr;\n      if (typeof dpr === \"number\") {\n        dpr = dpr.toFixed(1);\n      }\n      cldAsset.addTransformation(`dpr_${dpr}`);\n    }\n    const defaultFormat = options?.format === \"default\";\n    const rawContainsFormat = searchAssetRawTransformations(\"f_\", cldAsset, {\n      matchType: \"startsWith\"\n    });\n    const rawContainsFormatAndExplicit = rawContainsFormat && typeof options?.format !== \"undefined\";\n    if (pluginEffects?.format || !defaultFormat && (!rawContainsFormat || rawContainsFormatAndExplicit)) {\n      cldAsset.format(options?.format || pluginEffects?.format || \"auto\");\n    }\n    const defaultQuality = options?.quality === \"default\";\n    const rawContainsQuality = searchAssetRawTransformations(\"q_\", cldAsset, {\n      matchType: \"startsWith\"\n    });\n    const rawContainsQualityAndExplicit = rawContainsQuality && typeof options?.quality !== \"undefined\";\n    if (!defaultQuality && (!rawContainsQuality || rawContainsQualityAndExplicit)) {\n      cldAsset.quality(options?.quality || \"auto\");\n    }\n  }\n  return cldAsset.toURL({\n    trackedAnalytics: analytics\n  });\n}\nfunction searchAssetRawTransformations(query, asset, options) {\n  if (typeof asset.transformation === \"undefined\") return;\n  const { matchType = \"includes\" } = options || {};\n  const transformations = asset.transformation.actions.flatMap(\n    (transformation) => {\n      return transformation.toString().split(\"/\").flatMap((seg) => seg.split(\",\"));\n    }\n  );\n  const matches = transformations.filter((transformation) => {\n    if (matchType === \"startsWith\") {\n      return transformation.startsWith(query);\n    } else {\n      return transformation.includes(query);\n    }\n  });\n  return matches.length > 0;\n}\n\nexport {\n  primary,\n  position,\n  text,\n  effects,\n  assetOptionsSchema,\n  imageOptionsSchema,\n  videoOptionsSchema,\n  transformationPlugins,\n  constructUrlPropsSchema,\n  constructCloudinaryUrl\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAMA,8BAA8B;AAC9B;AACA;AAAA;AAq0EA;AA70EA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;;;AAMA,8BAA8B;AAC9B,IAAI,qBAAqB,CAAC;AAC1B,SAAS,oBAAoB;IAC3B,OAAO,IAAM;IACb,aAAa,IAAM;IACnB,sBAAsB,IAAM;IAC5B,MAAM,IAAM;IACZ,eAAe,IAAM;IACrB,aAAa,IAAM;IACnB,kBAAkB,IAAM;IACxB,OAAO,IAAM;IACb,WAAW,IAAM;IACjB,QAAQ,IAAM;IACd,SAAS,IAAM;IACf,QAAQ,IAAM;IACd,UAAU,IAAM;IAChB,QAAQ,IAAM;IACd,OAAO,IAAM;IACb,GAAG,IAAM;IACT,GAAG,IAAM;IACT,MAAM,IAAM;AACd;;AAEA,IAAI,gBAAgB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,IAAI,mBAAmB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAC5B;IACA;CACD;AACD,IAAI,YAAY,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,IAAI,QAAQ;IACV,WAAW;IACX,QAAQ,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAC,CAAC,MAAM;KAAG,EAAE,QAAQ,CAChD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,uBAAuB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAChC;IACA;IACA;IACA;IACA;CACD;AACD,IAAI,oBAAoB,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;IAC9B,iLAAA,CAAA,IAAC,CAAC,MAAM;IACR;IACA,iLAAA,CAAA,IAAC,CAAC,YAAY,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,CAAC;CAEtC;AACD,IAAI,cAAc;IAChB,WAAW;IACX,QAAQ,kBAAkB,QAAQ,CAChC,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,aAAa;AACjB,IAAI,OAAO;IACT,WAAW;IACX,QAAQ,WAAW,QAAQ,CACzB,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,oBAAoB;AACxB,IAAI,cAAc;IAChB,QAAQ,kBAAkB,OAAO,CAAC,WAAW,QAAQ,CACnD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,QAAQ;IACV,WAAW;IACX,QAAQ,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC;QAAW,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;KAAW,EAAE,QAAQ,CACvD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,SAAS;IACX,WAAW;IACX,cAAc;IACd,QAAQ,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CACzB,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,gBAAgB,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;IAC1B,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,+BAA+B;IAC/B,iLAAA,CAAA,IAAC,CAAC,YAAY,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,CAAC;CACtC;AACD,IAAI,UAAU;IACZ,WAAW;IACX,QAAQ,cAAc,QAAQ,CAC5B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,eAAe,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;IAAC,iLAAA,CAAA,IAAC,CAAC,MAAM;IAAI,iLAAA,CAAA,IAAC,CAAC,MAAM;CAAG;AACnD,IAAI,SAAS;IACX,WAAW;IACX,QAAQ,aAAa,QAAQ,CAC3B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,iBAAiB,iLAAA,CAAA,IAAC,CAAC,OAAO;AAC9B,IAAI,WAAW;IACb,QAAQ,eAAe,QAAQ,CAC7B,KAAK,SAAS,CAAC;QACb,MAAM;IACR;AAEJ;AACA,IAAI,SAAS;IACX,QAAQ,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CACzB,KAAK,SAAS,CAAC;QACb,MAAM;IACR;AAEJ;AACA,IAAI,cAAc,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;IAAC,iLAAA,CAAA,IAAC,CAAC,MAAM;IAAI,iLAAA,CAAA,IAAC,CAAC,MAAM;CAAG;AAClD,IAAI,QAAQ;IACV,WAAW;IACX,QAAQ,YAAY,QAAQ,CAC1B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,IAAI;IACN,WAAW;IACX,QAAQ,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAC,CAAC,MAAM;KAAG,EAAE,QAAQ,CAChD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,IAAI;IACN,WAAW;IACX,QAAQ,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAC,CAAC,MAAM;KAAG,EAAE,QAAQ,CAChD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AACA,IAAI,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;AACzB,IAAI,OAAO;IACT,QAAQ,WAAW,QAAQ,CACzB,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP;AAEJ;AAEA,8BAA8B;AAC9B,IAAI,mBAAmB;IACrB;QACE,MAAM,sNAAA,CAAA,iBAAc;QACpB,SAAS,sNAAA,CAAA,uBAAoB;IAC/B;CACD;AACD,IAAI,UAAU;IACZ;IACA;IACA;IACA;IACA;AACF;AACA,IAAI,WAAW;IACb;IACA;IACA;IACA;AACF;AACA,IAAI,OAAO;IACT,WAAW;QACT,WAAW;QACX,OAAO;IACT;IACA,WAAW;QACT,WAAW;IACb;IACA,QAAQ;QACN,WAAW;QACX,UAAU;IACZ;IACA,OAAO;QACL,WAAW;QACX,UAAU;QACV,YAAY;IACd;IACA,YAAY;QACV,WAAW;QACX,OAAO;IACT;IACA,UAAU;QACR,WAAW;QACX,OAAO;IACT;IACA,WAAW;QACT,WAAW;QACX,OAAO;IACT;IACA,YAAY;QACV,WAAW;QACX,OAAO;IACT;IACA,SAAS;QACP,WAAW;IACb;IACA,eAAe;QACb,WAAW;IACb;IACA,aAAa;QACX,WAAW;IACb;IACA,QAAQ;QACN,WAAW;QACX,OAAO;IACT;IACA,gBAAgB;QACd,WAAW;QACX,OAAO;IACT;AACF;AACA,IAAI,UAAU;IACZ;IACA,KAAK;QACH,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,gBAAgB;QACd,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,WAAW;QACT,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,cAAc;QACZ,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,kBAAkB;QAChB,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,YAAY;QACV,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,YAAY;QACV,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,MAAM;QACJ,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,WAAW;QACT,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,YAAY;QACV,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,QAAQ;QACN,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,YAAY;QACV,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,eAAe;QACb,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,YAAY;QACV,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,OAAO;QACL,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;QAEF,YAAY;IACd;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,SAAS;QACP,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,WAAW;QACT,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,OAAO;QACL,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,cAAc;QACZ,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,WAAW;QACT,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,OAAO,GAAG,QAAQ,CAC3B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,KAAK;QACH,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,SAAS;QACP,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,MAAM;QACJ,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,OAAO;YAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;SAAG,EAAE,QAAQ,CACjE,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,OAAO,GAAG,QAAQ,CAC3B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,QAAQ;QACN,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,OAAO;QACL,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,OAAO,GAAG,QAAQ,CAC3B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,SAAS;QACP,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;SAAG,EAAE,QAAQ,CACnD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,SAAS;QACP,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,eAAe;QACb,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,gBAAgB;QACd,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,QAAQ;QACN,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;SAAG,EAAE,QAAQ,CACnD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,QAAQ;QACN,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,cAAc;QACZ,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,YAAY;QACV,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,QAAQ;QACN,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,OAAO,GAAG,QAAQ,CAC3B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,OAAO;QACL,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,QAAQ;QACN,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,SAAS;QACP,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,OAAO;QACL,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CAC1B,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,oBAAoB;QAClB,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,MAAM;QACJ,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,MAAM;QACJ,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,aAAa;QACX,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,WAAW;QACT,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;IACA,UAAU;QACR,QAAQ;QACR,WAAW;QACX,QAAQ,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;YAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;YAAI,iLAAA,CAAA,IAAE,CAAC,OAAO;SAAG,EAAE,QAAQ,CACpD,KAAK,SAAS,CAAC;YACb,MAAM;YACN,KAAK;QACP;IAEJ;AACF;;;AAQA,6BAA6B;AAC7B,SAAS,wBAAwB,EAC/B,MAAM,EACN,SAAS,EACT,KAAK,EACL,UAAU,EACX;IACC,IAAI,iBAAiB;IACrB,IAAI,QAAQ;QACV,iBAAiB,GAAG,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,sBAAsB;IAC1B,YAAY,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;QACpC,IAAI,CAAC,KAAK,sBAAsB;QAChC,sBAAsB,QAAQ;IAChC;IACA,IAAI,wBAAwB,QAAQ,wBAAwB,QAAQ;QAClE,OAAO,GAAG,iBAAiB,WAAW;IACxC;IACA,IAAI,OAAO,wBAAwB,YAAY,OAAO,wBAAwB,UAAU;QACtF,IAAI,QAAQ;YACV,OAAO,GAAG,iBAAiB,UAAU,CAAC,EAAE,qBAAqB;QAC/D,OAAO;YACL,OAAO,GAAG,UAAU,CAAC,EAAE,qBAAqB;QAC9C;IACF;AACF;AACA,SAAS,oBAAoB,WAAW;IACtC,OAAO,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC;AACrC;AACA,SAAS,yBAAyB,KAAK;IACrC,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO,SAAS;AAClB;AAEA,0BAA0B;AAC1B,IAAI,mBAAmB;IAAC;IAAQ;IAAQ;IAAQ;IAAS;IAAY;CAAQ;AAC7E,IAAI,mBAAmB;IAAC;IAAQ;IAAQ;IAAQ;IAAS;IAAY;CAAQ;AAC7E,IAAI,gBAAgB;IAAC;IAAQ;CAAQ;AACrC,IAAI,eAAe;AACnB,IAAI,oBAAoB,iLAAA,CAAA,IAAE,CAAC,MAAM,CAAC;IAChC,aAAa,YAAY,MAAM,CAAC,QAAQ;IACxC,MAAM,KAAK,MAAM;IACjB,SAAS,QAAQ,MAAM,CAAC,QAAQ;IAChC,QAAQ,OAAO,MAAM,CAAC,QAAQ;IAC9B,OAAO,MAAM,MAAM,CAAC,QAAQ;IAC5B,GAAG,EAAE,MAAM,CAAC,QAAQ;IACpB,GAAG,EAAE,MAAM,CAAC,QAAQ;IACpB,MAAM,KAAK,MAAM,CAAC,QAAQ;IAC1B,QAAQ,iLAAA,CAAA,IAAE,CAAC,OAAO,GAAG,QAAQ;AAC/B;AACA,IAAI,gBAAgB;IAClB,aAAa,YAAY,MAAM,CAAC,QAAQ;IACxC,MAAM,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QACb,KAAK,MAAM;QACX;QACA,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;KACV,EAAE,OAAO,CAAC,cAAc,QAAQ;IACjC,SAAS,QAAQ,MAAM,CAAC,QAAQ;IAChC,MAAM,KAAK,MAAM,CAAC,QAAQ;AAC5B;AACA,IAAI,iBAAiB;IACnB,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,IAAI,QAAQ,EAAE;QACd,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,OAAO,QAAQ,IAAI,KAAK,aAAa;YAC3E,MAAM,IAAI,CAAC;gBACT,aAAa,QAAQ,WAAW;gBAChC,QAAQ,QAAQ,MAAM;gBACtB,SAAS,QAAQ,OAAO;gBACxB,MAAM,QAAQ,IAAI,IAAI;gBACtB,OAAO,QAAQ,KAAK;gBACpB,MAAM,QAAQ,IAAI;YACpB;QACF,OAAO,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,CAAC,MAAM,OAAO,CAAC,QAAQ,IAAI,GAAG;YAC3E,MAAM,IAAI,CAAC,QAAQ,IAAI;QACzB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,GAAG;YACtC,QAAQ,QAAQ,IAAI;QACtB;QACA,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,MAAM;YAClD,MAAM,IAAI,CAAC;gBACT,aAAa,QAAQ,WAAW;gBAChC,OAAO,QAAQ,KAAK;gBACpB,QAAQ,QAAQ,MAAM;gBACtB,SAAS,QAAQ,OAAO;gBACxB,MAAM;gBACN,MAAM,QAAQ,IAAI;YACpB;QACF;QACA,MAAM,uBAAuB,EAAE;QAC/B,MAAM,wBAAwB,EAAE;QAChC,KAAK,MAAM,SAAS,MAAO;YACzB,MAAM,iBAAiB;gBACrB,OAAO,MAAM,KAAK;gBAClB,QAAQ,MAAM,MAAM;YACtB;YACA,IAAI,OAAO,eAAe,KAAK,KAAK,eAAe,OAAO,MAAM,WAAW,KAAK,aAAa;gBAC3F,eAAe,KAAK,GAAG,QAAQ,KAAK;gBACpC,IAAI,OAAO,eAAe,MAAM,KAAK,aAAa;oBAChD,eAAe,MAAM,GAAG,QAAQ,MAAM;gBACxC;YACF;YACA,MAAM,kBAAkB,uBAAuB;gBAC7C,aAAa,MAAM,WAAW;gBAC9B,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI,IAAI;gBACpB,GAAG,MAAM,CAAC;gBACV,GAAG,MAAM,CAAC;gBACV,MAAM,MAAM,IAAI;gBAChB,GAAG,cAAc;YACnB;YACA,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,IAAI,MAAM,MAAM,KAAK,MAAM;oBACzB,sBAAsB,IAAI,CAAC;gBAC7B,OAAO;oBACL,qBAAqB,IAAI,CAAC;gBAC5B;YACF;QACF;QACA,sBAAsB,OAAO,CAAC,CAAC;YAC7B,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,SAAS,iBAAiB,CAAC,eAAe,IAAI,CAAC;YACjD;QACF;QACA,MAAM,UAAU;YACd,SAAS,CAAC;QACZ;QACA,IAAI,QAAQ,OAAO,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACtD,QAAQ,OAAO,CAAC,MAAM,GAAG,qBAAqB,GAAG,CAAC,CAAC,iBAAmB,eAAe,IAAI,CAAC,MAAM,IAAI,CAAC;QACvG;QACA,OAAO;IACT;AACF;AACA,SAAS,uBAAuB,cAAc;IAC5C,MAAM,EAAE,aAAa,YAAY,EAAE,MAAM,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,MAAM,KAAK,EAAE,GAAG;IAC9E,IAAI,WAAW,eAAe,OAAO;IACrC,MAAM,UAAU,yBAAyB,eAAe,MAAM;IAC9D,MAAM,SAAS,yBAAyB,eAAe,KAAK;IAC5D,MAAM,kBAAkB,EAAE;IAC1B,MAAM,uBAAuB,WAAW;IACxC,MAAM,sBAAsB,gBAAgB,iBAAiB,QAAQ,CAAC;IACtE,MAAM,iBAAiB,OAAO,OAAO,YAAY,OAAO,OAAO;IAC/D,MAAM,iBAAiB,OAAO,OAAO,YAAY,OAAO,OAAO;IAC/D,MAAM,wBAAwB,kBAAkB;IAChD,IAAI,SAAS,CAAC,wBAAwB,uBAAuB,qBAAqB,GAAG;QACnF,gBAAgB,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO;IACnC;IACA,IAAI,qBAAqB;QACvB,gBAAgB,IAAI,CAAC,CAAC,GAAG,EAAE,cAAc;IAC3C;IACA,IAAI,QAAQ;QACV,gBAAgB,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ;IACpC;IACA,IAAI,CAAC;QAAC;KAAQ,CAAC,QAAQ,CAAC,UAAU,OAAO,YAAY,UAAU;QAC7D,gBAAgB,IAAI,CAAC,CAAC,EAAE,EAAE,SAAS;IACrC;IACA,IAAI,gBAAgB;QAClB,gBAAgB,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI;IAChC;IACA,IAAI,gBAAgB;QAClB,gBAAgB,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI;IAChC;IACA,IAAI,CAAC,YAAY,iBAAiB,QAAQ,CAAC,UAAU,CAAC,uBAAuB;QAC3E,WAAW;IACb;IACA,IAAI,UAAU;QACZ,IAAI,aAAa,UAAU,CAAC,iBAAiB,QAAQ,CAAC,QAAQ;YAC5D,QAAQ,IAAI,CACV,CAAC,+CAA+C,EAAE,iBAAiB,IAAI,CACrE,MACA,uBAAuB,CAAC;QAE9B,OAAO;YACL,gBAAgB,IAAI,CAAC,CAAC,EAAE,EAAE,UAAU;QACtC;IACF;IACA,IAAI,OAAO;QACT,IAAI,UAAU,UAAU,CAAC,cAAc,QAAQ,CAAC,QAAQ;YACtD,QAAQ,IAAI,CACV,CAAC,uCAAuC,EAAE,cAAc,IAAI,CAC1D,MACA,oBAAoB,CAAC;QAE3B,OAAO;YACL,gBAAgB,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO;QACnC;IACF;IACA,OAAO;AACT;;AAIA,IAAI,cAAc;IAChB,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,QAAQ;IACpC,KAAK,QAAQ,GAAG,CAAC,MAAM,CAAC,QAAQ;IAChC,gBAAgB,QAAQ,cAAc,CAAC,MAAM,CAAC,QAAQ;IACtD,WAAW,QAAQ,SAAS,CAAC,MAAM,CAAC,QAAQ;IAC5C,cAAc,QAAQ,YAAY,CAAC,MAAM,CAAC,QAAQ;IAClD,kBAAkB,QAAQ,gBAAgB,CAAC,MAAM,CAAC,QAAQ;IAC1D,YAAY,QAAQ,UAAU,CAAC,MAAM,CAAC,QAAQ;IAC9C,YAAY,QAAQ,UAAU,CAAC,MAAM,CAAC,QAAQ;IAC9C,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ;IAClC,WAAW,QAAQ,SAAS,CAAC,MAAM,CAAC,QAAQ;IAC5C,YAAY,QAAQ,UAAU,CAAC,MAAM,CAAC,QAAQ;IAC9C,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAC,QAAQ;IACtC,YAAY,QAAQ,UAAU,CAAC,MAAM,CAAC,QAAQ;IAC9C,eAAe,QAAQ,aAAa,CAAC,MAAM,CAAC,QAAQ;IACpD,YAAY,QAAQ,UAAU,CAAC,MAAM,CAAC,QAAQ;IAC9C,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,QAAQ;IACpC,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAC1C,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAC1C,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ;IACxC,WAAW,QAAQ,SAAS,CAAC,MAAM,CAAC,QAAQ;IAC5C,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,QAAQ;IACpC,cAAc,QAAQ,YAAY,CAAC,MAAM,CAAC,QAAQ;IAClD,WAAW,QAAQ,SAAS,CAAC,MAAM,CAAC,QAAQ;IAC5C,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ;IACxC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ;IAClC,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAC1C,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAC,QAAQ;IACtC,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAC1C,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ;IACxC,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ;IACxC,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAC1C,eAAe,QAAQ,aAAa,CAAC,MAAM,CAAC,QAAQ;IACpD,gBAAgB,QAAQ,cAAc,CAAC,MAAM,CAAC,QAAQ;IACtD,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAC,QAAQ;IACtC,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAC,QAAQ;IACtC,cAAc,QAAQ,YAAY,CAAC,MAAM,CAAC,QAAQ;IAClD,YAAY,QAAQ,UAAU,CAAC,MAAM,CAAC,QAAQ;IAC9C,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAC,QAAQ;IACtC,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,QAAQ;IACpC,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAC,QAAQ;IACtC,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ;IACxC,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,QAAQ;IACpC,oBAAoB,QAAQ,kBAAkB,CAAC,MAAM,CAAC,QAAQ;IAC9D,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ;IAClC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ;IAClC,aAAa,QAAQ,WAAW,CAAC,MAAM,CAAC,QAAQ;IAChD,WAAW,QAAQ,SAAS,CAAC,MAAM,CAAC,QAAQ;IAC5C,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAC1C,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;AAC5C;AACA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAE,CAAC,MAAM,CAAC,cAAc,QAAQ,CAChD,KAAK,SAAS,CAAC;QACb,MAAM;IACR,IACA,QAAQ;IACV,GAAG,WAAW;AAChB;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,wBAAwB,8BAA8B;YAC1D;YACA;QACF;QACA,sBAAsB,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,iBAAmB,SAAS,MAAM,CAAC;QACrF,IAAI,MAAM,OAAO,CAAC,SAAS,UAAU;YACnC,SAAS,QAAQ,QAAQ,CAAC;gBACxB,MAAM,uBAAuB,8BAA8B;oBACzD;oBACA,SAAS;gBACX,GAAG,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC3B,SAAS,MAAM,CAAC;YAClB;QACF;QACA,SAAS,8BAA8B,EACrC,SAAS,QAAQ,EACjB,SAAS,QAAQ,EAClB;YACC,OAAO,OAAO,IAAI,CAAC,UAAU,GAAG,CAC9B,CAAC;gBACC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,IAAI;gBACvD,OAAO,wBAAwB;oBAC7B;oBACA;oBACA,OAAO,UAAU,CAAC,IAAI;oBACtB;gBACF;YACF;QAEJ;QACA,OAAO,CAAC;IACV;AACF;AAEA,uBAAuB;AACvB,IAAI,EAAE,WAAW,UAAU,EAAE,GAAG;AAChC,IAAI,aAAa;IACf,OAAO,MAAM,MAAM,CAAC,QAAQ;AAC9B;AACA,IAAI,cAAc;IAChB,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,OAAO,SAAS,EAAE,EAAE,GAAG;QAC/B,IAAI,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,GAAG,GAAG;YAC9C,OAAO,OAAO,CAAC,CAAC;gBACd,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,SAAS,CAAC;gBACzC,IAAI,CAAC,SAAS;oBACZ,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,eAAe,CAAC;oBACpD;oBACA;gBACF;gBACA,SAAS,OAAO,CAAC;YACnB;QACF,OAAO,IAAI,OAAO,WAAW,UAAU;YACrC,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,WAAW,MAAM;gBAChD,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,SAAS,CAAC;gBACzC,IAAI,CAAC,SAAS;oBACZ,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,UAAU,eAAe,CAAC;oBACzD;oBACA;gBACF;gBACA,SAAS,iBAAiB,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE,OAAO;YACvD;QACF;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,4BAA4B,iLAAA,CAAA,IAAE,CAAC,MAAM;AACzC,IAAI,4BAA4B;IAC9B,sBAAsB,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC;QAA2B,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;KAA2B,EAAE,QAAQ,CACvG,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV;;GAEC,GACD,iBAAiB,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC;QAA2B,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;KAA2B,EAAE,QAAQ,CAClG,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,6BAA6B;IAC/B,OAAO;IACP,QAAQ;IACR,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,eAAe,EAAE,oBAAoB,EAAE,GAAG;QAClD,IAAI,mBAAmB,QAAQ,GAAG,CAAC,gBAAgB,KAAK,eAAe;YACrE,QAAQ,IAAI,CACV;QAEJ;QACA,IAAI,wBAAwB,wBAAwB,mBAAmB,EAAE;QACzE,IAAI,CAAC,MAAM,OAAO,CAAC,wBAAwB;YACzC,wBAAwB;gBAAC;aAAsB;QACjD;QACA,sBAAsB,OAAO,CAAC,CAAC;YAC7B,SAAS,iBAAiB,CAAC,CAAC,EAAE,EAAE,gBAAgB;QAClD;QACA,OAAO,CAAC;IACV;AACF;;;AAKA,IAAI,oBAAoB,iLAAA,CAAA,IAAE,CAAC,MAAM,CAAC;IAChC,WAAW,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAC/B,WAAW,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAC/B,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAC5B,OAAO,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAC3B,YAAY,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAC9B,WAAW,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;KAAG,EAAE,QAAQ;IACxD,YAAY,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;KAAG,EAAE,QAAQ;IACtD,eAAe,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;KAAG,EAAE,QAAQ;IAC5D,aAAa,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAE,CAAC,MAAM;KAAG,EAAE,QAAQ;IAC1D,QAAQ,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAC5B,MAAM,iLAAA,CAAA,IAAE,CAAC,MAAM;AAEjB;AACA,IAAI,wBAAwB,iLAAA,CAAA,IAAE,CAAC,MAAM,CAAC;IACpC,OAAO,MAAM,MAAM,CAAC,QAAQ;IAC5B,SAAS,QAAQ,MAAM,CAAC,QAAQ;IAChC,GAAG,EAAE,MAAM,CAAC,QAAQ;IACpB,GAAG,EAAE,MAAM,CAAC,QAAQ;AACtB;AACA,IAAI,gBAAgB,iLAAA,CAAA,IAAE,CAAC,MAAM,CAAC;IAC5B,gBAAgB,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAE,CAAC,MAAM,CAAC,CAAC,IAAI,QAAQ;IAChD,cAAc,MAAM,MAAM,CAAC,QAAQ;IACnC,SAAS,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAE,CAAC,MAAM,CAAC,CAAC,IAAI,QAAQ;IACzC,MAAM,KAAK,MAAM,CAAC,QAAQ;IAC1B,OAAO,MAAM,MAAM,CAAC,QAAQ;IAC5B,QAAQ,OAAO,MAAM,CAAC,QAAQ;IAC9B,UAAU,sBAAsB,QAAQ;IACxC,UAAU,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IAC9B,MAAM,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAE,CAAC,MAAM;QAAI;KAAkB,EAAE,QAAQ;IACzD,KAAK,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ;IACzB,OAAO,MAAM,MAAM,CAAC,QAAQ;AAC9B;AACA,IAAI,uBAAuB;IACzB,OAAO;IACP,YAAY;IACZ,UAAU;IACV,YAAY;AACd;AACA,IAAI,gBAAgB;IAClB,SAAS,cAAc,QAAQ,CAC7B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,UAAU,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC,eAAe,QAAQ,CACxC,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,MAAM,iLAAA,CAAA,IAAE,CAAC,MAAM,GAAG,QAAQ,CACxB,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,iBAAiB;IACnB,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,MAAM,KAAK,EAAE,WAAW,EAAE,EAAE,GAAG;QACvC,MAAM,OAAO;QACb,MAAM,gBAAgB;QACtB,IAAI,MAAM,OAAO,CAAC,WAAW;YAC3B,SAAS,OAAO,CAAC;QACnB;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,aAAa;gBACX,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,sBAAsB;oBAC5C,MAAM;gBACR;YACF;QACF,OAAO,IAAI,OAAO,UAAU,UAAU;YACpC,aAAa;gBACX,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,sBAAsB;YAChD;QACF;QACA,SAAS,aAAa,EACpB,QAAQ,EACR,GAAG,EACH,UAAU,SAAS,EACnB,MAAM,KAAK,EACX,SAAS,eAAe,EAAE,EAC1B,iBAAiB,EAAE,EACnB,OAAO,aAAa,EAAE,EACtB,eAAe,EAAE,EACjB,GAAG,UACJ;YACC,MAAM,cAAc,OAAO,aAAa;YACxC,MAAM,SAAS,OAAO,QAAQ;YAC9B,MAAM,UAAU,OAAO,UAAU,YAAY,OAAO,UAAU;YAC9D,MAAM,cAAc,OAAO,cAAc;YACzC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS;gBACvC,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,mCAAmC,CAAC;gBAC5D;YACF;YACA,IAAI;YACJ,IAAI,SAAS;gBACX,sBAAsB,GAAG,cAAc,KAAK,CAAC;YAC/C,OAAO,IAAI,aAAa;gBACtB,sBAAsB,GAAG,cAAc,CAAC,EAAE,SAAS,OAAO,CACxD,OACA,MACC;YACL,OAAO,IAAI,QAAQ;gBACjB,sBAAsB,GAAG,cAAc,OAAO,EAAE,CAAA,GAAA,sNAAA,CAAA,eAAY,AAAD,EAAE,MAAM;YACrE;YACA,MAAM,WAAW,EAAE;YACnB,MAAM,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAC;gBAC7B,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM;gBACjC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI;gBAC9C,MAAM,iBAAiB,wBAAwB;oBAC7C;oBACA,OAAO,QAAQ,CAAC,IAAI;oBACpB;gBACF;gBACA,IAAI,gBAAgB;oBAClB,SAAS,IAAI,CAAC;gBAChB;YACF;YACA,aAAa,OAAO,CAAC,CAAC;gBACpB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC;oBAC3B,MAAM,kBAAkB,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI;oBACrE,IAAI,CAAC,iBAAiB;oBACtB,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;oBAC1C,MAAM,iBAAiB,wBAAwB;wBAC7C;wBACA;wBACA,OAAO,MAAM,CAAC,IAAI;wBAClB;oBACF;oBACA,IAAI,gBAAgB;wBAClB,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF;YACA,eAAe,OAAO,CAAC,CAAC;gBACtB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC;oBAC3B,MAAM,kBAAkB,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI;oBACrE,IAAI,CAAC,iBAAiB;oBACtB,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;oBAC1C,MAAM,iBAAiB,wBAAwB;wBAC7C;wBACA;wBACA,OAAO,MAAM,CAAC,IAAI;wBAClB;oBACF;oBACA,IAAI,gBAAgB;wBAClB,QAAQ,IAAI,CAAC;oBACf;gBACF;YACF;YACA,MAAM,mBAAmB,MAAM,OAAO,CAAC,cAAc,aAAa;gBAAC;aAAW;YAC9E,iBAAiB,OAAO,CAAC,CAAC;gBACxB,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,SAAS,CAAC;gBACxC,IAAI,CAAC,SAAS;oBACZ,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,eAAe,CAAC;oBACpD;oBACA;gBACF;gBACA,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM;YAC5B;YACA,MAAM,qBAAqB,MAAM,OAAO,CAAC,gBAAgB,eAAe;gBAAC;aAAa;YACtF,mBAAmB,OAAO,CAAC,CAAC;gBAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,SAAS,CAAC;gBACxC,IAAI,CAAC,SAAS;oBACZ,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,eAAe,CAAC;oBACpD;oBACA;gBACF;gBACA,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM;YAC3B;YACA,IAAI,SAAS;gBACX,IAAI,OAAO,UAAU,UAAU;oBAC7B,QAAQ;wBACN,GAAG,oBAAoB;wBACvB,MAAM;oBACR;gBACF;gBACA,MAAM,sBAAsB,EAAE;gBAC9B,IAAI,OAAO,UAAU,UAAU;oBAC7B,MAAM,cAAc,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,MAAQ,CAAA,GAAA,sNAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,GAAG,CAAC,CAAC;wBACnF,MAAM,QAAQ,SAAS,CAAA,GAAA,sNAAA,CAAA,eAAY,AAAD,EAAE,OAAO,QAAQ,KAAK,CAAC,IAAI;wBAC7D,OAAO;4BACL,GAAG,IAAI,CAAC,IAAI;4BACZ;4BACA;4BACA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;wBAC5B;oBACF;oBACA,MAAM,oBAAoB,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE,aAAa;oBACjD,KAAK,MAAM,cAAc,kBAAmB;wBAC1C,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;wBACxD,IAAI,YAAY;wBAChB,YAAY,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;4BACpC,IAAI,CAAC,KAAK,QAAQ;4BAClB,YAAY,QAAQ;wBACtB;wBACA,IAAI,aAAa,WAAW;4BAC1B,SAAS,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,WAAW;wBAC3C,OAAO,IAAI,cAAc,QAAQ;4BAC/B,oBAAoB,IAAI,CAAC;wBAC3B,OAAO,IAAI,WAAW;4BACpB,oBAAoB,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,WAAW;wBACtD,OAAO;4BACL,oBAAoB,IAAI,CAAC;wBAC3B;oBACF;gBACF;gBACA,MAAM,oBAAoB;oBACxB,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBACA,IAAI,YAAY,OAAO,QAAQ;gBAC/B,IAAI,OAAO,cAAc,UAAU;oBACjC,OAAO,IAAI,CAAC,oBAAoB,QAAQ,CAAC;wBACvC,YAAY,WAAW,QACrB,WACA,iBAAiB,CAAC,UAAU;oBAEhC;gBACF;gBACA,sBAAsB,GAAG,oBAAoB,CAAC,EAAE,oBAAoB,IAAI,CACtE,KACA,CAAC,EAAE,WAAW;YAClB;YACA,IAAI,aAAa;gBACf,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAC;oBAC9B,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,eAAY,AAAD,EAAE,UAAU,QAAQ,CAAC,CAAA,GAAA,sNAAA,CAAA,eAAY,AAAD,EAAE,WAAW,MAC3D;oBACF,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,IAAI;oBAC/C,MAAM,iBAAiB,wBAAwB;wBAC7C;wBACA,OAAO,SAAS,CAAC,IAAI;wBACrB;oBACF;oBACA,IAAI,gBAAgB;wBAClB,QAAQ,IAAI,CAAC;oBACf;gBACF;YACF;YACA,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,sBAAsB,GAAG,oBAAoB,CAAC,EAAE,SAAS,IAAI,CAAC,MAAM;YACtE;YACA,sBAAsB,GAAG,oBAAoB,8BAA8B,CAAC;YAC5E,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,sBAAsB,GAAG,oBAAoB,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM;YACrE;YACA,SAAS,iBAAiB,CAAC;QAC7B;QACA,OAAO,CAAC;IACV;AACF;;;AAKA,IAAI,+BAA+B;IACjC,yBAAyB,iLAAA,CAAA,IAAE,CAAC,OAAO,GAAG,QAAQ,CAC5C,KAAK,SAAS,CAAC;QACb,MAAM;IACR,IACA,QAAQ;AACZ;AACA,IAAI,gCAAgC;IAClC,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,0BAA0B,KAAK,EAAE,GAAG;QAC5C,IAAI,yBAAyB;YAC3B,IAAI;gBACF,MAAM,kBAAkB,CAAA,GAAA,sNAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC;gBAC1E,gBAAgB,IAAI,GAAG,OAAO,CAAC,CAAC;oBAC9B,SAAS,iBAAiB,CAAC;gBAC7B;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,IAAI,CAAC,CAAC,oCAAoC,EAAE,EAAE,OAAO,EAAE;YACjE;QACF;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,0BAA0B,iLAAA,CAAA,IAAE,CAAC,MAAM;AACvC,IAAI,0BAA0B;IAC5B,oBAAoB,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;QAAC;QAAyB,iLAAA,CAAA,IAAE,CAAC,KAAK,CAAC;KAAyB,EAAE,QAAQ,CACjG,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,2BAA2B;IAC7B,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,IAAI,EAAE,qBAAqB,EAAE,EAAE,GAAG;QAClC,IAAI,CAAC,MAAM,OAAO,CAAC,qBAAqB;YACtC,qBAAqB;gBAAC;aAAmB;QAC3C;QACA,mBAAmB,OAAO,CAAC,CAAC;YAC1B,SAAS,iBAAiB,CAAC;QAC7B;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,wBAAwB;IAC1B,kBAAkB,iLAAA,CAAA,IAAE,CAAC,OAAO,GAAG,QAAQ,CACrC,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,yBAAyB;IAC3B,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,mBAAmB,KAAK,EAAE,GAAG;QACrC,IAAI,kBAAkB;YACpB,SAAS,MAAM,CAAC;QAClB;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,gBAAgB;IAClB,UAAU,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ,CAC9B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,iBAAiB;IACnB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,WAAW,IAAI,EAAE,GAAG;QAC5B,MAAM,uBAAuB,YAAY,CAAC,QAAQ,MAAM,KAAK,SAAS,SAAS,QAAQ,CAAC,QAAQ,CAAC,OAAO;QACxG,IAAI,sBAAsB;YACxB,SAAS,MAAM,CAAC;QAClB;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,WAAW;IACb,WAAW,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ,CAC9B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,YAAY;IACd,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,SAAS,EAAE,GAAG;QACtB,IAAI,OAAO,cAAc,UAAU;YACjC,IAAI,QAAQ,YAAY,KAAK,SAAS;gBACpC,QAAQ,IAAI,CACV;YAEJ,OAAO;gBACL,SAAS,SAAS,CAAC;YACrB;QACF;QACA,OAAO,CAAC;IACV;AACF;;;AAKA,IAAI,yBAAyB,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IACtC,OAAO,MAAM,MAAM,CAAC,QAAQ;IAC5B,SAAS,QAAQ,MAAM,CAAC,QAAQ;IAChC,GAAG,EAAE,MAAM,CAAC,QAAQ;IACpB,GAAG,EAAE,MAAM,CAAC,QAAQ;AACtB;AACA,IAAI,iBAAiB,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IAC9B,gBAAgB,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC,CAAC,IAAI,QAAQ;IAClD,cAAc,MAAM,MAAM,CAAC,QAAQ;IACnC,SAAS,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC,CAAC,IAAI,QAAQ;IAC3C,MAAM,KAAK,MAAM,CAAC,QAAQ;IAC1B,OAAO,MAAM,MAAM,CAAC,QAAQ;IAC5B,QAAQ,OAAO,MAAM,CAAC,QAAQ;IAC9B,UAAU,uBAAuB,QAAQ;IACzC,UAAU,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ;IAC/B,MAAM,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ;IAC3B,KAAK,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ;IAC1B,OAAO,MAAM,MAAM,CAAC,QAAQ;AAC9B;AACA,IAAI,iBAAiB;IACnB,UAAU,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ,CAC7B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,WAAW,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,gBAAgB,QAAQ,CAC3C,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,kBAAkB;IACpB,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG;QACrC,MAAM,gBAAgB;QACtB,IAAI,MAAM,OAAO,CAAC,YAAY;YAC5B,UAAU,OAAO,CAAC;QACpB;QACA,IAAI,OAAO,aAAa,UAAU;YAChC,MAAM,kBAAkB;gBACtB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,OAAO;oBAAC;iBAAW;YACrB;YACA,cAAc;QAChB;QACA,SAAS,cAAc,EACrB,QAAQ,EACR,IAAI,EACJ,UAAU,SAAS,EACnB,SAAS,eAAe,EAAE,EAC1B,OAAO,aAAa,EAAE,EACtB,eAAe,EAAE,EACjB,GAAG,UACJ;YACC,MAAM,cAAc,OAAO,aAAa;YACxC,MAAM,cAAc,OAAO,cAAc;YACzC,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,uBAAuB,CAAC;gBAChD;YACF;YACA,IAAI,sBAAsB,GAAG,cAAc,CAAC,EAAE,SAAS,OAAO,CAC5D,OACA,MACC;YACH,MAAM,WAAW,EAAE;YACnB,MAAM,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAC;gBAC7B,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,eAAa,AAAD,EAAE,SAAS,MAAM;gBAClC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,IAAI;gBAClC,SAAS,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE;YAC/C;YACA,aAAa,OAAO,CAAC,CAAC;gBACpB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC;oBAC3B,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,eAAa,AAAD,EAAE,SAAS,MAAM;oBAClC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,IAAI;oBAClC,SAAS,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE;gBAC7C;YACF;YACA,IAAI,aAAa;gBACf,OAAO,IAAI,CAAC,WAAW,OAAO,CAC5B,CAAC;oBACC,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,eAAa,AAAD,EAAE,UAAU,MAAM;oBACnC,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC,IAAI;oBACnC,QAAQ,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,SAAS,CAAC,IAAI,EAAE;gBAC/C;YAEJ;YACA,MAAM,mBAAmB,MAAM,OAAO,CAAC,cAAc,aAAa;gBAAC;aAAW;YAC9E,iBAAiB,OAAO,CAAC,CAAC;gBACxB,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,SAAS,CAAC;gBACxC,IAAI,CAAC,SAAS;oBACZ,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,eAAe,CAAC;oBACpD;oBACA;gBACF;gBACA,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM;YAC5B;YACA,MAAM,qBAAqB,MAAM,OAAO,CAAC,gBAAgB,eAAe;gBAAC;aAAa;YACtF,mBAAmB,OAAO,CAAC,CAAC;gBAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,SAAS,CAAC;gBACxC,IAAI,CAAC,SAAS;oBACZ,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,eAAe,CAAC;oBACpD;oBACA;gBACF;gBACA,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM;YAC3B;YACA,sBAAsB,GAAG,oBAAoB,CAAC,EAAE,SAAS,IAAI,CAAC,MAAM;YACpE,sBAAsB,GAAG,oBAAoB,8BAA8B,CAAC;YAC5E,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,sBAAsB,GAAG,oBAAoB,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM;YACrE;YACA,SAAS,iBAAiB,CAAC;QAC7B;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAG,CAAC,MAAM;KAAG,EAAE,QAAQ,CACvD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;QAAU;QAAS;KAAS;IAClD,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,OAAO,EAAE,GAAG;QACpB,IAAI,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU;YAC9D,SAAS,UAAU,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK;QAChD;QACA,OAAO,CAAC;IACV;AACF;AAEA,qBAAqB;AACrB,IAAI,qBAAqB,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IAClC,WAAW,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,QAAQ,CAC/C,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,cAAc,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,QAAQ,CACnD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,KAAK,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAG,CAAC,MAAM;KAAG,EAAE,QAAQ,CACnD,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,QAAQ,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,QAAQ,CAC3C,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,QAAQ,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAG,CAAC,MAAM;KAAG,EAAE,QAAQ,CACtD,KAAK,SAAS,CAAC;QACb,MAAM;IACR,IACA,QAAQ;IACV,SAAS,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAG,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAG,CAAC,MAAM;KAAG,EAAE,OAAO,CAAC,QAAQ,QAAQ,CACrF,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,KAAK,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ,CACxB,KAAK,SAAS,CAAC;QACb,MAAM;IACR;IAEF,uBAAuB,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ,CAC3C,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;IACV,OAAO,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;QAAI,iLAAA,CAAA,IAAG,CAAC,MAAM;KAAG,EAAE,QAAQ,CACrD,KAAK,SAAS,CAAC;QACb,MAAM;IACR,IACA,QAAQ;IACV,iFAAiF;IACjF,+EAA+E;IAC/E,GAAG,aAAa;IAChB,GAAG,YAAY;IACf,GAAG,UAAU;IACb,GAAG,yBAAyB;IAC5B,GAAG,aAAa;IAChB,GAAG,4BAA4B;IAC/B,GAAG,uBAAuB;IAC1B,GAAG,qBAAqB;IACxB,GAAG,aAAa;IAChB,GAAG,QAAQ;IACX,GAAG,cAAc;IACjB,GAAG,YAAY;AACjB;;;;AAQA,IAAI,oBAAoB;IACtB,cAAc,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ,CACjC,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,qBAAqB;IACvB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,YAAY,EAAE,GAAG;QACzB,IAAI,OAAO,iBAAiB,UAAU;YACpC,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE,eAAe;gBAC5B,QAAQ,IAAI,CACV,CAAC,6GAA6G,CAAC;YAEnH;YACA,MAAM,iBAAiB,aAAa,OAAO,CAAC,OAAO;YACnD,SAAS,iBAAiB,CAAC,CAAC,EAAE,EAAE,gBAAgB;QAClD;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ,CAC7B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,UAAU,KAAK,EAAE,GAAG;QAC5B,IAAI,SAAS;YACX,SAAS,MAAM,CAAC;QAClB;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QACjB,OAAO,MAAM,CAAC,QAAQ;QACtB,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,OAAO,MAAM,EAAE,QAAQ;QACjC,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;YACT,QAAQ,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,QAAQ;YAC7C,MAAM,YAAY,MAAM,CAAC,QAAQ;YACjC,UAAU,SAAS,MAAM,CAAC,OAAO,CAAC,OAAO,QAAQ;YACjD,QAAQ,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;gBAAC,OAAO,MAAM;gBAAE,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,OAAO,MAAM;aAAE,EAAE,QAAQ;QACvE;KACD,EAAE,QAAQ,CACT,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,OAAO,EAAE,GAAG;QACpB,IAAI,CAAC,WAAW,OAAO,YAAY,aAAa,OAAO,CAAC;QACxD,MAAM,aAAa,EAAE;QACrB,IAAI,OAAO,YAAY,UAAU;YAC/B,WAAW,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS;QACrC,OAAO,IAAI,MAAM,OAAO,CAAC,UAAU;YACjC,WAAW,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,UAAU;QACpD,OAAO,IAAI,OAAO,YAAY,YAAY,CAAC,MAAM,OAAO,CAAC,UAAU;YACjE,MAAM,UAAU,cAAc,QAAQ,MAAM;YAC5C,IAAI,SAAS;gBACX,WAAW,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS;YACrC;YACA,IAAI,QAAQ,MAAM,KAAK,MAAM;gBAC3B,WAAW,IAAI,CAAC;YAClB;YACA,IAAI,OAAO,QAAQ,IAAI,KAAK,UAAU;gBACpC,WAAW,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,IAAI,EAAE;YACxC;YACA,IAAI,QAAQ,QAAQ,KAAK,MAAM;gBAC7B,WAAW,IAAI,CAAC;YAClB;QACF;QACA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,iBAAiB,CAAC,UAAU,EAAE,WAAW,IAAI,CAAC,MAAM;YAC1D,SAAS,iBAAiB,CAAC;QAC7B;QACA,OAAO,CAAC;IACV;AACF;AACA,SAAS,cAAc,OAAO;IAC5B,IAAI,OAAO,YAAY,UAAU,OAAO;IACxC,IAAI,MAAM,OAAO,CAAC,UAAU;QAC1B,OAAO,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,UAAY,OAAO,YAAY,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC;IAClF;IACA,OAAO,KAAK;AACd;;AAIA,IAAI,cAAc;AAClB,IAAI,sBAAsB;IACxB,gBAAgB,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QACxB,iLAAA,CAAA,IAAG,CAAC,OAAO;QACX,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;YACT,MAAM,KAAK,MAAM,CAAC,QAAQ;YAC1B,SAAS,QAAQ,MAAM,CAAC,QAAQ;YAChC,QAAQ,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ;QAC/B;KACD,EAAE,QAAQ,CACT,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,uBAAuB;IACzB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,IAAI,OAAO,mBAAmB,aAAa,OAAO,CAAC;QACnD,MAAM,SAAS,yBAAyB,QAAQ,KAAK;QACrD,MAAM,UAAU,yBAAyB,QAAQ,MAAM;QACvD,MAAM,uBAAuB,OAAO,YAAY,YAAY,OAAO,WAAW;QAC9E,IAAI,eAAe,QAAQ,WAAW;QACtC,IAAI,CAAC,gBAAgB,sBAAsB;YACzC,eAAe,GAAG,OAAO,CAAC,EAAE,SAAS;QACvC;QACA,IAAI,CAAC,cAAc;YACjB,wCAA4C;gBAC1C,QAAQ,IAAI,CACV,CAAC,sIAAsI,CAAC;YAE5I;YACA,OAAO,CAAC;QACV;QACA,IAAI,mBAAmB,MAAM;YAC3B,MAAM,aAAa;gBACjB;gBACA,CAAC,GAAG,EAAE,cAAc;gBACpB,CAAC,EAAE,EAAE,aAAa;aACnB;YACD,SAAS,iBAAiB,CAAC,WAAW,IAAI,CAAC;QAC7C,OAAO,IAAI,OAAO,mBAAmB,UAAU;YAC7C,MAAM,EAAE,MAAM,QAAQ,WAAW,EAAE,SAAS,QAAQ,EAAE,QAAQ,OAAO,EAAE,GAAG;YAC1E,MAAM,aAAa;gBAAC,CAAC,GAAG,EAAE,cAAc;gBAAE,CAAC,EAAE,EAAE,OAAO;aAAC;YACvD,IAAI,OAAO,YAAY,UAAU;gBAC/B,WAAW,OAAO,CAAC,CAAC,WAAW,EAAE,SAAS;YAC5C,OAAO;gBACL,WAAW,OAAO,CAAC,CAAC,UAAU,CAAC;YACjC;YACA,IAAI,OAAO,aAAa,UAAU;gBAChC,WAAW,IAAI,CAAC,CAAC,EAAE,EAAE,UAAU;YACjC;YACA,SAAS,iBAAiB,CAAC,WAAW,IAAI,CAAC;QAC7C;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,kCAAkC,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;IAC9C,iLAAA,CAAA,IAAG,CAAC,MAAM;IACV,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;CACrB;AACD,IAAI,4BAA4B,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IACzC,QAAQ,gCAAgC,QAAQ;IAChD,IAAI,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ;IACzB,UAAU,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ;AAClC;AACA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC;QAAiC;KAA0B,EAAE,QAAQ,CACvF,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,OAAO,EAAE,GAAG;QACpB,MAAM,iBAAiB;YACrB,QAAQ,KAAK;YACb,YAAY,KAAK;YACjB,UAAU,KAAK;QACjB;QACA,IAAI,MAAM,OAAO,CAAC,UAAU;YAC1B,IAAI,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG;gBAC7B,eAAe,MAAM,GAAG,oBAAoB,OAAO,CAAC,EAAE;YACxD,OAAO;gBACL,eAAe,MAAM,GAAG,OAAO,CAAC,EAAE;YACpC;YACA,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,UAAU;gBAClC,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC,EAAE;YACzC;QACF,OAAO,IAAI,OAAO,YAAY,UAAU;YACtC,IAAI,OAAO,QAAQ,MAAM,KAAK,UAAU;gBACtC,eAAe,MAAM,GAAG,QAAQ,MAAM;YACxC,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,GAAG;gBACxC,eAAe,MAAM,GAAG,oBAAoB,QAAQ,MAAM;YAC5D;YACA,IAAI,OAAO,QAAQ,EAAE,KAAK,UAAU;gBAClC,cAAc,CAAC,WAAW,GAAG,QAAQ,EAAE;YACzC;YACA,IAAI,QAAQ,QAAQ,KAAK,MAAM;gBAC7B,eAAe,QAAQ,GAAG,CAAC,IAAI,CAAC;YAClC;QACF;QACA,MAAM,iBAAiB,OAAO,OAAO,CAAC,gBAAgB,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC;QACnI,IAAI,gBAAgB;YAClB,SAAS,iBAAiB,CAAC,CAAC,cAAc,EAAE,gBAAgB;QAC9D;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,iCAAiC,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;IAC7C,iLAAA,CAAA,IAAG,CAAC,MAAM;IACV,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;CACrB;AACD,IAAI,2BAA2B,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IACxC,QAAQ,+BAA+B,QAAQ;IAC/C,QAAQ,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;QAAK,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;KAAK,EAAE,QAAQ;IACzF,UAAU,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ;IAChC,cAAc,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ;AACtC;AACA,IAAI,cAAc;IAChB,QAAQ,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC;QAAgC;KAAyB,EAAE,QAAQ,CACpF,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,eAAe;IACjB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,gBAAgB;YACpB,QAAQ,KAAK;YACb,QAAQ,KAAK;YACb,UAAU,KAAK;YACf,iBAAiB,KAAK;QACxB;QACA,IAAI,OAAO,WAAW,UAAU;YAC9B,cAAc,MAAM,GAAG;QACzB,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS;YAChC,cAAc,MAAM,GAAG,oBAAoB;QAC7C,OAAO,IAAI,OAAO,WAAW,UAAU;YACrC,MAAM,YAAY,OAAO,OAAO,MAAM,KAAK,YAAY,MAAM,OAAO,CAAC,OAAO,MAAM;YAClF,MAAM,YAAY,MAAM,OAAO,CAAC,OAAO,MAAM;YAC7C,IAAI,aAAa,WAAW;gBAC1B,MAAM,IAAI,MACR;YAEJ;YACA,IAAI,OAAO,OAAO,MAAM,KAAK,UAAU;gBACrC,cAAc,MAAM,GAAG,OAAO,MAAM;YACtC,OAAO,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;gBACvC,cAAc,MAAM,GAAG,oBAAoB,OAAO,MAAM;YAC1D;YACA,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;gBAChC,cAAc,MAAM,GAAG,oBAAoB,OAAO,MAAM;YAC1D;YACA,IAAI,OAAO,QAAQ,KAAK,MAAM;gBAC5B,cAAc,QAAQ,GAAG,CAAC,IAAI,CAAC;YACjC;YACA,IAAI,OAAO,YAAY,KAAK,MAAM;gBAChC,aAAa,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC;YACzC;QACF;QACA,MAAM,iBAAiB,OAAO,OAAO,CAAC,eAAe,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC;QAClI,IAAI,gBAAgB;YAClB,SAAS,iBAAiB,CAAC,CAAC,aAAa,EAAE,gBAAgB;QAC7D;QACA,OAAO,CAAC;IACV;AACF;AACA,SAAS,oBAAoB,WAAW;IACtC,MAAM,UAAU;QACd,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,MAAM,eAAe,YAAY,GAAG,CAAC,CAAC,QAAQ;QAC5C,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,OAAO,oBAAoB;QAC7B;QACA,MAAM,MAAM,OAAO,CAAC,MAAM;QAC1B,OAAO,GAAG,IAAI,CAAC,EAAE,QAAQ;IAC3B,GAAG,IAAI,CAAC;IACR,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AAC5B;;AAIA,IAAI,yBAAyB;IAC3B,mBAAmB,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAC3B,iLAAA,CAAA,IAAG,CAAC,OAAO;QACX,iLAAA,CAAA,IAAG,CAAC,MAAM;QACV,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;YACT,MAAM,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ;YAC3B,QAAQ,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ;QAC/B;KACD,EAAE,QAAQ,CACT,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,0BAA0B;IAC5B,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,iBAAiB,EAAE,GAAG;QAC9B,IAAI,CAAC,qBAAqB,OAAO,sBAAsB,aAAa,OAAO,CAAC;QAC5E,MAAM,aAAa,EAAE;QACrB,IAAI,OAAO,sBAAsB,UAAU;YACzC,IAAI,OAAO,kBAAkB,MAAM,KAAK,aAAa;gBACnD,WAAW,IAAI,CAAC,CAAC,OAAO,EAAE,kBAAkB,MAAM,EAAE;YACtD;YACA,IAAI,OAAO,kBAAkB,IAAI,KAAK,UAAU;gBAC9C,WAAW,IAAI,CAAC,CAAC,KAAK,EAAE,kBAAkB,IAAI,EAAE;YAClD;QACF,OAAO,IAAI,OAAO,sBAAsB,UAAU;YAChD,WAAW,IAAI,CAAC,CAAC,OAAO,EAAE,mBAAmB;QAC/C;QACA,IAAI,iBAAiB;QACrB,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,iBAAiB,GAAG,eAAe,CAAC,EAAE,WAAW,IAAI,CAAC,MAAM;QAC9D;QACA,SAAS,iBAAiB,CAAC;QAC3B,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QACjB,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,MAAM;QACpB,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAG,CAAC,OAAO;QACrB,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;YACT,IAAI,iLAAA,CAAA,IAAG,CAAC,MAAM;YACd,MAAM,iLAAA,CAAA,IAAG,CAAC,MAAM;YAChB,kBAAkB,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ;QAC1C;KACD,EAAE,QAAQ,CACT,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,UAAU,IAAI,EAAE,GAAG;QAC3B,IAAI,SAAS;YACX,IAAI,MAAM,IAAI,mBAAmB;YACjC,IAAI,MAAM,OAAO,CAAC,UAAU;gBAC1B,OAAO,OAAO,CAAC,EAAE;gBACjB,KAAK,OAAO,CAAC,EAAE;gBACf,mBAAmB,OAAO,CAAC,EAAE,IAAI;YACnC,OAAO;gBACL,OAAO,QAAQ,IAAI;gBACnB,KAAK,QAAQ,EAAE;gBACf,mBAAmB,QAAQ,gBAAgB,IAAI;YACjD;YACA,MAAM,aAAa;gBAAC,CAAC,mBAAmB,EAAE,MAAM;gBAAE,CAAC,GAAG,EAAE,IAAI;aAAC;YAC7D,IAAI,kBAAkB;gBACpB,WAAW,IAAI,CAAC,CAAC,kBAAkB,EAAE,kBAAkB;YACzD;YACA,SAAS,MAAM,CAAC,WAAW,IAAI,CAAC;QAClC;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAG,CAAC,OAAO,GAAG,QAAQ,CAC7B,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,UAAU,KAAK,EAAE,GAAG;QAC5B,IAAI,SAAS;YACX,SAAS,MAAM,CAAC;QAClB;QACA,OAAO,CAAC;IACV;AACF;;AAIA,IAAI,eAAe;IACjB,SAAS,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QACjB,iLAAA,CAAA,IAAG,CAAC,MAAM;QACV,iLAAA,CAAA,IAAG,CAAC,OAAO;QACX,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;YACT,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ;YAClC,SAAS,iLAAA,CAAA,IAAG,CAAC,MAAM;QACrB;KACD,EAAE,QAAQ,CACT,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,gBAAgB;IAClB,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,EAAE,UAAU,KAAK,EAAE,GAAG;QAC5B,MAAM,YAAY;YAChB,QAAQ,KAAK;QACf;QACA,IAAI,YAAY,MAAM;YACpB,SAAS,MAAM,CAAC;QAClB,OAAO,IAAI,OAAO,YAAY,UAAU;YACtC,IAAI,YAAY,QAAQ;gBACtB,SAAS,MAAM,CAAC;gBAChB,SAAS,MAAM,CAAC;YAClB,OAAO;gBACL,SAAS,MAAM,CAAC,CAAC,UAAU,EAAE,SAAS;YACxC;QACF,OAAO,IAAI,OAAO,YAAY,UAAU;YACtC,IAAI,gBAAgB;YACpB,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;gBACvC,gBAAgB,GAAG,cAAc,CAAC,EAAE,QAAQ,OAAO,EAAE;YACvD;YACA,SAAS,MAAM,CAAC;YAChB,IAAI;YACJ,IAAI,QAAQ,IAAI,KAAK,MAAM;gBACzB,aAAa;YACf,OAAO,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,OAAO,QAAQ,IAAI,KAAK,UAAU;gBAC/E,aAAa,CAAC,OAAO,EAAE,QAAQ,IAAI,EAAE;YACvC;YACA,IAAI,YAAY;gBACd,SAAS,MAAM,CAAC;YAClB;QACF;QACA,IAAI,YAAY,OAAO;YACrB,UAAU,MAAM,GAAG;QACrB;QACA,OAAO;YACL,SAAS;QACX;IACF;AACF;AAEA,qBAAqB;AACrB,IAAI,qBAAqB,mBAAmB,KAAK,CAC/C,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IACT,iFAAiF;IACjF,+EAA+E;IAC/E,GAAG,iBAAiB;IACpB,GAAG,YAAY;IACf,GAAG,YAAY;IACf,GAAG,mBAAmB;IACtB,GAAG,YAAY;IACf,GAAG,WAAW;IACd,GAAG,YAAY;IACf,GAAG,sBAAsB;IACzB,GAAG,YAAY;IACf,GAAG,YAAY;AACjB;;;AAQF,IAAI,WAAW;IACb,kBAAkB,iLAAA,CAAA,IAAG,CAAC,MAAM,GAAG,QAAQ,CACrC,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;IACP,IACA,QAAQ;AACZ;AACA,IAAI,YAAY;IACd,OAAO;IACP,YAAY;QAAC;QAAS;KAAS;IAC/B,QAAQ,CAAC;QACP,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,EAAE,gBAAgB,EAAE,GAAG;QAC7B,IAAI,OAAO,qBAAqB,UAAU;YACxC,SAAS,iBAAiB,CAAC,CAAC,GAAG,EAAE,kBAAkB;QACrD;QACA,OAAO,CAAC;IACV;AACF;AAEA,qBAAqB;AACrB,IAAI,qBAAqB,mBAAmB,KAAK,CAC/C,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IACT,iFAAiF;IACjF,+EAA+E;IAC/E,GAAG,QAAQ;AACb;;;;;AAYF,IAAI,yBAAyB,iLAAA,CAAA,IAAG,CAAC,GAAG;;AAIpC,IAAI,sBAAsB,iLAAA,CAAA,IAAG,CAAC,GAAG;AAEjC,wBAAwB;AACxB,IAAI,wBAAwB;IAC1B,2DAA2D;IAC3D,oDAAoD;IACpD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gEAAgE;IAChE,6DAA6D;IAC7D,2DAA2D;IAC3D,kDAAkD;IAClD;IACA,gDAAgD;IAChD,4CAA4C;IAC5C,kCAAkC;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,IAAI,4BAA4B,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;IAAC;IAAoB;CAAmB,EAAE,QAAQ,CAC1F,KAAK,SAAS,CAAC;IACb,MAAM;IACN,MAAM;AACR;AAEF,IAAI,0BAA0B,iLAAA,CAAA,IAAG,CAAC,MAAM,CAAC;IACvC,WAAW,iLAAA,CAAA,IAAG,CAAC,KAAK,CAAC;QAAC;QAAwB,iLAAA,CAAA,IAAG,CAAC,OAAO;KAAG,EAAE,QAAQ,CACpE,KAAK,SAAS,CAAC;QACb,MAAM;QACN,MAAM;IACR,IACA,QAAQ;IACV,QAAQ,oBAAoB,QAAQ,CAClC,KAAK,SAAS,CAAC;QACb,MAAM;QACN,KAAK;QACL,MAAM;IACR,IACA,QAAQ;IACV,SAAS;AACX;AACA,SAAS,uBAAuB,EAC9B,OAAO,EACP,SAAS,CAAC,CAAC,EACX,SAAS,EACV;IACC,IAAI,cAAc,OAAO;QACvB,IAAI,OAAO,QAAQ,QAAQ,aAAa;YACtC,OAAO,GAAG,GAAG,CAAC;QAChB;QACA,OAAO,GAAG,CAAC,SAAS,GAAG;IACzB;IACA,MAAM,MAAM,IAAI,oKAAA,CAAA,aAAU,CAAC;IAC3B,IAAI,OAAO,SAAS,QAAQ,UAAU;QACpC,MAAM,MACJ,CAAC,oEAAoE,CAAC;IAE1E;IACA,IAAI,CAAC,SAAS,WAAW;QACvB,QAAQ,SAAS,GAAG;IACtB;IACA,MAAM,aAAa,EAAE;IACrB,sBAAsB,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;QACtC,MAAM,cAAc,OAAO,IAAI,CAAC;QAChC,YAAY,OAAO,CAAC,CAAC;YACnB,IAAI,WAAW,QAAQ,CAAC,OAAO;gBAC7B,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,gBAAgB,CAAC;YAClD;YACA,WAAW,IAAI,CAAC;QAClB;IACF;IACA,MAAM,gBAAgB,CAAC;IACvB,IAAI;IACJ,IAAI,OAAO,QAAQ,GAAG,KAAK,YAAY,eAAe,IAAI,CAAC,QAAQ,GAAG,GAAG;QACvE,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,GAAG;YAClC,WAAW,OAAO;YAClB,cAAc,SAAS,GAAG,OAAO;YACjC,cAAc,OAAO,GAAG,OAAO;QACjC,EAAE,OAAO,GAAG,CACZ;IACF;IACA,IAAI,CAAC,UAAU;QACb,WAAW,QAAQ,GAAG;IACxB;IACA,OAAO,IAAI,CAAC,eAAe,OAAO,CAChC,CAAC;QACC,IAAI,CAAA,GAAA,sNAAA,CAAA,eAAa,AAAD,EAAE,SAAS,MAAM;QACjC,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI;IACnC;IAEF,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,GAAG,CAAC;IACvC,IAAI,WAAW,KAAK;IACpB,IAAI;QAAC;QAAS;KAAS,CAAC,QAAQ,CAAC,QAAQ,SAAS,GAAG;QACnD,WAAW,IAAI,KAAK,CAAC;IACvB,OAAO,IAAI;QAAC;QAAS;KAAS,CAAC,QAAQ,CAAC,QAAQ,SAAS,GAAG;QAC1D,WAAW,IAAI,KAAK,CAAC;IACvB;IACA,IAAI,OAAO,aAAa,aAAa;QACnC,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,gBAAgB,CAAC;IACvB,sBAAsB,OAAO,CAC3B,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE;QACpC,MAAM,qBAAqB,SAAS,cAAc,KAAK,KAAK,WAAW,QAAQ,CAAC,QAAQ,SAAS;QACjG,MAAM,cAAc,OAAO,IAAI,CAAC;QAChC,MAAM,cAAc,OAAO,IAAI,CAAC;QAChC,MAAM,eAAe,YAAY,GAAG,CAAC,CAAC,OAAS,YAAY,QAAQ,CAAC,OAAO,MAAM,CAAC,CAAC,SAAW,CAAC,CAAC,QAAQ,MAAM,GAAG;QACjH,IAAI,CAAC,oBAAoB;YACvB,IAAI,cAAc;gBAChB,QAAQ,IAAI,CACV,CAAC,4BAA4B,EAAE,YAAY,IAAI,CAC7C,MACA,2CAA2C,EAAE,SAAS,UAAU,CAAC,CAAC;YAExE;YACA;QACF;QACA,IAAI,QAAQ,qBAAqB,IAAI,CAAC,QAAQ;YAC5C,IAAI,cAAc;gBAChB,QAAQ,IAAI,CACV,CAAC,4BAA4B,EAAE,YAAY,IAAI,CAC7C,MACA,6DAA6D,CAAC;YAEpE;YACA;QACF;QACA,MAAM,UAAU,OAAO;YACrB;YACA;QACF;QACA,MAAM,EAAE,SAAS,aAAa,EAAE,GAAG,WAAW;YAAE,SAAS,KAAK;QAAE;QAChE,OAAO,MAAM,CAAC,eAAe;IAC/B;IAEF,IAAI,OAAO,cAAc,MAAM,KAAK,UAAU;QAC5C,SAAS,iBAAiB,CAAC,cAAc,MAAM;IACjD;IACA,SAAS,eAAe,CAAC,SAAS,gBAAgB;IAClD,IAAI,CAAC,QAAQ,qBAAqB,EAAE;QAClC,IAAI,SAAS,KAAK;YAChB,IAAI,MAAM,QAAQ,GAAG;YACrB,IAAI,OAAO,QAAQ,UAAU;gBAC3B,MAAM,IAAI,OAAO,CAAC;YACpB;YACA,SAAS,iBAAiB,CAAC,CAAC,IAAI,EAAE,KAAK;QACzC;QACA,MAAM,gBAAgB,SAAS,WAAW;QAC1C,MAAM,oBAAoB,8BAA8B,MAAM,UAAU;YACtE,WAAW;QACb;QACA,MAAM,+BAA+B,qBAAqB,OAAO,SAAS,WAAW;QACrF,IAAI,eAAe,UAAU,CAAC,iBAAiB,CAAC,CAAC,qBAAqB,4BAA4B,GAAG;YACnG,SAAS,MAAM,CAAC,SAAS,UAAU,eAAe,UAAU;QAC9D;QACA,MAAM,iBAAiB,SAAS,YAAY;QAC5C,MAAM,qBAAqB,8BAA8B,MAAM,UAAU;YACvE,WAAW;QACb;QACA,MAAM,gCAAgC,sBAAsB,OAAO,SAAS,YAAY;QACxF,IAAI,CAAC,kBAAkB,CAAC,CAAC,sBAAsB,6BAA6B,GAAG;YAC7E,SAAS,OAAO,CAAC,SAAS,WAAW;QACvC;IACF;IACA,OAAO,SAAS,KAAK,CAAC;QACpB,kBAAkB;IACpB;AACF;AACA,SAAS,8BAA8B,KAAK,EAAE,KAAK,EAAE,OAAO;IAC1D,IAAI,OAAO,MAAM,cAAc,KAAK,aAAa;IACjD,MAAM,EAAE,YAAY,UAAU,EAAE,GAAG,WAAW,CAAC;IAC/C,MAAM,kBAAkB,MAAM,cAAc,CAAC,OAAO,CAAC,OAAO,CAC1D,CAAC;QACC,OAAO,eAAe,QAAQ,GAAG,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC,MAAQ,IAAI,KAAK,CAAC;IACzE;IAEF,MAAM,UAAU,gBAAgB,MAAM,CAAC,CAAC;QACtC,IAAI,cAAc,cAAc;YAC9B,OAAO,eAAe,UAAU,CAAC;QACnC,OAAO;YACL,OAAO,eAAe,QAAQ,CAAC;QACjC;IACF;IACA,OAAO,QAAQ,MAAM,GAAG;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/node_modules/%40cloudinary-util/url-loader/dist/index.js"], "sourcesContent": ["import {\n  constructCloudinaryUrl,\n  effects,\n  position,\n  primary,\n  text,\n  transformationPlugins\n} from \"./chunk-L3YIXMGG.js\";\n\n// src/lib/upload-widget.ts\nfunction getUploadWidgetOptions({ uploadSignature, ...options }, config) {\n  const signed = typeof uploadSignature === \"function\";\n  const { cloudName, apiKey } = config?.cloud || {};\n  if (!cloudName) {\n    throw new Error(\"A Cloudinary Cloud name is required, please make sure your environment variable is set and configured in your environment.\");\n  }\n  if (signed && !apiKey) {\n    throw new Error(\"A Cloudinary API Key is required for signed requests, please make sure your environment variable is set and configured in your environment.\");\n  }\n  if (!signed && !options.uploadPreset) {\n    throw new Error(\"A Cloudinary Upload Preset is required for unsigned uploads. Please specify an uploadPreset or configure signed uploads.\");\n  }\n  const uploadOptions = {\n    cloudName,\n    apiKey,\n    ...options\n  };\n  if (signed) {\n    uploadOptions.uploadSignature = uploadSignature;\n  }\n  return uploadOptions;\n}\nvar UPLOAD_WIDGET_EVENTS = {\n  \"abort\": \"onAbort\",\n  \"batch-cancelled\": \"onBatchCancelled\",\n  \"close\": \"onClose\",\n  \"display-changed\": \"onDisplayChanged\",\n  \"publicid\": \"onPublicId\",\n  \"queues-end\": \"onQueuesEnd\",\n  \"queues-start\": \"onQueuesStart\",\n  \"retry\": \"onRetry\",\n  \"show-completed\": \"onShowCompleted\",\n  \"source-changed\": \"onSourceChanged\",\n  \"success\": \"onSuccess\",\n  \"tags\": \"onTags\",\n  \"upload-added\": \"onUploadAdded\"\n};\nfunction generateUploadWidgetResultCallback(options) {\n  return function resultCallback(error, uploadResult) {\n    if (error) {\n      if (typeof options.onError === \"function\") {\n        options.onError(error, uploadResult);\n      }\n    }\n    if (typeof options.onResult === \"function\") {\n      options.onResult(uploadResult);\n    }\n    const widgetEvent = typeof uploadResult?.event === \"string\" && UPLOAD_WIDGET_EVENTS[uploadResult.event];\n    if (typeof widgetEvent === \"string\" && typeof options[widgetEvent] === \"function\") {\n      const callback = options[widgetEvent];\n      callback(uploadResult);\n    }\n  };\n}\n\n// src/lib/upload.ts\nfunction generateSignatureCallback({ signatureEndpoint, fetch: fetcher }) {\n  return function generateSignature(callback, paramsToSign) {\n    if (typeof signatureEndpoint === \"undefined\") {\n      throw Error(\"Failed to generate signature: signatureEndpoint property undefined.\");\n    }\n    if (typeof fetcher === \"undefined\") {\n      throw Error(\"Failed to generate signature: fetch property undefined.\");\n    }\n    fetcher(signatureEndpoint, {\n      method: \"POST\",\n      body: JSON.stringify({\n        paramsToSign\n      }),\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    }).then((response) => response.json()).then((result) => {\n      callback(result.signature);\n    }).catch((error) => {\n      callback(null, error);\n    });\n  };\n}\n\n// src/lib/video-player.ts\nimport { parseUrl } from \"@cloudinary-util/util\";\nfunction getVideoPlayerOptions(options, config) {\n  const {\n    autoplay,\n    controls = true,\n    language,\n    languages,\n    logo = true,\n    loop = false,\n    muted = false,\n    poster,\n    src,\n    transformation,\n    quality = \"auto\",\n    ...otherCldVidPlayerOptions\n  } = options;\n  const { cloudName } = config?.cloud || {};\n  const { secureDistribution, privateCdn } = config?.url || {};\n  if (!cloudName) {\n    throw new Error(\n      \"A Cloudinary Cloud name is required, please make sure your environment variable is set and configured in your environment.\"\n    );\n  }\n  let publicId = src || \"\";\n  if (publicId.startsWith(\"http\")) {\n    try {\n      const parts = parseUrl(src);\n      if (typeof parts?.publicId === \"string\") {\n        publicId = parts?.publicId;\n      }\n    } catch (e) {\n    }\n  }\n  if (!publicId) {\n    throw new Error(\n      \"Video Player requires a src, please make sure to configure your src as a public ID or Cloudinary URL.\"\n    );\n  }\n  const playerTransformations = Array.isArray(transformation) ? transformation : [transformation];\n  playerTransformations.unshift({\n    quality\n  });\n  let logoOptions = {};\n  if (typeof logo === \"boolean\") {\n    logoOptions.showLogo = logo;\n  } else if (typeof logo === \"object\") {\n    logoOptions = {\n      ...logoOptions,\n      showLogo: true,\n      logoImageUrl: logo.imageUrl,\n      logoOnclickUrl: logo.onClickUrl\n    };\n  }\n  let autoplayValue = false;\n  let autoplayModeValue = void 0;\n  if (typeof autoplay === \"boolean\" || autoplay === \"true\" || autoplay === \"false\") {\n    autoplayValue = autoplay;\n  }\n  if (typeof autoplay === \"string\" && autoplay !== \"true\" && autoplay !== \"false\") {\n    autoplayModeValue = autoplay;\n  }\n  const playerOptions = {\n    cloud_name: cloudName,\n    privateCdn,\n    secureDistribution,\n    autoplayMode: autoplayModeValue,\n    autoplay: autoplayValue,\n    controls,\n    language,\n    languages,\n    loop,\n    muted,\n    publicId,\n    transformation: playerTransformations,\n    ...logoOptions,\n    ...otherCldVidPlayerOptions\n  };\n  if (playerOptions.width && playerOptions.height && !playerOptions.aspectRatio) {\n    playerOptions.aspectRatio = `${playerOptions.width}:${playerOptions.height}`;\n  }\n  if (typeof poster === \"string\") {\n    playerOptions.posterOptions = {\n      publicId: poster\n    };\n  } else if (typeof poster === \"object\") {\n    if (typeof poster.src !== \"string\") {\n      playerOptions.posterOptions = {\n        publicId: constructCloudinaryUrl({\n          options: {\n            ...poster,\n            src: publicId,\n            assetType: \"video\",\n            format: \"auto:image\"\n          },\n          config\n        })\n      };\n    } else {\n      playerOptions.posterOptions = {\n        publicId: constructCloudinaryUrl({\n          options: poster,\n          config\n        })\n      };\n    }\n  }\n  return playerOptions;\n}\nexport {\n  UPLOAD_WIDGET_EVENTS,\n  constructCloudinaryUrl,\n  effects,\n  generateSignatureCallback,\n  generateUploadWidgetResultCallback,\n  getUploadWidgetOptions,\n  getVideoPlayerOptions,\n  position,\n  primary,\n  text,\n  transformationPlugins\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AA0FA,0BAA0B;AAC1B;;AAlFA,2BAA2B;AAC3B,SAAS,uBAAuB,EAAE,eAAe,EAAE,GAAG,SAAS,EAAE,MAAM;IACrE,MAAM,SAAS,OAAO,oBAAoB;IAC1C,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,QAAQ,SAAS,CAAC;IAChD,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,UAAU,CAAC,QAAQ;QACrB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,CAAC,UAAU,CAAC,QAAQ,YAAY,EAAE;QACpC,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,gBAAgB;QACpB;QACA;QACA,GAAG,OAAO;IACZ;IACA,IAAI,QAAQ;QACV,cAAc,eAAe,GAAG;IAClC;IACA,OAAO;AACT;AACA,IAAI,uBAAuB;IACzB,SAAS;IACT,mBAAmB;IACnB,SAAS;IACT,mBAAmB;IACnB,YAAY;IACZ,cAAc;IACd,gBAAgB;IAChB,SAAS;IACT,kBAAkB;IAClB,kBAAkB;IAClB,WAAW;IACX,QAAQ;IACR,gBAAgB;AAClB;AACA,SAAS,mCAAmC,OAAO;IACjD,OAAO,SAAS,eAAe,KAAK,EAAE,YAAY;QAChD,IAAI,OAAO;YACT,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY;gBACzC,QAAQ,OAAO,CAAC,OAAO;YACzB;QACF;QACA,IAAI,OAAO,QAAQ,QAAQ,KAAK,YAAY;YAC1C,QAAQ,QAAQ,CAAC;QACnB;QACA,MAAM,cAAc,OAAO,cAAc,UAAU,YAAY,oBAAoB,CAAC,aAAa,KAAK,CAAC;QACvG,IAAI,OAAO,gBAAgB,YAAY,OAAO,OAAO,CAAC,YAAY,KAAK,YAAY;YACjF,MAAM,WAAW,OAAO,CAAC,YAAY;YACrC,SAAS;QACX;IACF;AACF;AAEA,oBAAoB;AACpB,SAAS,0BAA0B,EAAE,iBAAiB,EAAE,OAAO,OAAO,EAAE;IACtE,OAAO,SAAS,kBAAkB,QAAQ,EAAE,YAAY;QACtD,IAAI,OAAO,sBAAsB,aAAa;YAC5C,MAAM,MAAM;QACd;QACA,IAAI,OAAO,YAAY,aAAa;YAClC,MAAM,MAAM;QACd;QACA,QAAQ,mBAAmB;YACzB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;YACF;YACA,SAAS;gBACP,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI,IAAI,IAAI,CAAC,CAAC;YAC3C,SAAS,OAAO,SAAS;QAC3B,GAAG,KAAK,CAAC,CAAC;YACR,SAAS,MAAM;QACjB;IACF;AACF;;AAIA,SAAS,sBAAsB,OAAO,EAAE,MAAM;IAC5C,MAAM,EACJ,QAAQ,EACR,WAAW,IAAI,EACf,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,OAAO,KAAK,EACZ,QAAQ,KAAK,EACb,MAAM,EACN,GAAG,EACH,cAAc,EACd,UAAU,MAAM,EAChB,GAAG,0BACJ,GAAG;IACJ,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ,SAAS,CAAC;IACxC,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,GAAG,QAAQ,OAAO,CAAC;IAC3D,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,MACR;IAEJ;IACA,IAAI,WAAW,OAAO;IACtB,IAAI,SAAS,UAAU,CAAC,SAAS;QAC/B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,IAAI,OAAO,OAAO,aAAa,UAAU;gBACvC,WAAW,OAAO;YACpB;QACF,EAAE,OAAO,GAAG,CACZ;IACF;IACA,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MACR;IAEJ;IACA,MAAM,wBAAwB,MAAM,OAAO,CAAC,kBAAkB,iBAAiB;QAAC;KAAe;IAC/F,sBAAsB,OAAO,CAAC;QAC5B;IACF;IACA,IAAI,cAAc,CAAC;IACnB,IAAI,OAAO,SAAS,WAAW;QAC7B,YAAY,QAAQ,GAAG;IACzB,OAAO,IAAI,OAAO,SAAS,UAAU;QACnC,cAAc;YACZ,GAAG,WAAW;YACd,UAAU;YACV,cAAc,KAAK,QAAQ;YAC3B,gBAAgB,KAAK,UAAU;QACjC;IACF;IACA,IAAI,gBAAgB;IACpB,IAAI,oBAAoB,KAAK;IAC7B,IAAI,OAAO,aAAa,aAAa,aAAa,UAAU,aAAa,SAAS;QAChF,gBAAgB;IAClB;IACA,IAAI,OAAO,aAAa,YAAY,aAAa,UAAU,aAAa,SAAS;QAC/E,oBAAoB;IACtB;IACA,MAAM,gBAAgB;QACpB,YAAY;QACZ;QACA;QACA,cAAc;QACd,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB;QAChB,GAAG,WAAW;QACd,GAAG,wBAAwB;IAC7B;IACA,IAAI,cAAc,KAAK,IAAI,cAAc,MAAM,IAAI,CAAC,cAAc,WAAW,EAAE;QAC7E,cAAc,WAAW,GAAG,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,cAAc,MAAM,EAAE;IAC9E;IACA,IAAI,OAAO,WAAW,UAAU;QAC9B,cAAc,aAAa,GAAG;YAC5B,UAAU;QACZ;IACF,OAAO,IAAI,OAAO,WAAW,UAAU;QACrC,IAAI,OAAO,OAAO,GAAG,KAAK,UAAU;YAClC,cAAc,aAAa,GAAG;gBAC5B,UAAU,CAAA,GAAA,kLAAA,CAAA,yBAAsB,AAAD,EAAE;oBAC/B,SAAS;wBACP,GAAG,MAAM;wBACT,KAAK;wBACL,WAAW;wBACX,QAAQ;oBACV;oBACA;gBACF;YACF;QACF,OAAO;YACL,cAAc,aAAa,GAAG;gBAC5B,UAAU,CAAA,GAAA,kLAAA,CAAA,yBAAsB,AAAD,EAAE;oBAC/B,SAAS;oBACT;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}