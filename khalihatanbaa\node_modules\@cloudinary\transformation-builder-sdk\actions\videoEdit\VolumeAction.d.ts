import { Action } from "../../internal/Action.js";
import { IActionModel } from "../../internal/models/IActionModel.js";
import { IVolumeActionModel } from "../../internal/models/IVolumeActionModel.js";
/**
 * @description Class to Controls the volume of an audio or video file.
 * @extends SDK.Action
 * @memberOf Actions.VideoEdit
 * @see Visit {@link Actions.VideoEdit|VideoEdit} for an example
 */
declare class VolumeAction extends Action {
    protected _actionModel: IVolumeActionModel;
    constructor(volumeValue: string | number);
    static fromJson(actionModel: IActionModel): VolumeAction;
}
export default VolumeAction;
