{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/ads\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الإعلانات\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-dark-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* معلومات الموقع */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4\">\n              <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">خ</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">خَلّيها تنْباع</h3>\n                <p className=\"text-sm text-gray-400\">منصة البيع والشراء</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. \n              نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">فيسبوك</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">تلغرام</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">واتساب</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* روابط سريعة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  من نحن\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  كيف يعمل الموقع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety-tips\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  نصائح الأمان\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  باقات الإعلانات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  اتصل بنا\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* الدعم والمساعدة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">الدعم والمساعدة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  مركز المساعدة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  شروط الاستخدام\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الإبلاغ عن مشكلة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* معلومات الاتصال */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center md:text-right\">\n              <h5 className=\"font-semibold mb-2\">البريد الإلكتروني</h5>\n              <p className=\"text-gray-300\"><EMAIL></p>\n            </div>\n            <div className=\"text-center\">\n              <h5 className=\"font-semibold mb-2\">رقم الهاتف</h5>\n              <p className=\"text-gray-300\" dir=\"ltr\">+963 11 123 4567</p>\n            </div>\n            <div className=\"text-center md:text-left\">\n              <h5 className=\"font-semibold mb-2\">العنوان</h5>\n              <p className=\"text-gray-300\">دمشق، سوريا</p>\n            </div>\n          </div>\n        </div>\n\n        {/* حقوق النشر */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\n          </p>\n          <p className=\"text-gray-500 text-sm mt-2\">\n            تم التطوير بـ ❤️ في سوريا\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAmD;;;;;;;;;;;sDAI1F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;wCAAgB,KAAI;kDAAM;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ui/ImageUpload.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef } from \"react\";\nimport { CldUploadWidget } from \"next-cloudinary\";\nimport {\n  PhotoIcon,\n  XMarkIcon,\n  PlusIcon,\n  ArrowUpTrayIcon,\n} from \"@heroicons/react/24/outline\";\n\ninterface ImageUploadProps {\n  value: string[];\n  onChange: (urls: string[]) => void;\n  maxImages?: number;\n  disabled?: boolean;\n}\n\nexport function ImageUpload({\n  value = [],\n  onChange,\n  maxImages = 5,\n  disabled = false,\n}: ImageUploadProps) {\n  const [uploading, setUploading] = useState(false);\n\n  const handleUpload = (result: any) => {\n    if (result.event === \"success\") {\n      const newUrls = [...value, result.info.secure_url];\n      onChange(newUrls);\n      setUploading(false);\n    }\n  };\n\n  const removeImage = (index: number) => {\n    const newUrls = value.filter((_, i) => i !== index);\n    onChange(newUrls);\n  };\n\n  const canAddMore = value.length < maxImages;\n\n  return (\n    <div className=\"space-y-4\">\n      {/* عرض الصور المرفوعة */}\n      {value.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n          {value.map((url, index) => (\n            <div key={index} className=\"relative group\">\n              <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n                <img\n                  src={url}\n                  alt={`صورة ${index + 1}`}\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n\n              {/* زر الحذف */}\n              <button\n                type=\"button\"\n                onClick={() => removeImage(index)}\n                disabled={disabled}\n                className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity disabled:opacity-50\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n\n              {/* رقم الصورة */}\n              <div className=\"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\n                {index + 1}\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* زر إضافة صور */}\n      {canAddMore && (\n        <CldUploadWidget\n          uploadPreset=\"khalihatanbaa_preset\"\n          options={{\n            maxFiles: 1,\n            resourceType: \"image\",\n            clientAllowedFormats: [\"jpg\", \"jpeg\", \"png\", \"webp\"],\n            maxFileSize: 5000000, // 5MB\n            folder: \"khalihatanbaa/ads\",\n            transformation: [\n              { width: 800, height: 600, crop: \"limit\" },\n              { quality: \"auto\" },\n              { format: \"auto\" },\n            ],\n            sources: [\"local\", \"camera\"],\n            multiple: false,\n            cropping: true,\n            croppingAspectRatio: 4 / 3,\n          }}\n          onUpload={handleUpload}\n          onOpen={() => setUploading(true)}\n          onClose={() => setUploading(false)}\n        >\n          {({ open }) => (\n            <button\n              type=\"button\"\n              onClick={() => open()}\n              disabled={disabled || uploading}\n              className=\"w-full border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <div className=\"flex flex-col items-center\">\n                {uploading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-3\"></div>\n                    <p className=\"text-sm text-gray-600\">جاري رفع الصورة...</p>\n                  </>\n                ) : (\n                  <>\n                    <PhotoIcon className=\"h-12 w-12 text-gray-400 mb-3\" />\n                    <p className=\"text-sm font-medium text-gray-900 mb-1\">\n                      اضغط لإضافة صورة\n                    </p>\n                    <p className=\"text-xs text-gray-500\">\n                      PNG, JPG, WEBP حتى 5MB\n                    </p>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {value.length} من {maxImages} صور\n                    </p>\n                  </>\n                )}\n              </div>\n            </button>\n          )}\n        </CldUploadWidget>\n      )}\n\n      {/* رسالة عند الوصول للحد الأقصى */}\n      {!canAddMore && (\n        <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n          <p className=\"text-sm text-gray-600\">\n            تم الوصول للحد الأقصى من الصور ({maxImages} صور)\n          </p>\n        </div>\n      )}\n\n      {/* نصائح */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n          نصائح للحصول على أفضل النتائج:\n        </h4>\n        <ul className=\"text-xs text-blue-700 space-y-1\">\n          <li>• استخدم صور واضحة وعالية الجودة</li>\n          <li>• اعرض المنتج من زوايا مختلفة</li>\n          <li>• تأكد من الإضاءة الجيدة</li>\n          <li>• الصورة الأولى ستكون الصورة الرئيسية</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n\n// مكون مبسط لعرض الصور فقط\nexport function ImageGallery({ images }: { images: string[] }) {\n  const [currentImage, setCurrentImage] = useState(0);\n\n  if (!images || images.length === 0) {\n    return (\n      <div className=\"aspect-square bg-gray-100 rounded-lg flex items-center justify-center\">\n        <PhotoIcon className=\"h-16 w-16 text-gray-400\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* الصورة الرئيسية */}\n      <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n        <img\n          src={images[currentImage]}\n          alt={`صورة ${currentImage + 1}`}\n          className=\"w-full h-full object-cover\"\n        />\n      </div>\n\n      {/* الصور المصغرة */}\n      {images.length > 1 && (\n        <div className=\"flex space-x-2 space-x-reverse overflow-x-auto\">\n          {images.map((image, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentImage(index)}\n              className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${\n                currentImage === index\n                  ? \"border-primary-500\"\n                  : \"border-gray-200 hover:border-gray-300\"\n              }`}\n            >\n              <img\n                src={image}\n                alt={`صورة ${index + 1}`}\n                className=\"w-full h-full object-cover\"\n              />\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* مؤشر الصور */}\n      {images.length > 1 && (\n        <div className=\"text-center\">\n          <span className=\"text-sm text-gray-500\">\n            {currentImage + 1} من {images.length}\n          </span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAkBO,SAAS,YAAY,EAC1B,QAAQ,EAAE,EACV,QAAQ,EACR,YAAY,CAAC,EACb,WAAW,KAAK,EACC;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,KAAK,KAAK,WAAW;YAC9B,MAAM,UAAU;mBAAI;gBAAO,OAAO,IAAI,CAAC,UAAU;aAAC;YAClD,SAAS;YACT,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,UAAU,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC7C,SAAS;IACX;IAEA,MAAM,aAAa,MAAM,MAAM,GAAG;IAElC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,KAAK,sBACf,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK;oCACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;oCACxB,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,YAAY;gCAC3B,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAIvB,8OAAC;gCAAI,WAAU;0CACZ,QAAQ;;;;;;;uBArBH;;;;;;;;;;YA6Bf,4BACC,8OAAC,oJAAA,CAAA,kBAAe;gBACd,cAAa;gBACb,SAAS;oBACP,UAAU;oBACV,cAAc;oBACd,sBAAsB;wBAAC;wBAAO;wBAAQ;wBAAO;qBAAO;oBACpD,aAAa;oBACb,QAAQ;oBACR,gBAAgB;wBACd;4BAAE,OAAO;4BAAK,QAAQ;4BAAK,MAAM;wBAAQ;wBACzC;4BAAE,SAAS;wBAAO;wBAClB;4BAAE,QAAQ;wBAAO;qBAClB;oBACD,SAAS;wBAAC;wBAAS;qBAAS;oBAC5B,UAAU;oBACV,UAAU;oBACV,qBAAqB,IAAI;gBAC3B;gBACA,UAAU;gBACV,QAAQ,IAAM,aAAa;gBAC3B,SAAS,IAAM,aAAa;0BAE3B,CAAC,EAAE,IAAI,EAAE,iBACR,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM;wBACf,UAAU,YAAY;wBACtB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,0BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;6DAGvC;;kDACE,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAGrC,8OAAC;wCAAE,WAAU;;4CACV,MAAM,MAAM;4CAAC;4CAAK;4CAAU;;;;;;;;;;;;;;;;;;;;;;;;YAW5C,CAAC,4BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAwB;wBACF;wBAAU;;;;;;;;;;;;0BAMjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;AAGO,SAAS,aAAa,EAAE,MAAM,EAAwB;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;QAClC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;IAG3B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAK,MAAM,CAAC,aAAa;oBACzB,KAAK,CAAC,KAAK,EAAE,eAAe,GAAG;oBAC/B,WAAU;;;;;;;;;;;YAKb,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,4DAA4D,EACtE,iBAAiB,QACb,uBACA,yCACJ;kCAEF,cAAA,8OAAC;4BACC,KAAK;4BACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;4BACxB,WAAU;;;;;;uBAXP;;;;;;;;;;YAmBZ,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;wBACb,eAAe;wBAAE;wBAAK,OAAO,MAAM;;;;;;;;;;;;;;;;;;AAMhD", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ui/SimpleImageUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport { PhotoIcon, XMarkIcon, PlusIcon } from '@heroicons/react/24/outline'\n\ninterface SimpleImageUploadProps {\n  images: string[]\n  onImagesChange: (images: string[]) => void\n  maxImages?: number\n  className?: string\n}\n\nexport function SimpleImageUpload({ \n  images, \n  onImagesChange, \n  maxImages = 5,\n  className = '' \n}: SimpleImageUploadProps) {\n  const [uploading, setUploading] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files\n    if (!files || files.length === 0) return\n\n    setUploading(true)\n\n    try {\n      const newImages: string[] = []\n\n      for (let i = 0; i < files.length && images.length + newImages.length < maxImages; i++) {\n        const file = files[i]\n        \n        // التحقق من نوع الملف\n        if (!file.type.startsWith('image/')) {\n          alert('يرجى اختيار ملفات صور فقط')\n          continue\n        }\n\n        // التحقق من حجم الملف (5MB)\n        if (file.size > 5 * 1024 * 1024) {\n          alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت')\n          continue\n        }\n\n        // تحويل الصورة إلى base64\n        const base64 = await fileToBase64(file)\n        newImages.push(base64)\n      }\n\n      onImagesChange([...images, ...newImages])\n    } catch (error) {\n      console.error('Error uploading images:', error)\n      alert('حدث خطأ في رفع الصور')\n    } finally {\n      setUploading(false)\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n    }\n  }\n\n  const fileToBase64 = (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      reader.readAsDataURL(file)\n      reader.onload = () => resolve(reader.result as string)\n      reader.onerror = error => reject(error)\n    })\n  }\n\n  const removeImage = (index: number) => {\n    const newImages = images.filter((_, i) => i !== index)\n    onImagesChange(newImages)\n  }\n\n  const canAddMore = images.length < maxImages\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* عرض الصور المرفوعة */}\n      {images.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n          {images.map((image, index) => (\n            <div key={index} className=\"relative group\">\n              <img\n                src={image}\n                alt={`صورة ${index + 1}`}\n                className=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => removeImage(index)}\n                className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n              {index === 0 && (\n                <div className=\"absolute bottom-2 left-2 bg-primary-500 text-white text-xs px-2 py-1 rounded\">\n                  الصورة الرئيسية\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* زر إضافة صور */}\n      {canAddMore && (\n        <div>\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            multiple\n            onChange={handleFileSelect}\n            className=\"hidden\"\n          />\n          \n          <button\n            type=\"button\"\n            onClick={() => fileInputRef.current?.click()}\n            disabled={uploading}\n            className=\"w-full h-32 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-gray-500 hover:border-primary-500 hover:text-primary-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {uploading ? (\n              <div className=\"flex flex-col items-center\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500\"></div>\n                <span className=\"mt-2 text-sm\">جاري الرفع...</span>\n              </div>\n            ) : (\n              <div className=\"flex flex-col items-center\">\n                <PlusIcon className=\"h-8 w-8 mb-2\" />\n                <span className=\"text-sm font-medium\">إضافة صور</span>\n                <span className=\"text-xs text-gray-400 mt-1\">\n                  {images.length}/{maxImages} صور\n                </span>\n              </div>\n            )}\n          </button>\n        </div>\n      )}\n\n      {/* نصائح */}\n      <div className=\"text-xs text-gray-500 space-y-1\">\n        <p>• يمكنك رفع حتى {maxImages} صور</p>\n        <p>• الحد الأقصى لحجم الصورة: 5 ميجابايت</p>\n        <p>• الصيغ المدعومة: JPG, PNG, WebP</p>\n        <p>• الصورة الأولى ستكون الصورة الرئيسية</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAYO,SAAS,kBAAkB,EAChC,MAAM,EACN,cAAc,EACd,YAAY,CAAC,EACb,YAAY,EAAE,EACS;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,aAAa;QAEb,IAAI;YACF,MAAM,YAAsB,EAAE;YAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,IAAI,OAAO,MAAM,GAAG,UAAU,MAAM,GAAG,WAAW,IAAK;gBACrF,MAAM,OAAO,KAAK,CAAC,EAAE;gBAErB,sBAAsB;gBACtB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,MAAM;oBACN;gBACF;gBAEA,4BAA4B;gBAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,MAAM;oBACN;gBACF;gBAEA,0BAA0B;gBAC1B,MAAM,SAAS,MAAM,aAAa;gBAClC,UAAU,IAAI,CAAC;YACjB;YAEA,eAAe;mBAAI;mBAAW;aAAU;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,aAAa;YACb,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,aAAa,CAAC;YACrB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;YAC3C,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;QACnC;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAChD,eAAe;IACjB;IAEA,MAAM,aAAa,OAAO,MAAM,GAAG;IAEnC,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YAErC,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCACC,KAAK;gCACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;gCACxB,WAAU;;;;;;0CAEZ,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAEtB,UAAU,mBACT,8OAAC;gCAAI,WAAU;0CAA+E;;;;;;;uBAdxF;;;;;;;;;;YAwBf,4BACC,8OAAC;;kCACC,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,QAAQ;wBACR,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,aAAa,OAAO,EAAE;wBACrC,UAAU;wBACV,WAAU;kCAET,0BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;iDAGjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,+MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,8OAAC;oCAAK,WAAU;;wCACb,OAAO,MAAM;wCAAC;wCAAE;wCAAU;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAE;4BAAiB;4BAAU;;;;;;;kCAC9B,8OAAC;kCAAE;;;;;;kCACH,8OAAC;kCAAE;;;;;;kCACH,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/ads/create/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter } from \"next/navigation\";\nimport { Header } from \"@/components/layout/Header\";\nimport { Footer } from \"@/components/layout/Footer\";\nimport { ImageUpload } from \"@/components/ui/ImageUpload\";\nimport { SimpleImageUpload } from \"@/components/ui/SimpleImageUpload\";\nimport {\n  PlusIcon,\n  InformationCircleIcon,\n  StarIcon,\n  ClockIcon,\n} from \"@heroicons/react/24/outline\";\n\ninterface AdLimits {\n  freeAds: {\n    total: number;\n    used: number;\n    available: number;\n    expiresAt?: string;\n  };\n  paidAds: {\n    total: number;\n    used: number;\n    available: number;\n    expiresAt?: string;\n  };\n  promotedAds: {\n    total: number;\n    used: number;\n    available: number;\n    expiresAt?: string;\n  };\n  totalActiveAds: number;\n  canCreateFreeAd: boolean;\n  canCreatePaidAd: boolean;\n  canCreatePromotedAd: boolean;\n}\n\nexport default function CreateAdPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  const [adLimits, setAdLimits] = useState<AdLimits | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [selectedAdType, setSelectedAdType] = useState<\n    \"free\" | \"paid\" | \"promoted\"\n  >(\"free\");\n  const [formData, setFormData] = useState({\n    title: \"\",\n    description: \"\",\n    price: \"\",\n    category: \"\",\n    subCategory: \"\",\n    condition: \"جديد\",\n    city: \"\",\n    region: \"\",\n    addressDetail: \"\",\n    duration: 30,\n  });\n  const [imageUrls, setImageUrls] = useState<string[]>([]);\n  const [useSimpleUpload, setUseSimpleUpload] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [errors, setErrors] = useState<string[]>([]);\n  const [debugInfo, setDebugInfo] = useState<any>(null);\n\n  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(\"/auth/login\");\n    }\n  }, [status, router]);\n\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      fetchAdLimits();\n    }\n  }, [status]);\n\n  const fetchAdLimits = async () => {\n    try {\n      const response = await fetch(\"/api/ads/limits\");\n      const data = await response.json();\n\n      if (data.success) {\n        setAdLimits(data.data);\n        // تحديد نوع الإعلان الافتراضي بناءً على المتاح\n        if (data.data.canCreateFreeAd) {\n          setSelectedAdType(\"free\");\n        } else if (data.data.canCreatePaidAd) {\n          setSelectedAdType(\"paid\");\n        } else if (data.data.canCreatePromotedAd) {\n          setSelectedAdType(\"promoted\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching ad limits:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // التحقق من صحة النموذج\n  const validateForm = () => {\n    const newErrors: string[] = [];\n\n    if (!formData.title.trim()) newErrors.push(\"عنوان الإعلان مطلوب\");\n    if (!formData.description.trim()) newErrors.push(\"وصف الإعلان مطلوب\");\n    if (!formData.price || parseFloat(formData.price) < 0)\n      newErrors.push(\"السعر مطلوب ويجب أن يكون رقم موجب\");\n    if (!formData.category) newErrors.push(\"الفئة مطلوبة\");\n    if (!formData.city) newErrors.push(\"المدينة مطلوبة\");\n\n    // التحقق من حدود الإعلانات\n    if (adLimits) {\n      if (selectedAdType === \"free\" && !adLimits.canCreateFreeAd) {\n        newErrors.push(\"لا يمكنك إنشاء إعلانات مجانية إضافية\");\n      }\n      if (selectedAdType === \"paid\" && !adLimits.canCreatePaidAd) {\n        newErrors.push(\"لا يمكنك إنشاء إعلانات مدفوعة إضافية\");\n      }\n      if (selectedAdType === \"promoted\" && !adLimits.canCreatePromotedAd) {\n        newErrors.push(\"لا يمكنك إنشاء إعلانات مميزة إضافية\");\n      }\n    }\n\n    setErrors(newErrors);\n    return newErrors.length === 0;\n  };\n\n  // التحقق من إمكانية تفعيل الزر\n  const canSubmit = () => {\n    const hasRequiredFields =\n      formData.title.trim() &&\n      formData.description.trim() &&\n      formData.price &&\n      parseFloat(formData.price) >= 0 &&\n      formData.category &&\n      formData.city;\n\n    const hasAdLimits = adLimits !== null;\n\n    const canCreateSelectedType =\n      adLimits &&\n      ((selectedAdType === \"free\" && adLimits.canCreateFreeAd) ||\n        (selectedAdType === \"paid\" && adLimits.canCreatePaidAd) ||\n        (selectedAdType === \"promoted\" && adLimits.canCreatePromotedAd));\n\n    return (\n      hasRequiredFields && hasAdLimits && canCreateSelectedType && !submitting\n    );\n  };\n\n  // تحديث معلومات التشخيص عند تغيير البيانات (بدون infinite loop)\n  useEffect(() => {\n    const hasRequiredFields =\n      formData.title.trim() &&\n      formData.description.trim() &&\n      formData.price &&\n      parseFloat(formData.price) >= 0 &&\n      formData.category &&\n      formData.city;\n\n    const hasAdLimits = adLimits !== null;\n\n    const canCreateSelectedType =\n      adLimits &&\n      ((selectedAdType === \"free\" && adLimits.canCreateFreeAd) ||\n        (selectedAdType === \"paid\" && adLimits.canCreatePaidAd) ||\n        (selectedAdType === \"promoted\" && adLimits.canCreatePromotedAd));\n\n    // تحديث معلومات التشخيص فقط عند الحاجة\n    setDebugInfo((prev) => {\n      const newDebugInfo = {\n        hasRequiredFields,\n        hasAdLimits,\n        canCreateSelectedType,\n        selectedAdType,\n        adLimits: adLimits\n          ? {\n              canCreateFreeAd: adLimits.canCreateFreeAd,\n              canCreatePaidAd: adLimits.canCreatePaidAd,\n              canCreatePromotedAd: adLimits.canCreatePromotedAd,\n            }\n          : null,\n        formData: {\n          title: !!formData.title.trim(),\n          description: !!formData.description.trim(),\n          price: !!formData.price && parseFloat(formData.price) >= 0,\n          category: !!formData.category,\n          city: !!formData.city,\n        },\n      };\n\n      // تحديث فقط إذا تغيرت البيانات\n      if (JSON.stringify(prev) !== JSON.stringify(newDebugInfo)) {\n        return newDebugInfo;\n      }\n      return prev;\n    });\n  }, [\n    formData.title,\n    formData.description,\n    formData.price,\n    formData.category,\n    formData.city,\n    adLimits,\n    selectedAdType,\n  ]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (submitting) return;\n\n    // التحقق من صحة النموذج\n    if (!validateForm()) {\n      return;\n    }\n\n    setSubmitting(true);\n    setErrors([]);\n\n    try {\n      const response = await fetch(\"/api/ads/create\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          ...formData,\n          price: parseFloat(formData.price) || 0,\n          adType: selectedAdType,\n          imageUrls: imageUrls,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        router.push(`/ads/${data.data.id}`);\n      } else {\n        setErrors([data.error || \"حدث خطأ في إنشاء الإعلان\"]);\n      }\n    } catch (error) {\n      console.error(\"Error creating ad:\", error);\n      setErrors([\"حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.\"]);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return \"غير محدد\";\n    return new Date(dateString).toLocaleDateString(\"ar-SY\");\n  };\n\n  if (status === \"loading\" || loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/3 mb-6\"></div>\n            <div className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"space-y-4\">\n                {[...Array(6)].map((_, i) => (\n                  <div key={i} className=\"h-4 bg-gray-200 rounded\"></div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  if (status === \"unauthenticated\") {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-dark-800 mb-2\">\n            إنشاء إعلان جديد\n          </h1>\n          <p className=\"text-gray-600\">أضف إعلانك واصل إلى آلاف المشترين</p>\n        </div>\n\n        {/* عرض حدود الإعلانات */}\n        {adLimits && (\n          <div className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8\">\n            <h2 className=\"text-lg font-semibold text-dark-800 mb-4\">\n              حدود الإعلانات المتاحة\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {/* الإعلانات المجانية */}\n              <div\n                className={`p-4 rounded-lg border-2 ${\n                  adLimits.canCreateFreeAd\n                    ? \"border-green-200 bg-green-50\"\n                    : \"border-gray-200 bg-gray-50\"\n                }`}\n              >\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h3 className=\"font-medium text-dark-700\">إعلانات مجانية</h3>\n                  <div\n                    className={`w-3 h-3 rounded-full ${\n                      adLimits.canCreateFreeAd ? \"bg-green-500\" : \"bg-gray-400\"\n                    }`}\n                  ></div>\n                </div>\n                <p className=\"text-sm text-gray-600 mb-2\">\n                  {adLimits.freeAds.available} من {adLimits.freeAds.total} متاح\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  ينتهي في: {formatDate(adLimits.freeAds.expiresAt)}\n                </p>\n              </div>\n\n              {/* الإعلانات المدفوعة */}\n              <div\n                className={`p-4 rounded-lg border-2 ${\n                  adLimits.canCreatePaidAd\n                    ? \"border-blue-200 bg-blue-50\"\n                    : \"border-gray-200 bg-gray-50\"\n                }`}\n              >\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h3 className=\"font-medium text-dark-700\">إعلانات مدفوعة</h3>\n                  <div\n                    className={`w-3 h-3 rounded-full ${\n                      adLimits.canCreatePaidAd ? \"bg-blue-500\" : \"bg-gray-400\"\n                    }`}\n                  ></div>\n                </div>\n                <p className=\"text-sm text-gray-600 mb-2\">\n                  {adLimits.paidAds.available} من {adLimits.paidAds.total} متاح\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  ينتهي في: {formatDate(adLimits.paidAds.expiresAt)}\n                </p>\n              </div>\n\n              {/* الإعلانات المميزة */}\n              <div\n                className={`p-4 rounded-lg border-2 ${\n                  adLimits.canCreatePromotedAd\n                    ? \"border-yellow-200 bg-yellow-50\"\n                    : \"border-gray-200 bg-gray-50\"\n                }`}\n              >\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h3 className=\"font-medium text-dark-700\">إعلانات مميزة</h3>\n                  <div\n                    className={`w-3 h-3 rounded-full ${\n                      adLimits.canCreatePromotedAd\n                        ? \"bg-yellow-500\"\n                        : \"bg-gray-400\"\n                    }`}\n                  ></div>\n                </div>\n                <p className=\"text-sm text-gray-600 mb-2\">\n                  {adLimits.promotedAds.available} من{\" \"}\n                  {adLimits.promotedAds.total} متاح\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  ينتهي في: {formatDate(adLimits.promotedAds.expiresAt)}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* اختيار نوع الإعلان */}\n        <div className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8\">\n          <h2 className=\"text-lg font-semibold text-dark-800 mb-4\">\n            اختر نوع الإعلان\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {/* إعلان مجاني */}\n            <button\n              type=\"button\"\n              onClick={() => setSelectedAdType(\"free\")}\n              disabled={!adLimits?.canCreateFreeAd}\n              className={`p-4 rounded-lg border-2 text-right transition-colors ${\n                selectedAdType === \"free\"\n                  ? \"border-green-500 bg-green-50\"\n                  : \"border-gray-200 hover:border-green-300\"\n              } ${\n                !adLimits?.canCreateFreeAd\n                  ? \"opacity-50 cursor-not-allowed\"\n                  : \"\"\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <h3 className=\"font-medium text-dark-800\">إعلان مجاني</h3>\n                <ClockIcon className=\"h-5 w-5 text-green-500\" />\n              </div>\n              <p className=\"text-sm text-gray-600 mb-2\">مدة العرض: 30 يوم</p>\n              <p className=\"text-xs text-gray-500\">مجاني تماماً</p>\n            </button>\n\n            {/* إعلان مدفوع */}\n            <button\n              type=\"button\"\n              onClick={() => setSelectedAdType(\"paid\")}\n              disabled={!adLimits?.canCreatePaidAd}\n              className={`p-4 rounded-lg border-2 text-right transition-colors ${\n                selectedAdType === \"paid\"\n                  ? \"border-blue-500 bg-blue-50\"\n                  : \"border-gray-200 hover:border-blue-300\"\n              } ${\n                !adLimits?.canCreatePaidAd\n                  ? \"opacity-50 cursor-not-allowed\"\n                  : \"\"\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <h3 className=\"font-medium text-dark-800\">إعلان مدفوع</h3>\n                <PlusIcon className=\"h-5 w-5 text-blue-500\" />\n              </div>\n              <p className=\"text-sm text-gray-600 mb-2\">\n                مدة العرض: حتى 60 يوم\n              </p>\n              <p className=\"text-xs text-gray-500\">ميزات إضافية</p>\n            </button>\n\n            {/* إعلان مميز */}\n            <button\n              type=\"button\"\n              onClick={() => setSelectedAdType(\"promoted\")}\n              disabled={!adLimits?.canCreatePromotedAd}\n              className={`p-4 rounded-lg border-2 text-right transition-colors ${\n                selectedAdType === \"promoted\"\n                  ? \"border-yellow-500 bg-yellow-50\"\n                  : \"border-gray-200 hover:border-yellow-300\"\n              } ${\n                !adLimits?.canCreatePromotedAd\n                  ? \"opacity-50 cursor-not-allowed\"\n                  : \"\"\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <h3 className=\"font-medium text-dark-800\">إعلان مميز</h3>\n                <StarIcon className=\"h-5 w-5 text-yellow-500\" />\n              </div>\n              <p className=\"text-sm text-gray-600 mb-2\">ظهور في المقدمة</p>\n              <p className=\"text-xs text-gray-500\">أولوية عالية</p>\n            </button>\n          </div>\n        </div>\n\n        {/* عرض الأخطاء */}\n        {errors.length > 0 && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n            <h3 className=\"text-red-800 font-medium mb-2\">\n              يرجى تصحيح الأخطاء التالية:\n            </h3>\n            <ul className=\"text-red-700 text-sm space-y-1\">\n              {errors.map((error, index) => (\n                <li key={index} className=\"flex items-start\">\n                  <span className=\"text-red-500 ml-2\">•</span>\n                  {error}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        {/* نموذج الإعلان */}\n        <form\n          onSubmit={handleSubmit}\n          className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-6\"\n        >\n          <h2 className=\"text-lg font-semibold text-dark-800 mb-6\">\n            تفاصيل الإعلان\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* العنوان */}\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                عنوان الإعلان *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.title}\n                onChange={(e) =>\n                  setFormData((prev) => ({ ...prev, title: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"أدخل عنوان جذاب للإعلان\"\n                required\n                maxLength={100}\n              />\n            </div>\n\n            {/* الوصف */}\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                وصف الإعلان *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) =>\n                  setFormData((prev) => ({\n                    ...prev,\n                    description: e.target.value,\n                  }))\n                }\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"اكتب وصفاً مفصلاً للمنتج أو الخدمة\"\n                required\n                maxLength={1000}\n              />\n            </div>\n\n            {/* السعر */}\n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                السعر (ليرة سورية) *\n              </label>\n              <input\n                type=\"number\"\n                value={formData.price}\n                onChange={(e) =>\n                  setFormData((prev) => ({ ...prev, price: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"0\"\n                min=\"0\"\n                required\n              />\n            </div>\n\n            {/* الفئة */}\n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                الفئة *\n              </label>\n              <select\n                value={formData.category}\n                onChange={(e) =>\n                  setFormData((prev) => ({ ...prev, category: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                required\n              >\n                <option value=\"\">اختر الفئة</option>\n                <option value=\"عقارات\">عقارات</option>\n                <option value=\"سيارات\">سيارات</option>\n                <option value=\"إلكترونيات\">إلكترونيات</option>\n                <option value=\"أثاث\">أثاث ومنزل</option>\n                <option value=\"ملابس\">ملابس وأزياء</option>\n                <option value=\"خدمات\">خدمات</option>\n                <option value=\"أخرى\">أخرى</option>\n              </select>\n            </div>\n\n            {/* الحالة */}\n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                الحالة *\n              </label>\n              <select\n                value={formData.condition}\n                onChange={(e) =>\n                  setFormData((prev) => ({\n                    ...prev,\n                    condition: e.target.value,\n                  }))\n                }\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                required\n              >\n                <option value=\"جديد\">جديد</option>\n                <option value=\"مستعمل\">مستعمل</option>\n              </select>\n            </div>\n\n            {/* المدينة */}\n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                المدينة *\n              </label>\n              <select\n                value={formData.city}\n                onChange={(e) =>\n                  setFormData((prev) => ({ ...prev, city: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                required\n              >\n                <option value=\"\">اختر المدينة</option>\n                <option value=\"دمشق\">دمشق</option>\n                <option value=\"حلب\">حلب</option>\n                <option value=\"حمص\">حمص</option>\n                <option value=\"حماة\">حماة</option>\n                <option value=\"اللاذقية\">اللاذقية</option>\n                <option value=\"طرطوس\">طرطوس</option>\n                <option value=\"درعا\">درعا</option>\n                <option value=\"السويداء\">السويداء</option>\n                <option value=\"القنيطرة\">القنيطرة</option>\n                <option value=\"دير الزور\">دير الزور</option>\n                <option value=\"الرقة\">الرقة</option>\n                <option value=\"الحسكة\">الحسكة</option>\n                <option value=\"إدلب\">إدلب</option>\n                <option value=\"ريف دمشق\">ريف دمشق</option>\n              </select>\n            </div>\n\n            {/* المنطقة */}\n            <div>\n              <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                المنطقة\n              </label>\n              <input\n                type=\"text\"\n                value={formData.region}\n                onChange={(e) =>\n                  setFormData((prev) => ({ ...prev, region: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"اسم المنطقة أو الحي\"\n              />\n            </div>\n\n            {/* مدة الإعلان (للإعلانات المدفوعة والمميزة) */}\n            {(selectedAdType === \"paid\" || selectedAdType === \"promoted\") && (\n              <div>\n                <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n                  مدة الإعلان (بالأيام)\n                </label>\n                <select\n                  value={formData.duration}\n                  onChange={(e) =>\n                    setFormData((prev) => ({\n                      ...prev,\n                      duration: parseInt(e.target.value),\n                    }))\n                  }\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                >\n                  <option value={7}>7 أيام</option>\n                  <option value={15}>15 يوم</option>\n                  <option value={30}>30 يوم</option>\n                  <option value={60}>60 يوم</option>\n                  {selectedAdType === \"paid\" && (\n                    <option value={90}>90 يوم</option>\n                  )}\n                </select>\n              </div>\n            )}\n          </div>\n\n          {/* تفاصيل العنوان */}\n          <div className=\"mt-6\">\n            <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n              تفاصيل العنوان\n            </label>\n            <textarea\n              value={formData.addressDetail}\n              onChange={(e) =>\n                setFormData((prev) => ({\n                  ...prev,\n                  addressDetail: e.target.value,\n                }))\n              }\n              rows={2}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              placeholder=\"تفاصيل إضافية عن الموقع (اختياري)\"\n            />\n          </div>\n\n          {/* رفع الصور */}\n          <div className=\"mt-6\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <label className=\"block text-sm font-medium text-dark-700\">\n                صور الإعلان\n              </label>\n              <button\n                type=\"button\"\n                onClick={() => setUseSimpleUpload(!useSimpleUpload)}\n                className=\"text-xs text-primary-500 hover:text-primary-600\"\n              >\n                {useSimpleUpload ? \"استخدام Cloudinary\" : \"رفع بسيط\"}\n              </button>\n            </div>\n\n            {useSimpleUpload ? (\n              <SimpleImageUpload\n                images={imageUrls}\n                onImagesChange={setImageUrls}\n                maxImages={5}\n              />\n            ) : (\n              <ImageUpload\n                value={imageUrls}\n                onChange={setImageUrls}\n                maxImages={5}\n                disabled={submitting}\n              />\n            )}\n          </div>\n\n          {/* معلومات نوع الإعلان المحدد */}\n          <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-start\">\n              <InformationCircleIcon className=\"h-5 w-5 text-blue-500 mt-0.5 ml-2\" />\n              <div>\n                <h4 className=\"font-medium text-dark-800 mb-1\">\n                  {selectedAdType === \"free\" && \"إعلان مجاني\"}\n                  {selectedAdType === \"paid\" && \"إعلان مدفوع\"}\n                  {selectedAdType === \"promoted\" && \"إعلان مميز\"}\n                </h4>\n                <p className=\"text-sm text-gray-600\">\n                  {selectedAdType === \"free\" &&\n                    \"سيظهر إعلانك لمدة 30 يوم مجاناً في القائمة العادية.\"}\n                  {selectedAdType === \"paid\" &&\n                    \"سيظهر إعلانك لمدة أطول مع ميزات إضافية مثل إحصائيات مفصلة.\"}\n                  {selectedAdType === \"promoted\" &&\n                    \"سيظهر إعلانك في المقدمة ويحصل على أولوية عالية في نتائج البحث.\"}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* أزرار الإجراءات */}\n          <div className=\"mt-8 flex space-x-4 space-x-reverse\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              disabled={!canSubmit()}\n              className=\"flex-1 px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {submitting ? \"جاري النشر...\" : \"نشر الإعلان\"}\n            </button>\n          </div>\n\n          {/* معلومات التشخيص (للمطور فقط) */}\n          {process.env.NODE_ENV === \"development\" && debugInfo && (\n            <div className=\"mt-6 p-4 bg-gray-100 rounded-lg text-xs\">\n              <h4 className=\"font-bold mb-2\">معلومات التشخيص:</h4>\n              <div className=\"space-y-1\">\n                <div>\n                  الحقول المطلوبة: {debugInfo.hasRequiredFields ? \"✅\" : \"❌\"}\n                </div>\n                <div>\n                  حدود الإعلانات محملة: {debugInfo.hasAdLimits ? \"✅\" : \"❌\"}\n                </div>\n                <div>\n                  يمكن إنشاء النوع المحدد:{\" \"}\n                  {debugInfo.canCreateSelectedType ? \"✅\" : \"❌\"}\n                </div>\n                <div>النوع المحدد: {debugInfo.selectedAdType}</div>\n                <div className=\"mt-2\">\n                  <strong>تفاصيل الحقول:</strong>\n                  <ul className=\"mr-4\">\n                    <li>العنوان: {debugInfo.formData?.title ? \"✅\" : \"❌\"}</li>\n                    <li>\n                      الوصف: {debugInfo.formData?.description ? \"✅\" : \"❌\"}\n                    </li>\n                    <li>السعر: {debugInfo.formData?.price ? \"✅\" : \"❌\"}</li>\n                    <li>الفئة: {debugInfo.formData?.category ? \"✅\" : \"❌\"}</li>\n                    <li>المدينة: {debugInfo.formData?.city ? \"✅\" : \"❌\"}</li>\n                  </ul>\n                </div>\n                {debugInfo.adLimits && (\n                  <div className=\"mt-2\">\n                    <strong>حدود الإعلانات:</strong>\n                    <ul className=\"mr-4\">\n                      <li>\n                        مجاني:{\" \"}\n                        {debugInfo.adLimits.canCreateFreeAd ? \"✅\" : \"❌\"}\n                      </li>\n                      <li>\n                        مدفوع:{\" \"}\n                        {debugInfo.adLimits.canCreatePaidAd ? \"✅\" : \"❌\"}\n                      </li>\n                      <li>\n                        مميز:{\" \"}\n                        {debugInfo.adLimits.canCreatePromotedAd ? \"✅\" : \"❌\"}\n                      </li>\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </form>\n\n        {/* رسالة توضيحية للمستخدم */}\n        {!canSubmit() && (\n          <div className=\"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <h4 className=\"font-medium text-yellow-800 mb-2\">\n              لتفعيل زر النشر:\n            </h4>\n            <ul className=\"text-yellow-700 text-sm space-y-1\">\n              {!formData.title.trim() && <li>• أدخل عنوان الإعلان</li>}\n              {!formData.description.trim() && <li>• أدخل وصف الإعلان</li>}\n              {(!formData.price || parseFloat(formData.price) < 0) && (\n                <li>• أدخل سعر صحيح</li>\n              )}\n              {!formData.category && <li>• اختر الفئة</li>}\n              {!formData.city && <li>• اختر المدينة</li>}\n              {!adLimits && <li>• جاري تحميل حدود الإعلانات...</li>}\n              {adLimits &&\n                selectedAdType === \"free\" &&\n                !adLimits.canCreateFreeAd && (\n                  <li>• لا يمكنك إنشاء إعلانات مجانية إضافية</li>\n                )}\n              {adLimits &&\n                selectedAdType === \"paid\" &&\n                !adLimits.canCreatePaidAd && (\n                  <li>• لا يمكنك إنشاء إعلانات مدفوعة إضافية</li>\n                )}\n              {adLimits &&\n                selectedAdType === \"promoted\" &&\n                !adLimits.canCreatePromotedAd && (\n                  <li>• لا يمكنك إنشاء إعلانات مميزة إضافية</li>\n                )}\n            </ul>\n          </div>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAyCe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEjD;IACF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,eAAe;QACf,UAAU;IACZ;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEhD,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,iBAAiB;YAC9B;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI;gBACrB,+CAA+C;gBAC/C,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE;oBAC7B,kBAAkB;gBACpB,OAAO,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE;oBACpC,kBAAkB;gBACpB,OAAO,IAAI,KAAK,IAAI,CAAC,mBAAmB,EAAE;oBACxC,kBAAkB;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,eAAe;QACnB,MAAM,YAAsB,EAAE;QAE9B,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,IAAI,CAAC;QAC3C,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,IAAI,CAAC;QACjD,IAAI,CAAC,SAAS,KAAK,IAAI,WAAW,SAAS,KAAK,IAAI,GAClD,UAAU,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,IAAI,CAAC;QACvC,IAAI,CAAC,SAAS,IAAI,EAAE,UAAU,IAAI,CAAC;QAEnC,2BAA2B;QAC3B,IAAI,UAAU;YACZ,IAAI,mBAAmB,UAAU,CAAC,SAAS,eAAe,EAAE;gBAC1D,UAAU,IAAI,CAAC;YACjB;YACA,IAAI,mBAAmB,UAAU,CAAC,SAAS,eAAe,EAAE;gBAC1D,UAAU,IAAI,CAAC;YACjB;YACA,IAAI,mBAAmB,cAAc,CAAC,SAAS,mBAAmB,EAAE;gBAClE,UAAU,IAAI,CAAC;YACjB;QACF;QAEA,UAAU;QACV,OAAO,UAAU,MAAM,KAAK;IAC9B;IAEA,+BAA+B;IAC/B,MAAM,YAAY;QAChB,MAAM,oBACJ,SAAS,KAAK,CAAC,IAAI,MACnB,SAAS,WAAW,CAAC,IAAI,MACzB,SAAS,KAAK,IACd,WAAW,SAAS,KAAK,KAAK,KAC9B,SAAS,QAAQ,IACjB,SAAS,IAAI;QAEf,MAAM,cAAc,aAAa;QAEjC,MAAM,wBACJ,YACA,CAAC,AAAC,mBAAmB,UAAU,SAAS,eAAe,IACpD,mBAAmB,UAAU,SAAS,eAAe,IACrD,mBAAmB,cAAc,SAAS,mBAAmB,AAAC;QAEnE,OACE,qBAAqB,eAAe,yBAAyB,CAAC;IAElE;IAEA,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBACJ,SAAS,KAAK,CAAC,IAAI,MACnB,SAAS,WAAW,CAAC,IAAI,MACzB,SAAS,KAAK,IACd,WAAW,SAAS,KAAK,KAAK,KAC9B,SAAS,QAAQ,IACjB,SAAS,IAAI;QAEf,MAAM,cAAc,aAAa;QAEjC,MAAM,wBACJ,YACA,CAAC,AAAC,mBAAmB,UAAU,SAAS,eAAe,IACpD,mBAAmB,UAAU,SAAS,eAAe,IACrD,mBAAmB,cAAc,SAAS,mBAAmB,AAAC;QAEnE,uCAAuC;QACvC,aAAa,CAAC;YACZ,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA,UAAU,WACN;oBACE,iBAAiB,SAAS,eAAe;oBACzC,iBAAiB,SAAS,eAAe;oBACzC,qBAAqB,SAAS,mBAAmB;gBACnD,IACA;gBACJ,UAAU;oBACR,OAAO,CAAC,CAAC,SAAS,KAAK,CAAC,IAAI;oBAC5B,aAAa,CAAC,CAAC,SAAS,WAAW,CAAC,IAAI;oBACxC,OAAO,CAAC,CAAC,SAAS,KAAK,IAAI,WAAW,SAAS,KAAK,KAAK;oBACzD,UAAU,CAAC,CAAC,SAAS,QAAQ;oBAC7B,MAAM,CAAC,CAAC,SAAS,IAAI;gBACvB;YACF;YAEA,+BAA+B;YAC/B,IAAI,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,eAAe;gBACzD,OAAO;YACT;YACA,OAAO;QACT;IACF,GAAG;QACD,SAAS,KAAK;QACd,SAAS,WAAW;QACpB,SAAS,KAAK;QACd,SAAS,QAAQ;QACjB,SAAS,IAAI;QACb;QACA;KACD;IAED,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,YAAY;QAEhB,wBAAwB;QACxB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,cAAc;QACd,UAAU,EAAE;QAEZ,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,QAAQ;oBACX,OAAO,WAAW,SAAS,KAAK,KAAK;oBACrC,QAAQ;oBACR,WAAW;gBACb;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,OAAO,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;YACpC,OAAO;gBACL,UAAU;oBAAC,KAAK,KAAK,IAAI;iBAA2B;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,UAAU;gBAAC;aAA8C;QAC3D,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC;IACjD;IAEA,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMpB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAI9B,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,WAAW,CAAC,wBAAwB,EAClC,SAAS,eAAe,GACpB,iCACA,8BACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDACC,WAAW,CAAC,qBAAqB,EAC/B,SAAS,eAAe,GAAG,iBAAiB,eAC5C;;;;;;;;;;;;0DAGN,8OAAC;gDAAE,WAAU;;oDACV,SAAS,OAAO,CAAC,SAAS;oDAAC;oDAAK,SAAS,OAAO,CAAC,KAAK;oDAAC;;;;;;;0DAE1D,8OAAC;gDAAE,WAAU;;oDAAwB;oDACxB,WAAW,SAAS,OAAO,CAAC,SAAS;;;;;;;;;;;;;kDAKpD,8OAAC;wCACC,WAAW,CAAC,wBAAwB,EAClC,SAAS,eAAe,GACpB,+BACA,8BACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDACC,WAAW,CAAC,qBAAqB,EAC/B,SAAS,eAAe,GAAG,gBAAgB,eAC3C;;;;;;;;;;;;0DAGN,8OAAC;gDAAE,WAAU;;oDACV,SAAS,OAAO,CAAC,SAAS;oDAAC;oDAAK,SAAS,OAAO,CAAC,KAAK;oDAAC;;;;;;;0DAE1D,8OAAC;gDAAE,WAAU;;oDAAwB;oDACxB,WAAW,SAAS,OAAO,CAAC,SAAS;;;;;;;;;;;;;kDAKpD,8OAAC;wCACC,WAAW,CAAC,wBAAwB,EAClC,SAAS,mBAAmB,GACxB,mCACA,8BACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDACC,WAAW,CAAC,qBAAqB,EAC/B,SAAS,mBAAmB,GACxB,kBACA,eACJ;;;;;;;;;;;;0DAGN,8OAAC;gDAAE,WAAU;;oDACV,SAAS,WAAW,CAAC,SAAS;oDAAC;oDAAI;oDACnC,SAAS,WAAW,CAAC,KAAK;oDAAC;;;;;;;0DAE9B,8OAAC;gDAAE,WAAU;;oDAAwB;oDACxB,WAAW,SAAS,WAAW,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,UAAU,CAAC,UAAU;wCACrB,WAAW,CAAC,qDAAqD,EAC/D,mBAAmB,SACf,iCACA,yCACL,CAAC,EACA,CAAC,UAAU,kBACP,kCACA,IACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;;0DAEvB,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,UAAU,CAAC,UAAU;wCACrB,WAAW,CAAC,qDAAqD,EAC/D,mBAAmB,SACf,+BACA,wCACL,CAAC,EACA,CAAC,UAAU,kBACP,kCACA,IACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC,+MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;0DAEtB,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,UAAU,CAAC,UAAU;wCACrB,WAAW,CAAC,qDAAqD,EAC/D,mBAAmB,aACf,mCACA,0CACL,CAAC,EACA,CAAC,UAAU,sBACP,kCACA,IACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC,+MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;0DAEtB,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;oBAM1C,OAAO,MAAM,GAAG,mBACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAG9C,8OAAC;gCAAG,WAAU;0CACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;0DAAoB;;;;;;4CACnC;;uCAFM;;;;;;;;;;;;;;;;kCAUjB,8OAAC;wBACC,UAAU;wBACV,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAE3D,WAAU;gDACV,aAAY;gDACZ,QAAQ;gDACR,WAAW;;;;;;;;;;;;kDAKf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DACrB,GAAG,IAAI;4DACP,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC7B,CAAC;gDAEH,MAAM;gDACN,WAAU;gDACV,aAAY;gDACZ,QAAQ;gDACR,WAAW;;;;;;;;;;;;kDAKf,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAE3D,WAAU;gDACV,aAAY;gDACZ,KAAI;gDACJ,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAE9D,WAAU;gDACV,QAAQ;;kEAER,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;;;;;;;;;;;;;kDAKzB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DACrB,GAAG,IAAI;4DACP,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC3B,CAAC;gDAEH,WAAU;gDACV,QAAQ;;kEAER,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAK3B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAE1D,WAAU;gDACV,QAAQ;;kEAER,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAK7B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAE5D,WAAU;gDACV,aAAY;;;;;;;;;;;;oCAKf,CAAC,mBAAmB,UAAU,mBAAmB,UAAU,mBAC1D,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DACrB,GAAG,IAAI;4DACP,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK;wDACnC,CAAC;gDAEH,WAAU;;kEAEV,8OAAC;wDAAO,OAAO;kEAAG;;;;;;kEAClB,8OAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,8OAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,8OAAC;wDAAO,OAAO;kEAAI;;;;;;oDAClB,mBAAmB,wBAClB,8OAAC;wDAAO,OAAO;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO,SAAS,aAAa;wCAC7B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;oDACrB,GAAG,IAAI;oDACP,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC/B,CAAC;wCAEH,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,mBAAmB,CAAC;gDACnC,WAAU;0DAET,kBAAkB,uBAAuB;;;;;;;;;;;;oCAI7C,gCACC,8OAAC,6IAAA,CAAA,oBAAiB;wCAChB,QAAQ;wCACR,gBAAgB;wCAChB,WAAW;;;;;6DAGb,8OAAC,uIAAA,CAAA,cAAW;wCACV,OAAO;wCACP,UAAU;wCACV,WAAW;wCACX,UAAU;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,yOAAA,CAAA,wBAAqB;4CAAC,WAAU;;;;;;sDACjC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDACX,mBAAmB,UAAU;wDAC7B,mBAAmB,UAAU;wDAC7B,mBAAmB,cAAc;;;;;;;8DAEpC,8OAAC;oDAAE,WAAU;;wDACV,mBAAmB,UAClB;wDACD,mBAAmB,UAClB;wDACD,mBAAmB,cAClB;;;;;;;;;;;;;;;;;;;;;;;;0CAOV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU,CAAC;wCACX,WAAU;kDAET,aAAa,kBAAkB;;;;;;;;;;;;4BAKnC,oDAAyB,iBAAiB,2BACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiB;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAI;oDACe,UAAU,iBAAiB,GAAG,MAAM;;;;;;;0DAExD,8OAAC;;oDAAI;oDACoB,UAAU,WAAW,GAAG,MAAM;;;;;;;0DAEvD,8OAAC;;oDAAI;oDACsB;oDACxB,UAAU,qBAAqB,GAAG,MAAM;;;;;;;0DAE3C,8OAAC;;oDAAI;oDAAe,UAAU,cAAc;;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;;oEAAG;oEAAU,UAAU,QAAQ,EAAE,QAAQ,MAAM;;;;;;;0EAChD,8OAAC;;oEAAG;oEACM,UAAU,QAAQ,EAAE,cAAc,MAAM;;;;;;;0EAElD,8OAAC;;oEAAG;oEAAQ,UAAU,QAAQ,EAAE,QAAQ,MAAM;;;;;;;0EAC9C,8OAAC;;oEAAG;oEAAQ,UAAU,QAAQ,EAAE,WAAW,MAAM;;;;;;;0EACjD,8OAAC;;oEAAG;oEAAU,UAAU,QAAQ,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;;;4CAGlD,UAAU,QAAQ,kBACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;;oEAAG;oEACK;oEACN,UAAU,QAAQ,CAAC,eAAe,GAAG,MAAM;;;;;;;0EAE9C,8OAAC;;oEAAG;oEACK;oEACN,UAAU,QAAQ,CAAC,eAAe,GAAG,MAAM;;;;;;;0EAE9C,8OAAC;;oEAAG;oEACI;oEACL,UAAU,QAAQ,CAAC,mBAAmB,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAW/D,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAG,WAAU;;oCACX,CAAC,SAAS,KAAK,CAAC,IAAI,oBAAM,8OAAC;kDAAG;;;;;;oCAC9B,CAAC,SAAS,WAAW,CAAC,IAAI,oBAAM,8OAAC;kDAAG;;;;;;oCACpC,CAAC,CAAC,SAAS,KAAK,IAAI,WAAW,SAAS,KAAK,IAAI,CAAC,mBACjD,8OAAC;kDAAG;;;;;;oCAEL,CAAC,SAAS,QAAQ,kBAAI,8OAAC;kDAAG;;;;;;oCAC1B,CAAC,SAAS,IAAI,kBAAI,8OAAC;kDAAG;;;;;;oCACtB,CAAC,0BAAY,8OAAC;kDAAG;;;;;;oCACjB,YACC,mBAAmB,UACnB,CAAC,SAAS,eAAe,kBACvB,8OAAC;kDAAG;;;;;;oCAEP,YACC,mBAAmB,UACnB,CAAC,SAAS,eAAe,kBACvB,8OAAC;kDAAG;;;;;;oCAEP,YACC,mBAAmB,cACnB,CAAC,SAAS,mBAAmB,kBAC3B,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}