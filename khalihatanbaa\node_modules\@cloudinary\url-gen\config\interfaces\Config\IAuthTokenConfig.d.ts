/**
 * @name IAuthTokenConfig
 * @summary config
 * @description
 * Defines the configuration for delivering token-based authenticated media assets.</br>
 * <b>Learn more:</b> {@link https://cloudinary.com/documentation/control_access_to_media#delivering_token_based_authenticated_media_assets|Delivering token based authenticated media assets}
 *
 * @private
 *
 * @prop {string} token_name
 * @prop {string} duration
 * @prop {string} start_time
 * @prop {string} expiration
 * @prop {string} ip
 * @prop {string} acl
 * @prop {string} url
 * @prop {string} key
 */
interface IAuthTokenConfig {
    token_name: string;
    duration: string;
    start_time: string;
    expiration: string;
    ip: string;
    acl: string;
    url: string;
    key: string;
}
export default IAuthTokenConfig;
