import { Transformation } from "../transformation/Transformation.js";
import { IErrorObject } from "./models/IErrorObject.js";
import { ITransformationModel } from "./models/ITransformationModel.js";
/**
 * Return array of action instances represented by given action models.
 * @param transformationModel
 */
declare function fromJson(transformationModel: ITransformationModel): Transformation | IErrorObject;
export { fromJson };
