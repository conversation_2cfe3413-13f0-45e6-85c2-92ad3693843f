{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\nimport bcrypt from \"bcryptjs\";\nimport { prisma } from \"./prisma\";\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        emailOrPhone: {\n          label: \"البريد الإلكتروني أو رقم الهاتف\",\n          type: \"text\",\n        },\n        password: { label: \"كلمة المرور\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null;\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone },\n            ],\n            isActive: true,\n          },\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        };\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.phone = user.phone;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.phone = token.phone as string;\n        session.user.avatar = token.avatar as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/auth/login\",\n    // signUp: \"/auth/register\", // غير مدعوم في NextAuth\n  },\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBACZ,OAAO;oBACP,MAAM;gBACR;gBACA,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IAEV;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/api/ads/limits/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\nimport { prisma } from \"@/lib/prisma\";\n\n// جلب حدود الإعلانات للمستخدم الحالي\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { id: session.user.id },\n      select: {\n        freeAdsCount: true,\n        freeAdsExpiresAt: true,\n        paidAdsCount: true,\n        paidAdsExpiresAt: true,\n        _count: {\n          select: {\n            ads: {\n              where: { isActive: true },\n            },\n          },\n        },\n      },\n    });\n\n    if (!user) {\n      return NextResponse.json(\n        { success: false, error: \"المستخدم غير موجود\" },\n        { status: 404 }\n      );\n    }\n\n    // حساب الإحصائيات\n    const totalActiveAds = user._count.ads;\n\n    // حساب إجمالي المشاهدات من الإعلانات\n    const adStats = await prisma.ad.aggregate({\n      where: {\n        userId: session.user.id,\n        isActive: true,\n      },\n      _sum: {\n        views: true,\n      },\n    });\n\n    const totalViews = adStats._sum.views || 0;\n    const totalContacts = 0; // يمكن حسابه من الرسائل لاحقاً\n\n    // حساب الإعلانات المتاحة\n    const now = new Date();\n\n    // التحقق من انتهاء صلاحية الإعلانات المجانية\n    const freeAdsUsed = totalActiveAds; // عدد الإعلانات النشطة\n    const freeAdsAvailable =\n      user.freeAdsExpiresAt && user.freeAdsExpiresAt < now\n        ? 0\n        : Math.max(0, user.freeAdsCount - freeAdsUsed);\n\n    // التحقق من انتهاء صلاحية الإعلانات المدفوعة\n    const paidAdsUsed = 0; // لا يوجد نظام دفع حالياً\n    const paidAdsAvailable =\n      user.paidAdsExpiresAt && user.paidAdsExpiresAt < now\n        ? 0\n        : Math.max(0, user.paidAdsCount - paidAdsUsed);\n\n    // الإعلانات المميزة (متاحة دائماً للاختبار)\n    const promotedAdsAvailable = 10; // عدد ثابت للاختبار\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        freeAds: {\n          total: user.freeAdsCount,\n          used: freeAdsUsed,\n          available: freeAdsAvailable,\n          expiresAt: user.freeAdsExpiresAt,\n        },\n        paidAds: {\n          total: user.paidAdsCount,\n          used: paidAdsUsed,\n          available: paidAdsAvailable,\n          expiresAt: user.paidAdsExpiresAt,\n        },\n        promotedAds: {\n          total: 10,\n          used: 0,\n          available: promotedAdsAvailable,\n          expiresAt: null,\n        },\n        totalActiveAds: totalActiveAds,\n        totalViews: totalViews,\n        totalContacts: totalContacts,\n        canCreateFreeAd: freeAdsAvailable > 0,\n        canCreatePaidAd: paidAdsAvailable > 0,\n        canCreatePromotedAd: promotedAdsAvailable > 0,\n      },\n    });\n  } catch (error) {\n    console.error(\"Error fetching ad limits:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في جلب حدود الإعلانات\" },\n      { status: 500 }\n    );\n  }\n}\n\n// تحديث حدود الإعلانات (للإدارة)\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    // التحقق من صلاحيات الإدارة\n    const user = await prisma.user.findUnique({\n      where: { id: session.user.id },\n      select: { role: true },\n    });\n\n    if (user?.role !== \"admin\") {\n      return NextResponse.json(\n        { success: false, error: \"غير مسموح لك بهذا الإجراء\" },\n        { status: 403 }\n      );\n    }\n\n    const body = await request.json();\n    const {\n      userId,\n      freeAdsCount,\n      paidAdsCount,\n      promotedAdsCount,\n      freeAdsDuration,\n      paidAdsDuration,\n      promotedAdsDuration,\n    } = body;\n\n    if (!userId) {\n      return NextResponse.json(\n        { success: false, error: \"معرف المستخدم مطلوب\" },\n        { status: 400 }\n      );\n    }\n\n    const now = new Date();\n    const updateData: any = {};\n\n    if (freeAdsCount !== undefined) {\n      updateData.freeAdsCount = freeAdsCount;\n      if (freeAdsDuration) {\n        updateData.freeAdsExpiresAt = new Date(\n          now.getTime() + freeAdsDuration * 24 * 60 * 60 * 1000\n        );\n      }\n    }\n\n    if (paidAdsCount !== undefined) {\n      updateData.paidAdsCount = paidAdsCount;\n      if (paidAdsDuration) {\n        updateData.paidAdsExpiresAt = new Date(\n          now.getTime() + paidAdsDuration * 24 * 60 * 60 * 1000\n        );\n      }\n    }\n\n    if (promotedAdsCount !== undefined) {\n      updateData.promotedAdsCount = promotedAdsCount;\n      if (promotedAdsDuration) {\n        updateData.promotedAdsExpiresAt = new Date(\n          now.getTime() + promotedAdsDuration * 24 * 60 * 60 * 1000\n        );\n      }\n    }\n\n    const updatedUser = await prisma.user.update({\n      where: { id: userId },\n      data: updateData,\n      select: {\n        id: true,\n        name: true,\n        email: true,\n        freeAdsCount: true,\n        freeAdsUsed: true,\n        freeAdsExpiresAt: true,\n        paidAdsCount: true,\n        paidAdsUsed: true,\n        paidAdsExpiresAt: true,\n        promotedAdsCount: true,\n        promotedAdsUsed: true,\n        promotedAdsExpiresAt: true,\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: updatedUser,\n      message: \"تم تحديث حدود الإعلانات بنجاح\",\n    });\n  } catch (error) {\n    console.error(\"Error updating ad limits:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في تحديث حدود الإعلانات\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,IAAI,CAAC,EAAE;YAAC;YAC7B,QAAQ;gBACN,cAAc;gBACd,kBAAkB;gBAClB,cAAc;gBACd,kBAAkB;gBAClB,QAAQ;oBACN,QAAQ;wBACN,KAAK;4BACH,OAAO;gCAAE,UAAU;4BAAK;wBAC1B;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAqB,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,iBAAiB,KAAK,MAAM,CAAC,GAAG;QAEtC,qCAAqC;QACrC,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,SAAS,CAAC;YACxC,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,UAAU;YACZ;YACA,MAAM;gBACJ,OAAO;YACT;QACF;QAEA,MAAM,aAAa,QAAQ,IAAI,CAAC,KAAK,IAAI;QACzC,MAAM,gBAAgB,GAAG,+BAA+B;QAExD,yBAAyB;QACzB,MAAM,MAAM,IAAI;QAEhB,6CAA6C;QAC7C,MAAM,cAAc,gBAAgB,uBAAuB;QAC3D,MAAM,mBACJ,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,GAAG,MAC7C,IACA,KAAK,GAAG,CAAC,GAAG,KAAK,YAAY,GAAG;QAEtC,6CAA6C;QAC7C,MAAM,cAAc,GAAG,0BAA0B;QACjD,MAAM,mBACJ,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,GAAG,MAC7C,IACA,KAAK,GAAG,CAAC,GAAG,KAAK,YAAY,GAAG;QAEtC,4CAA4C;QAC5C,MAAM,uBAAuB,IAAI,oBAAoB;QAErD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS;oBACP,OAAO,KAAK,YAAY;oBACxB,MAAM;oBACN,WAAW;oBACX,WAAW,KAAK,gBAAgB;gBAClC;gBACA,SAAS;oBACP,OAAO,KAAK,YAAY;oBACxB,MAAM;oBACN,WAAW;oBACX,WAAW,KAAK,gBAAgB;gBAClC;gBACA,aAAa;oBACX,OAAO;oBACP,MAAM;oBACN,WAAW;oBACX,WAAW;gBACb;gBACA,gBAAgB;gBAChB,YAAY;gBACZ,eAAe;gBACf,iBAAiB,mBAAmB;gBACpC,iBAAiB,mBAAmB;gBACpC,qBAAqB,uBAAuB;YAC9C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAgC,GACzD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,IAAI,CAAC,EAAE;YAAC;YAC7B,QAAQ;gBAAE,MAAM;YAAK;QACvB;QAEA,IAAI,MAAM,SAAS,SAAS;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA4B,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,mBAAmB,EACpB,GAAG;QAEJ,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,MAAM,IAAI;QAChB,MAAM,aAAkB,CAAC;QAEzB,IAAI,iBAAiB,WAAW;YAC9B,WAAW,YAAY,GAAG;YAC1B,IAAI,iBAAiB;gBACnB,WAAW,gBAAgB,GAAG,IAAI,KAChC,IAAI,OAAO,KAAK,kBAAkB,KAAK,KAAK,KAAK;YAErD;QACF;QAEA,IAAI,iBAAiB,WAAW;YAC9B,WAAW,YAAY,GAAG;YAC1B,IAAI,iBAAiB;gBACnB,WAAW,gBAAgB,GAAG,IAAI,KAChC,IAAI,OAAO,KAAK,kBAAkB,KAAK,KAAK,KAAK;YAErD;QACF;QAEA,IAAI,qBAAqB,WAAW;YAClC,WAAW,gBAAgB,GAAG;YAC9B,IAAI,qBAAqB;gBACvB,WAAW,oBAAoB,GAAG,IAAI,KACpC,IAAI,OAAO,KAAK,sBAAsB,KAAK,KAAK,KAAK;YAEzD;QACF;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;YACN,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,cAAc;gBACd,aAAa;gBACb,kBAAkB;gBAClB,cAAc;gBACd,aAAa;gBACb,kBAAkB;gBAClB,kBAAkB;gBAClB,iBAAiB;gBACjB,sBAAsB;YACxB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAkC,GAC3D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}