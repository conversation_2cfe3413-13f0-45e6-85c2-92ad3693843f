"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { ImageUpload } from "@/components/ui/ImageUpload";
import { SimpleImageUpload } from "@/components/ui/SimpleImageUpload";
import {
  PlusIcon,
  InformationCircleIcon,
  StarIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

interface AdLimits {
  freeAds: {
    total: number;
    used: number;
    available: number;
    expiresAt?: string;
  };
  paidAds: {
    total: number;
    used: number;
    available: number;
    expiresAt?: string;
  };
  promotedAds: {
    total: number;
    used: number;
    available: number;
    expiresAt?: string;
  };
  totalActiveAds: number;
  canCreateFreeAd: boolean;
  canCreatePaidAd: boolean;
  canCreatePromotedAd: boolean;
}

export default function CreateAdPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [adLimits, setAdLimits] = useState<AdLimits | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedAdType, setSelectedAdType] = useState<
    "free" | "paid" | "promoted"
  >("free");
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    price: "",
    category: "",
    subCategory: "",
    condition: "جديد",
    city: "",
    region: "",
    addressDetail: "",
    duration: 30,
  });
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [useSimpleUpload, setUseSimpleUpload] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login");
    }
  }, [status, router]);

  useEffect(() => {
    if (status === "authenticated") {
      fetchAdLimits();
    }
  }, [status]);

  const fetchAdLimits = async () => {
    try {
      const response = await fetch("/api/ads/limits");
      const data = await response.json();

      if (data.success) {
        setAdLimits(data.data);
        // تحديد نوع الإعلان الافتراضي بناءً على المتاح
        if (data.data.canCreateFreeAd) {
          setSelectedAdType("free");
        } else if (data.data.canCreatePaidAd) {
          setSelectedAdType("paid");
        } else if (data.data.canCreatePromotedAd) {
          setSelectedAdType("promoted");
        }
      }
    } catch (error) {
      console.error("Error fetching ad limits:", error);
    } finally {
      setLoading(false);
    }
  };

  // التحقق من صحة النموذج
  const validateForm = () => {
    const newErrors: string[] = [];

    if (!formData.title.trim()) newErrors.push("عنوان الإعلان مطلوب");
    if (!formData.description.trim()) newErrors.push("وصف الإعلان مطلوب");
    if (!formData.price || parseFloat(formData.price) < 0)
      newErrors.push("السعر مطلوب ويجب أن يكون رقم موجب");
    if (!formData.category) newErrors.push("الفئة مطلوبة");
    if (!formData.city) newErrors.push("المدينة مطلوبة");

    // التحقق من حدود الإعلانات
    if (adLimits) {
      if (selectedAdType === "free" && !adLimits.canCreateFreeAd) {
        newErrors.push("لا يمكنك إنشاء إعلانات مجانية إضافية");
      }
      if (selectedAdType === "paid" && !adLimits.canCreatePaidAd) {
        newErrors.push("لا يمكنك إنشاء إعلانات مدفوعة إضافية");
      }
      if (selectedAdType === "promoted" && !adLimits.canCreatePromotedAd) {
        newErrors.push("لا يمكنك إنشاء إعلانات مميزة إضافية");
      }
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  // التحقق من إمكانية تفعيل الزر
  const canSubmit = () => {
    const hasRequiredFields =
      formData.title.trim() &&
      formData.description.trim() &&
      formData.price &&
      parseFloat(formData.price) >= 0 &&
      formData.category &&
      formData.city;

    const hasAdLimits = adLimits !== null;

    const canCreateSelectedType =
      adLimits &&
      ((selectedAdType === "free" && adLimits.canCreateFreeAd) ||
        (selectedAdType === "paid" && adLimits.canCreatePaidAd) ||
        (selectedAdType === "promoted" && adLimits.canCreatePromotedAd));

    // تحديث معلومات التشخيص
    setDebugInfo({
      hasRequiredFields,
      hasAdLimits,
      canCreateSelectedType,
      selectedAdType,
      adLimits: adLimits
        ? {
            canCreateFreeAd: adLimits.canCreateFreeAd,
            canCreatePaidAd: adLimits.canCreatePaidAd,
            canCreatePromotedAd: adLimits.canCreatePromotedAd,
          }
        : null,
      formData: {
        title: !!formData.title.trim(),
        description: !!formData.description.trim(),
        price: !!formData.price && parseFloat(formData.price) >= 0,
        category: !!formData.category,
        city: !!formData.city,
      },
    });

    return (
      hasRequiredFields && hasAdLimits && canCreateSelectedType && !submitting
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (submitting) return;

    // التحقق من صحة النموذج
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    setErrors([]);

    try {
      const response = await fetch("/api/ads/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price) || 0,
          adType: selectedAdType,
          imageUrls: imageUrls,
        }),
      });

      const data = await response.json();

      if (data.success) {
        router.push(`/ads/${data.data.id}`);
      } else {
        setErrors([data.error || "حدث خطأ في إنشاء الإعلان"]);
      }
    } catch (error) {
      console.error("Error creating ad:", error);
      setErrors(["حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى."]);
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "غير محدد";
    return new Date(dateString).toLocaleDateString("ar-SY");
  };

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <div className="space-y-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (status === "unauthenticated") {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-dark-800 mb-2">
            إنشاء إعلان جديد
          </h1>
          <p className="text-gray-600">أضف إعلانك واصل إلى آلاف المشترين</p>
        </div>

        {/* عرض حدود الإعلانات */}
        {adLimits && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
            <h2 className="text-lg font-semibold text-dark-800 mb-4">
              حدود الإعلانات المتاحة
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* الإعلانات المجانية */}
              <div
                className={`p-4 rounded-lg border-2 ${
                  adLimits.canCreateFreeAd
                    ? "border-green-200 bg-green-50"
                    : "border-gray-200 bg-gray-50"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-dark-700">إعلانات مجانية</h3>
                  <div
                    className={`w-3 h-3 rounded-full ${
                      adLimits.canCreateFreeAd ? "bg-green-500" : "bg-gray-400"
                    }`}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  {adLimits.freeAds.available} من {adLimits.freeAds.total} متاح
                </p>
                <p className="text-xs text-gray-500">
                  ينتهي في: {formatDate(adLimits.freeAds.expiresAt)}
                </p>
              </div>

              {/* الإعلانات المدفوعة */}
              <div
                className={`p-4 rounded-lg border-2 ${
                  adLimits.canCreatePaidAd
                    ? "border-blue-200 bg-blue-50"
                    : "border-gray-200 bg-gray-50"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-dark-700">إعلانات مدفوعة</h3>
                  <div
                    className={`w-3 h-3 rounded-full ${
                      adLimits.canCreatePaidAd ? "bg-blue-500" : "bg-gray-400"
                    }`}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  {adLimits.paidAds.available} من {adLimits.paidAds.total} متاح
                </p>
                <p className="text-xs text-gray-500">
                  ينتهي في: {formatDate(adLimits.paidAds.expiresAt)}
                </p>
              </div>

              {/* الإعلانات المميزة */}
              <div
                className={`p-4 rounded-lg border-2 ${
                  adLimits.canCreatePromotedAd
                    ? "border-yellow-200 bg-yellow-50"
                    : "border-gray-200 bg-gray-50"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-dark-700">إعلانات مميزة</h3>
                  <div
                    className={`w-3 h-3 rounded-full ${
                      adLimits.canCreatePromotedAd
                        ? "bg-yellow-500"
                        : "bg-gray-400"
                    }`}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  {adLimits.promotedAds.available} من{" "}
                  {adLimits.promotedAds.total} متاح
                </p>
                <p className="text-xs text-gray-500">
                  ينتهي في: {formatDate(adLimits.promotedAds.expiresAt)}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* اختيار نوع الإعلان */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-semibold text-dark-800 mb-4">
            اختر نوع الإعلان
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* إعلان مجاني */}
            <button
              type="button"
              onClick={() => setSelectedAdType("free")}
              disabled={!adLimits?.canCreateFreeAd}
              className={`p-4 rounded-lg border-2 text-right transition-colors ${
                selectedAdType === "free"
                  ? "border-green-500 bg-green-50"
                  : "border-gray-200 hover:border-green-300"
              } ${
                !adLimits?.canCreateFreeAd
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-dark-800">إعلان مجاني</h3>
                <ClockIcon className="h-5 w-5 text-green-500" />
              </div>
              <p className="text-sm text-gray-600 mb-2">مدة العرض: 30 يوم</p>
              <p className="text-xs text-gray-500">مجاني تماماً</p>
            </button>

            {/* إعلان مدفوع */}
            <button
              type="button"
              onClick={() => setSelectedAdType("paid")}
              disabled={!adLimits?.canCreatePaidAd}
              className={`p-4 rounded-lg border-2 text-right transition-colors ${
                selectedAdType === "paid"
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-blue-300"
              } ${
                !adLimits?.canCreatePaidAd
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-dark-800">إعلان مدفوع</h3>
                <PlusIcon className="h-5 w-5 text-blue-500" />
              </div>
              <p className="text-sm text-gray-600 mb-2">
                مدة العرض: حتى 60 يوم
              </p>
              <p className="text-xs text-gray-500">ميزات إضافية</p>
            </button>

            {/* إعلان مميز */}
            <button
              type="button"
              onClick={() => setSelectedAdType("promoted")}
              disabled={!adLimits?.canCreatePromotedAd}
              className={`p-4 rounded-lg border-2 text-right transition-colors ${
                selectedAdType === "promoted"
                  ? "border-yellow-500 bg-yellow-50"
                  : "border-gray-200 hover:border-yellow-300"
              } ${
                !adLimits?.canCreatePromotedAd
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-dark-800">إعلان مميز</h3>
                <StarIcon className="h-5 w-5 text-yellow-500" />
              </div>
              <p className="text-sm text-gray-600 mb-2">ظهور في المقدمة</p>
              <p className="text-xs text-gray-500">أولوية عالية</p>
            </button>
          </div>
        </div>

        {/* عرض الأخطاء */}
        {errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h3 className="text-red-800 font-medium mb-2">
              يرجى تصحيح الأخطاء التالية:
            </h3>
            <ul className="text-red-700 text-sm space-y-1">
              {errors.map((error, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-red-500 ml-2">•</span>
                  {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* نموذج الإعلان */}
        <form
          onSubmit={handleSubmit}
          className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6"
        >
          <h2 className="text-lg font-semibold text-dark-800 mb-6">
            تفاصيل الإعلان
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* العنوان */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-dark-700 mb-2">
                عنوان الإعلان *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="أدخل عنوان جذاب للإعلان"
                required
                maxLength={100}
              />
            </div>

            {/* الوصف */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-dark-700 mb-2">
                وصف الإعلان *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="اكتب وصفاً مفصلاً للمنتج أو الخدمة"
                required
                maxLength={1000}
              />
            </div>

            {/* السعر */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                السعر (ليرة سورية) *
              </label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, price: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="0"
                min="0"
                required
              />
            </div>

            {/* الفئة */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                الفئة *
              </label>
              <select
                value={formData.category}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, category: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              >
                <option value="">اختر الفئة</option>
                <option value="عقارات">عقارات</option>
                <option value="سيارات">سيارات</option>
                <option value="إلكترونيات">إلكترونيات</option>
                <option value="أثاث">أثاث ومنزل</option>
                <option value="ملابس">ملابس وأزياء</option>
                <option value="خدمات">خدمات</option>
                <option value="أخرى">أخرى</option>
              </select>
            </div>

            {/* الحالة */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                الحالة *
              </label>
              <select
                value={formData.condition}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    condition: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              >
                <option value="جديد">جديد</option>
                <option value="مستعمل">مستعمل</option>
              </select>
            </div>

            {/* المدينة */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                المدينة *
              </label>
              <select
                value={formData.city}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, city: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              >
                <option value="">اختر المدينة</option>
                <option value="دمشق">دمشق</option>
                <option value="حلب">حلب</option>
                <option value="حمص">حمص</option>
                <option value="حماة">حماة</option>
                <option value="اللاذقية">اللاذقية</option>
                <option value="طرطوس">طرطوس</option>
                <option value="درعا">درعا</option>
                <option value="السويداء">السويداء</option>
                <option value="القنيطرة">القنيطرة</option>
                <option value="دير الزور">دير الزور</option>
                <option value="الرقة">الرقة</option>
                <option value="الحسكة">الحسكة</option>
                <option value="إدلب">إدلب</option>
                <option value="ريف دمشق">ريف دمشق</option>
              </select>
            </div>

            {/* المنطقة */}
            <div>
              <label className="block text-sm font-medium text-dark-700 mb-2">
                المنطقة
              </label>
              <input
                type="text"
                value={formData.region}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, region: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="اسم المنطقة أو الحي"
              />
            </div>

            {/* مدة الإعلان (للإعلانات المدفوعة والمميزة) */}
            {(selectedAdType === "paid" || selectedAdType === "promoted") && (
              <div>
                <label className="block text-sm font-medium text-dark-700 mb-2">
                  مدة الإعلان (بالأيام)
                </label>
                <select
                  value={formData.duration}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      duration: parseInt(e.target.value),
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value={7}>7 أيام</option>
                  <option value={15}>15 يوم</option>
                  <option value={30}>30 يوم</option>
                  <option value={60}>60 يوم</option>
                  {selectedAdType === "paid" && (
                    <option value={90}>90 يوم</option>
                  )}
                </select>
              </div>
            )}
          </div>

          {/* تفاصيل العنوان */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-dark-700 mb-2">
              تفاصيل العنوان
            </label>
            <textarea
              value={formData.addressDetail}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  addressDetail: e.target.value,
                }))
              }
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="تفاصيل إضافية عن الموقع (اختياري)"
            />
          </div>

          {/* رفع الصور */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-dark-700">
                صور الإعلان
              </label>
              <button
                type="button"
                onClick={() => setUseSimpleUpload(!useSimpleUpload)}
                className="text-xs text-primary-500 hover:text-primary-600"
              >
                {useSimpleUpload ? "استخدام Cloudinary" : "رفع بسيط"}
              </button>
            </div>

            {useSimpleUpload ? (
              <SimpleImageUpload
                images={imageUrls}
                onImagesChange={setImageUrls}
                maxImages={5}
              />
            ) : (
              <ImageUpload
                value={imageUrls}
                onChange={setImageUrls}
                maxImages={5}
                disabled={submitting}
              />
            )}
          </div>

          {/* معلومات نوع الإعلان المحدد */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-start">
              <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5 ml-2" />
              <div>
                <h4 className="font-medium text-dark-800 mb-1">
                  {selectedAdType === "free" && "إعلان مجاني"}
                  {selectedAdType === "paid" && "إعلان مدفوع"}
                  {selectedAdType === "promoted" && "إعلان مميز"}
                </h4>
                <p className="text-sm text-gray-600">
                  {selectedAdType === "free" &&
                    "سيظهر إعلانك لمدة 30 يوم مجاناً في القائمة العادية."}
                  {selectedAdType === "paid" &&
                    "سيظهر إعلانك لمدة أطول مع ميزات إضافية مثل إحصائيات مفصلة."}
                  {selectedAdType === "promoted" &&
                    "سيظهر إعلانك في المقدمة ويحصل على أولوية عالية في نتائج البحث."}
                </p>
              </div>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="mt-8 flex space-x-4 space-x-reverse">
            <button
              type="button"
              onClick={() => router.back()}
              className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={!canSubmit()}
              className="flex-1 px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? "جاري النشر..." : "نشر الإعلان"}
            </button>
          </div>

          {/* معلومات التشخيص (للمطور فقط) */}
          {process.env.NODE_ENV === "development" && debugInfo && (
            <div className="mt-6 p-4 bg-gray-100 rounded-lg text-xs">
              <h4 className="font-bold mb-2">معلومات التشخيص:</h4>
              <div className="space-y-1">
                <div>
                  الحقول المطلوبة: {debugInfo.hasRequiredFields ? "✅" : "❌"}
                </div>
                <div>
                  حدود الإعلانات محملة: {debugInfo.hasAdLimits ? "✅" : "❌"}
                </div>
                <div>
                  يمكن إنشاء النوع المحدد:{" "}
                  {debugInfo.canCreateSelectedType ? "✅" : "❌"}
                </div>
                <div>النوع المحدد: {debugInfo.selectedAdType}</div>
                <div className="mt-2">
                  <strong>تفاصيل الحقول:</strong>
                  <ul className="mr-4">
                    <li>العنوان: {debugInfo.formData?.title ? "✅" : "❌"}</li>
                    <li>
                      الوصف: {debugInfo.formData?.description ? "✅" : "❌"}
                    </li>
                    <li>السعر: {debugInfo.formData?.price ? "✅" : "❌"}</li>
                    <li>الفئة: {debugInfo.formData?.category ? "✅" : "❌"}</li>
                    <li>المدينة: {debugInfo.formData?.city ? "✅" : "❌"}</li>
                  </ul>
                </div>
                {debugInfo.adLimits && (
                  <div className="mt-2">
                    <strong>حدود الإعلانات:</strong>
                    <ul className="mr-4">
                      <li>
                        مجاني:{" "}
                        {debugInfo.adLimits.canCreateFreeAd ? "✅" : "❌"}
                      </li>
                      <li>
                        مدفوع:{" "}
                        {debugInfo.adLimits.canCreatePaidAd ? "✅" : "❌"}
                      </li>
                      <li>
                        مميز:{" "}
                        {debugInfo.adLimits.canCreatePromotedAd ? "✅" : "❌"}
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </form>

        {/* رسالة توضيحية للمستخدم */}
        {!canSubmit() && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">
              لتفعيل زر النشر:
            </h4>
            <ul className="text-yellow-700 text-sm space-y-1">
              {!formData.title.trim() && <li>• أدخل عنوان الإعلان</li>}
              {!formData.description.trim() && <li>• أدخل وصف الإعلان</li>}
              {(!formData.price || parseFloat(formData.price) < 0) && (
                <li>• أدخل سعر صحيح</li>
              )}
              {!formData.category && <li>• اختر الفئة</li>}
              {!formData.city && <li>• اختر المدينة</li>}
              {!adLimits && <li>• جاري تحميل حدود الإعلانات...</li>}
              {adLimits &&
                selectedAdType === "free" &&
                !adLimits.canCreateFreeAd && (
                  <li>• لا يمكنك إنشاء إعلانات مجانية إضافية</li>
                )}
              {adLimits &&
                selectedAdType === "paid" &&
                !adLimits.canCreatePaidAd && (
                  <li>• لا يمكنك إنشاء إعلانات مدفوعة إضافية</li>
                )}
              {adLimits &&
                selectedAdType === "promoted" &&
                !adLimits.canCreatePromotedAd && (
                  <li>• لا يمكنك إنشاء إعلانات مميزة إضافية</li>
                )}
            </ul>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
