{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/ads\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الإعلانات\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-dark-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* معلومات الموقع */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4\">\n              <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">خ</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">خَلّيها تنْباع</h3>\n                <p className=\"text-sm text-gray-400\">منصة البيع والشراء</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. \n              نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">فيسبوك</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">تلغرام</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">واتساب</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* روابط سريعة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  من نحن\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  كيف يعمل الموقع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety-tips\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  نصائح الأمان\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  باقات الإعلانات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  اتصل بنا\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* الدعم والمساعدة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">الدعم والمساعدة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  مركز المساعدة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  شروط الاستخدام\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الإبلاغ عن مشكلة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* معلومات الاتصال */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center md:text-right\">\n              <h5 className=\"font-semibold mb-2\">البريد الإلكتروني</h5>\n              <p className=\"text-gray-300\"><EMAIL></p>\n            </div>\n            <div className=\"text-center\">\n              <h5 className=\"font-semibold mb-2\">رقم الهاتف</h5>\n              <p className=\"text-gray-300\" dir=\"ltr\">+963 11 123 4567</p>\n            </div>\n            <div className=\"text-center md:text-left\">\n              <h5 className=\"font-semibold mb-2\">العنوان</h5>\n              <p className=\"text-gray-300\">دمشق، سوريا</p>\n            </div>\n          </div>\n        </div>\n\n        {/* حقوق النشر */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\n          </p>\n          <p className=\"text-gray-500 text-sm mt-2\">\n            تم التطوير بـ ❤️ في سوريا\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAmD;;;;;;;;;;;sDAI1F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;wCAAgB,KAAI;kDAAM;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ui/PlaceholderImage.tsx"], "sourcesContent": ["interface PlaceholderImageProps {\n  width?: number\n  height?: number\n  text?: string\n  className?: string\n}\n\nexport function PlaceholderImage({ \n  width = 300, \n  height = 200, \n  text = 'صورة', \n  className = '' \n}: PlaceholderImageProps) {\n  return (\n    <div \n      className={`bg-gray-200 flex items-center justify-center text-gray-500 ${className}`}\n      style={{ width, height }}\n    >\n      <div className=\"text-center\">\n        <svg \n          className=\"mx-auto h-12 w-12 text-gray-400 mb-2\" \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\" \n            strokeWidth={2} \n            d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" \n          />\n        </svg>\n        <p className=\"text-sm\">{text}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,SAAS,iBAAiB,EAC/B,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,OAAO,MAAM,EACb,YAAY,EAAE,EACQ;IACtB,qBACE,8OAAC;QACC,WAAW,CAAC,2DAA2D,EAAE,WAAW;QACpF,OAAO;YAAE;YAAO;QAAO;kBAEvB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;8BAGN,8OAAC;oBAAE,WAAU;8BAAW;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/types/index.ts"], "sourcesContent": ["// أنواع البيانات الأساسية للتطبيق\n\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  role: \"user\" | \"admin\";\n  avatar?: string;\n  isActive: boolean;\n  freeAdsCount: number;\n  freeAdsExpiresAt?: Date;\n  paidAdsCount: number;\n  paidAdsExpiresAt?: Date;\n  ratingAverage: number;\n  ratingCount: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Ad {\n  id: string;\n  title: string;\n  description: string;\n  price: number;\n  category: string;\n  subCategory?: string;\n  condition: \"جديد\" | \"مستعمل\";\n  city: string;\n  region?: string;\n  addressDetail?: string;\n  imageUrls: string[];\n  specifications?: Record<string, any>;\n  views: number;\n  isActive: boolean;\n  isFreeAd: boolean;\n  isPromoted: boolean;\n  expiresAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  userId: string;\n  user?: User;\n}\n\nexport interface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: Date;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from?: User;\n  to?: User;\n  ad?: Ad;\n}\n\nexport interface Favorite {\n  id: string;\n  userId: string;\n  adId: string;\n  createdAt: Date;\n  user?: User;\n  ad?: Ad;\n}\n\nexport interface Rating {\n  id: string;\n  rating: number;\n  comment?: string;\n  createdAt: Date;\n  userId: string;\n  sellerId: string;\n  giver?: User;\n  receiver?: User;\n}\n\nexport interface AdPackage {\n  id: string;\n  name: string;\n  adsCount: number;\n  price: number;\n  duration: number;\n  isActive: boolean;\n  createdAt: Date;\n  userId: string;\n  user?: User;\n}\n\n// أنواع الفئات\nexport const CATEGORIES = {\n  عقارات: {\n    name: \"عقارات\",\n    icon: \"🏠\",\n    subCategories: [\"شقة\", \"فيلا\", \"أرض\", \"محل تجاري\", \"مكتب\"],\n  },\n  سيارات: {\n    name: \"سيارات\",\n    icon: \"🚗\",\n    subCategories: [\n      \"تويوتا\",\n      \"نيسان\",\n      \"هيونداي\",\n      \"كيا\",\n      \"مرسيدس\",\n      \"BMW\",\n      \"أخرى\",\n    ],\n  },\n  إلكترونيات: {\n    name: \"إلكترونيات\",\n    icon: \"📱\",\n    subCategories: [\"هاتف ذكي\", \"لابتوب\", \"تلفاز\", \"كاميرا\", \"أجهزة منزلية\"],\n  },\n  أثاث: {\n    name: \"أثاث\",\n    icon: \"🪑\",\n    subCategories: [\"غرفة نوم\", \"غرفة جلوس\", \"مطبخ\", \"مكتب\", \"ديكور\"],\n  },\n  ملابس: {\n    name: \"ملابس\",\n    icon: \"👕\",\n    subCategories: [\"رجالي\", \"نسائي\", \"أطفال\", \"أحذية\", \"إكسسوارات\"],\n  },\n  رياضة: {\n    name: \"رياضة\",\n    icon: \"⚽\",\n    subCategories: [\"كرة قدم\", \"كرة سلة\", \"جيم\", \"دراجات\", \"أخرى\"],\n  },\n} as const;\n\nexport type CategoryKey = keyof typeof CATEGORIES;\n\n// أنواع المدن السورية\nexport const SYRIAN_CITIES = [\n  \"دمشق\",\n  \"حلب\",\n  \"حمص\",\n  \"حماة\",\n  \"اللاذقية\",\n  \"طرطوس\",\n  \"درعا\",\n  \"السويداء\",\n  \"القنيطرة\",\n  \"دير الزور\",\n  \"الرقة\",\n  \"الحسكة\",\n  \"إدلب\",\n  \"ريف دمشق\",\n] as const;\n\nexport type SyrianCity = (typeof SYRIAN_CITIES)[number];\n\n// أنواع الاستعلامات\nexport interface SearchFilters {\n  keyword?: string;\n  category?: CategoryKey;\n  subCategory?: string;\n  city?: SyrianCity;\n  minPrice?: number;\n  maxPrice?: number;\n  condition?: \"جديد\" | \"مستعمل\";\n  sortBy?: \"newest\" | \"oldest\" | \"price_low\" | \"price_high\";\n}\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> extends ApiResponse<T[]> {\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AA0F3B,MAAM,aAAa;IACxB,QAAQ;QACN,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAO;YAAQ;YAAO;YAAa;SAAO;IAC5D;IACA,QAAQ;QACN,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,YAAY;QACV,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAU;YAAS;YAAU;SAAe;IAC1E;IACA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAa;YAAQ;YAAQ;SAAQ;IACnE;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAS;YAAS;YAAS;YAAS;SAAY;IAClE;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAW;YAAW;YAAO;YAAU;SAAO;IAChE;AACF;AAKO,MAAM,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/search/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, Suspense } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport Link from 'next/link'\nimport { Header } from '@/components/layout/Header'\nimport { Footer } from '@/components/layout/Footer'\nimport { PlaceholderImage } from '@/components/ui/PlaceholderImage'\nimport { CATEGORIES, SYRIAN_CITIES, Ad } from '@/types'\nimport { \n  MagnifyingGlassIcon, \n  AdjustmentsHorizontalIcon,\n  HeartIcon,\n  MapPinIcon,\n  ClockIcon \n} from '@heroicons/react/24/outline'\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'\n\n// بيانات تجريبية للبحث\nconst sampleSearchResults: Ad[] = [\n  {\n    id: '1',\n    title: 'تويوتا كامري 2018 فل كامل',\n    description: 'سيارة بحالة ممتازة، صيانة دورية، لون أبيض',\n    price: 45000000,\n    category: 'سيارات',\n    subCategory: 'تويوتا',\n    condition: 'مستعمل',\n    city: 'دمشق',\n    region: 'المزة',\n    imageUrls: ['/placeholder-car.jpg'],\n    views: 125,\n    isActive: true,\n    isFreeAd: false,\n    isPromoted: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15'),\n    userId: 'user1'\n  },\n  {\n    id: '2',\n    title: 'شقة 3 غرف في المالكي',\n    description: 'شقة مفروشة بالكامل، إطلالة رائعة، قريبة من الخدمات',\n    price: 120000000,\n    category: 'عقارات',\n    subCategory: 'شقة',\n    condition: 'مستعمل',\n    city: 'دمشق',\n    region: 'المالكي',\n    imageUrls: ['/placeholder-apartment.jpg'],\n    views: 89,\n    isActive: true,\n    isFreeAd: true,\n    isPromoted: false,\n    createdAt: new Date('2024-01-14'),\n    updatedAt: new Date('2024-01-14'),\n    userId: 'user2'\n  }\n]\n\nfunction SearchContent() {\n  const searchParams = useSearchParams()\n  const [ads, setAds] = useState<Ad[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showFilters, setShowFilters] = useState(false)\n  const [favorites, setFavorites] = useState<Set<string>>(new Set())\n  \n  const [filters, setFilters] = useState({\n    keyword: searchParams.get('q') || '',\n    category: searchParams.get('category') || '',\n    subCategory: '',\n    city: '',\n    minPrice: '',\n    maxPrice: '',\n    condition: '',\n    sortBy: 'newest'\n  })\n\n  useEffect(() => {\n    // محاكاة جلب البيانات من API\n    setLoading(true)\n    setTimeout(() => {\n      setAds(sampleSearchResults)\n      setLoading(false)\n    }, 1000)\n  }, [filters])\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('ar-SY').format(price)\n  }\n\n  const formatDate = (date: Date) => {\n    const now = new Date()\n    const diffTime = Math.abs(now.getTime() - date.getTime())\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n    \n    if (diffDays === 1) return 'منذ يوم واحد'\n    if (diffDays < 7) return `منذ ${diffDays} أيام`\n    if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`\n    return `منذ ${Math.ceil(diffDays / 30)} شهر`\n  }\n\n  const toggleFavorite = (adId: string) => {\n    setFavorites(prev => {\n      const newFavorites = new Set(prev)\n      if (newFavorites.has(adId)) {\n        newFavorites.delete(adId)\n      } else {\n        newFavorites.add(adId)\n      }\n      return newFavorites\n    })\n  }\n\n  const handleFilterChange = (key: string, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }))\n  }\n\n  const clearFilters = () => {\n    setFilters({\n      keyword: '',\n      category: '',\n      subCategory: '',\n      city: '',\n      minPrice: '',\n      maxPrice: '',\n      condition: '',\n      sortBy: 'newest'\n    })\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      \n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* شريط البحث والفلاتر */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            {/* شريط البحث */}\n            <div className=\"flex-1 relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={filters.keyword}\n                onChange={(e) => handleFilterChange('keyword', e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </div>\n\n            {/* زر الفلاتر */}\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"lg:w-auto px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center justify-center gap-2\"\n            >\n              <AdjustmentsHorizontalIcon className=\"h-5 w-5\" />\n              <span>فلاتر</span>\n            </button>\n          </div>\n\n          {/* الفلاتر المتقدمة */}\n          {showFilters && (\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">الفئة</label>\n                  <select\n                    value={filters.category}\n                    onChange={(e) => handleFilterChange('category', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  >\n                    <option value=\"\">جميع الفئات</option>\n                    {Object.entries(CATEGORIES).map(([key, category]) => (\n                      <option key={key} value={key}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">المحافظة</label>\n                  <select\n                    value={filters.city}\n                    onChange={(e) => handleFilterChange('city', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  >\n                    <option value=\"\">جميع المحافظات</option>\n                    {SYRIAN_CITIES.map((city) => (\n                      <option key={city} value={city}>\n                        {city}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">الحالة</label>\n                  <select\n                    value={filters.condition}\n                    onChange={(e) => handleFilterChange('condition', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  >\n                    <option value=\"\">جميع الحالات</option>\n                    <option value=\"جديد\">جديد</option>\n                    <option value=\"مستعمل\">مستعمل</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">ترتيب حسب</label>\n                  <select\n                    value={filters.sortBy}\n                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  >\n                    <option value=\"newest\">الأحدث</option>\n                    <option value=\"oldest\">الأقدم</option>\n                    <option value=\"price_low\">السعر: من الأقل للأعلى</option>\n                    <option value=\"price_high\">السعر: من الأعلى للأقل</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">السعر الأدنى (ل.س)</label>\n                  <input\n                    type=\"number\"\n                    placeholder=\"0\"\n                    value={filters.minPrice}\n                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">السعر الأعلى (ل.س)</label>\n                  <input\n                    type=\"number\"\n                    placeholder=\"∞\"\n                    value={filters.maxPrice}\n                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-4 flex justify-end\">\n                <button\n                  onClick={clearFilters}\n                  className=\"px-4 py-2 text-gray-600 hover:text-gray-800\"\n                >\n                  مسح الفلاتر\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* النتائج */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-xl font-semibold text-dark-800\">\n            {loading ? 'جاري البحث...' : `تم العثور على ${ads.length} إعلان`}\n          </h2>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {[...Array(8)].map((_, i) => (\n              <div key={i} className=\"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden animate-pulse\">\n                <div className=\"h-48 bg-gray-200\"></div>\n                <div className=\"p-4 space-y-3\">\n                  <div className=\"h-4 bg-gray-200 rounded\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : ads.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 text-6xl mb-4\">🔍</div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لم يتم العثور على نتائج</h3>\n            <p className=\"text-gray-500\">جرب تعديل كلمات البحث أو الفلاتر</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {ads.map((ad) => (\n              <div\n                key={ad.id}\n                className=\"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300\"\n              >\n                {/* صورة الإعلان */}\n                <div className=\"relative h-48 bg-gray-200\">\n                  <PlaceholderImage \n                    width={300} \n                    height={192} \n                    text={`صورة ${ad.category}`}\n                    className=\"w-full h-full\"\n                  />\n                  \n                  {/* شارة الإعلان المميز */}\n                  {ad.isPromoted && (\n                    <div className=\"absolute top-3 right-3 bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-semibold\">\n                      مميز\n                    </div>\n                  )}\n\n                  {/* زر المفضلة */}\n                  <button\n                    onClick={() => toggleFavorite(ad.id)}\n                    className=\"absolute top-3 left-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors\"\n                  >\n                    {favorites.has(ad.id) ? (\n                      <HeartSolidIcon className=\"h-5 w-5 text-red-500\" />\n                    ) : (\n                      <HeartIcon className=\"h-5 w-5 text-gray-600\" />\n                    )}\n                  </button>\n                </div>\n\n                {/* محتوى الإعلان */}\n                <div className=\"p-4\">\n                  <Link href={`/ads/${ad.id}`}>\n                    <h3 className=\"font-semibold text-dark-800 mb-2 hover:text-primary-500 transition-colors line-clamp-2\">\n                      {ad.title}\n                    </h3>\n                  </Link>\n\n                  <div className=\"flex items-center text-sm text-dark-500 mb-2\">\n                    <MapPinIcon className=\"h-4 w-4 ml-1\" />\n                    {ad.city} - {ad.region}\n                  </div>\n\n                  <div className=\"flex items-center text-sm text-dark-500 mb-3\">\n                    <ClockIcon className=\"h-4 w-4 ml-1\" />\n                    {formatDate(ad.createdAt)}\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-lg font-bold text-primary-500\">\n                      {formatPrice(ad.price)} <span className=\"text-sm\">ل.س</span>\n                    </div>\n                    <div className=\"text-sm text-dark-500\">\n                      {ad.views} مشاهدة\n                    </div>\n                  </div>\n\n                  <div className=\"mt-3 flex items-center justify-between\">\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      ad.condition === 'جديد' \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-blue-100 text-blue-800'\n                    }`}>\n                      {ad.condition}\n                    </span>\n                    \n                    {ad.isFreeAd && (\n                      <span className=\"px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium\">\n                        إعلان مجاني\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Pagination */}\n        {!loading && ads.length > 0 && (\n          <div className=\"mt-12 flex justify-center\">\n            <div className=\"flex items-center space-x-2 space-x-reverse\">\n              <button className=\"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\">\n                السابق\n              </button>\n              <button className=\"px-3 py-2 bg-primary-500 text-white rounded-lg\">\n                1\n              </button>\n              <button className=\"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\">\n                2\n              </button>\n              <button className=\"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\">\n                3\n              </button>\n              <button className=\"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\">\n                التالي\n              </button>\n            </div>\n          </div>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n\nexport default function SearchPage() {\n  return (\n    <Suspense fallback={<div className=\"min-h-screen flex items-center justify-center\">جاري التحميل...</div>}>\n      <SearchContent />\n    </Suspense>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAhBA;;;;;;;;;;;AAkBA,uBAAuB;AACvB,MAAM,sBAA4B;IAChC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAAuB;QACnC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAA6B;QACzC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;CACD;AAED,SAAS;IACP,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAE5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,SAAS,aAAa,GAAG,CAAC,QAAQ;QAClC,UAAU,aAAa,GAAG,CAAC,eAAe;QAC1C,aAAa;QACb,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,WAAW;QACX,WAAW;YACT,OAAO;YACP,WAAW;QACb,GAAG;IACL,GAAG;QAAC;KAAQ;IAEZ,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO;QACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE,SAAS,KAAK,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QACjE,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;IAC9C;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA;YACX,MAAM,eAAe,IAAI,IAAI;YAC7B,IAAI,aAAa,GAAG,CAAC,OAAO;gBAC1B,aAAa,MAAM,CAAC;YACtB,OAAO;gBACL,aAAa,GAAG,CAAC;YACnB;YACA,OAAO;QACT;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAa;QACvC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,SAAS;YACT,UAAU;YACV,aAAa;YACb,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,OAAO;gDACtB,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC7D,WAAU;;;;;;0DAEZ,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;;kDAIjC,8OAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAKT,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,OAAO,QAAQ,QAAQ;wDACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC9D,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,OAAO,OAAO,CAAC,qHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBAC9C,8OAAC;oEAAiB,OAAO;8EACtB,SAAS,IAAI;mEADH;;;;;;;;;;;;;;;;;0DAOnB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,OAAO,QAAQ,IAAI;wDACnB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAC1D,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,qHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;oEAAkB,OAAO;8EACvB;mEADU;;;;;;;;;;;;;;;;;0DAOnB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,OAAO,QAAQ,SAAS;wDACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;;;;;;;;;;;;;0DAI3B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,OAAO,QAAQ,MAAM;wDACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wDAC5D,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAa;;;;;;;;;;;;;;;;;;;;;;;;kDAKjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,QAAQ,QAAQ;wDACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC9D,WAAU;;;;;;;;;;;;0DAGd,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,QAAQ,QAAQ;wDACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC9D,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,UAAU,kBAAkB,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC;;;;;;;;;;;oBAInE,wBACC,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BALT;;;;;;;;;+BAUZ,IAAI,MAAM,KAAK,kBACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B,8OAAC;wBAAI,WAAU;kCACZ,IAAI,GAAG,CAAC,CAAC,mBACR,8OAAC;gCAEC,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,OAAO;gDACP,QAAQ;gDACR,MAAM,CAAC,KAAK,EAAE,GAAG,QAAQ,EAAE;gDAC3B,WAAU;;;;;;4CAIX,GAAG,UAAU,kBACZ,8OAAC;gDAAI,WAAU;0DAAgG;;;;;;0DAMjH,8OAAC;gDACC,SAAS,IAAM,eAAe,GAAG,EAAE;gDACnC,WAAU;0DAET,UAAU,GAAG,CAAC,GAAG,EAAE,kBAClB,8OAAC,+MAAA,CAAA,YAAc;oDAAC,WAAU;;;;;yEAE1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAM3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;0DACzB,cAAA,8OAAC;oDAAG,WAAU;8DACX,GAAG,KAAK;;;;;;;;;;;0DAIb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,mNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDACrB,GAAG,IAAI;oDAAC;oDAAI,GAAG,MAAM;;;;;;;0DAGxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpB,WAAW,GAAG,SAAS;;;;;;;0DAG1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,YAAY,GAAG,KAAK;4DAAE;0EAAC,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;4DACZ,GAAG,KAAK;4DAAC;;;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAC3D,GAAG,SAAS,KAAK,SACb,gCACA,6BACJ;kEACC,GAAG,SAAS;;;;;;oDAGd,GAAG,QAAQ,kBACV,8OAAC;wDAAK,WAAU;kEAA2E;;;;;;;;;;;;;;;;;;;+BArE5F,GAAG,EAAE;;;;;;;;;;oBAiFjB,CAAC,WAAW,IAAI,MAAM,GAAG,mBACxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAA+D;;;;;;8CAGjF,8OAAC;oCAAO,WAAU;8CAAiD;;;;;;8CAGnE,8OAAC;oCAAO,WAAU;8CAA+D;;;;;;8CAGjF,8OAAC;oCAAO,WAAU;8CAA+D;;;;;;8CAGjF,8OAAC;oCAAO,WAAU;8CAA+D;;;;;;;;;;;;;;;;;;;;;;;0BAQzF,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb;AAEe,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;YAAI,WAAU;sBAAgD;;;;;;kBACjF,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}